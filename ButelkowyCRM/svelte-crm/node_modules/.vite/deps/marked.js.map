{"version": 3, "sources": ["../../.pnpm/marked@15.0.8/node_modules/marked/src/defaults.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/rules.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/helpers.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/Tokenizer.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/Lexer.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/Renderer.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/TextRenderer.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/Parser.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/Hooks.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/Instance.ts", "../../.pnpm/marked@15.0.8/node_modules/marked/src/marked.ts"], "sourcesContent": ["/**\n * Gets the original marked default options.\n */\nexport function _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nexport let _defaults = _getDefaults();\nexport function changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null };\nfunction edit(regex, opt = '') {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(other.caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nexport const other = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n    htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/\\|table/g, '') // table not in commonmark\n    .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    lheading: lheadingGfm,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n    .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n    .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n    + '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    emStrongRDelimAst: emStrongRDelimAstGfm,\n    emStrongLDelim: emStrongLDelimGfm,\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nexport const block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nexport const inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n", "import { other } from './rules.ts';\n/**\n * Helpers\n */\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nexport function escape(html, encode) {\n    if (encode) {\n        if (other.escapeTest.test(html)) {\n            return html.replace(other.escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (other.escapeTestNoEncode.test(html)) {\n            return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nexport function unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(other.unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nexport function cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(other.percentDecode, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nexport function splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(other.splitPipe);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells.at(-1)?.trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nexport function findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport { rtrim, splitCells, findClosingBracket, } from './helpers.ts';\nfunction outputLink(cap, link, raw, lexer, rules) {\n    const href = link.href;\n    const title = link.title || null;\n    const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text,\n    };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n    const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(rules.other.beginningSpace);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (this.rules.other.endingHash.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (this.rules.other.blockquoteStart.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n                    .replace(this.rules.other.blockquoteSetextReplace2, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = this.rules.other.listItemRegex(bull);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n                    const hrRegex = this.rules.other.hrRegex(indent);\n                    const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n                    const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n                    const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        let nextLineWithoutTabs;\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n                            nextLineWithoutTabs = nextLine;\n                        }\n                        else {\n                            nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of html block\n                        if (htmlBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(nextLine)) {\n                            break;\n                        }\n                        if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLineWithoutTabs.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (this.rules.other.doubleBlankLine.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = this.rules.other.listIsTask.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            const lastItem = list.items.at(-1);\n            if (lastItem) {\n                lastItem.raw = lastItem.raw.trimEnd();\n                lastItem.text = lastItem.text.trimEnd();\n            }\n            else {\n                // not a list since there were no items\n                return;\n            }\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!this.rules.other.tableDelimiter.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n        const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (this.rules.other.tableAlignRight.test(align)) {\n                item.align.push('right');\n            }\n            else if (this.rules.other.tableAlignCenter.test(align)) {\n                item.align.push('center');\n            }\n            else if (this.rules.other.tableAlignLeft.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: cap[1],\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = this.rules.other.pedanticHrefTitle.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (this.rules.other.startAngleBracket.test(href)) {\n                if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer, this.rules);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer, this.rules);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n            const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n            const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[1];\n                href = 'mailto:' + text;\n            }\n            else {\n                text = cap[1];\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[0];\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = cap[0];\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            const escaped = this.lexer.state.inRawBlock;\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                escaped,\n            };\n        }\n    }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\n/**\n * Block Lexer\n */\nexport class _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            other,\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src.replace(other.carriageReturn, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n        }\n        while (src) {\n            let token;\n            if (this.options.extensions?.block?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.raw.length === 1 && lastToken !== undefined) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    lastToken.raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                const lastToken = tokens.at(-1);\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = cutSrc.length !== src.length;\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match = null;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index)\n                            + '[' + 'a'.repeat(match[0].length - 2) + ']'\n                            + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        let keepPrevChar = false;\n        let prevChar = '';\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            let token;\n            // extensions\n            if (this.options.extensions?.inline?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.type === 'text' && lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { cleanUrl, escape, } from './helpers.ts';\nimport { other } from './rules.ts';\n/**\n * Renderer\n */\nexport class _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(other.notSpaceStart)?.[0];\n        const code = text.replace(other.endingNewline, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens[0]?.type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n                        item.tokens[0].tokens[0].escaped = true;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                        escaped: true,\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${escape(text, true)}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + (escape(title)) + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return escape(text);\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${escape(title)}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens\n            ? this.parser.parseInline(token.tokens)\n            : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n    }\n}\n", "/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer = this.renderer) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nexport class _Hooks {\n    options;\n    block;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n    /**\n     * Provide function to tokenize markdown\n     */\n    provideLexer() {\n        return this.block ? _Lexer.lex : _Lexer.lexInline;\n    }\n    /**\n     * Provide function to parse tokens\n     */\n    provideParser() {\n        return this.block ? _Parser.parse : _Parser.parseInline;\n    }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nexport class Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.parseMarkdown(true);\n    parseInline = this.parseMarkdown(false);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (['options', 'block'].includes(prop)) {\n                        // ignore options and block properties\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    parseMarkdown(blockType) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            const throwError = this.onError(!!opt.silent, !!opt.async);\n            // throw error if an extension set async to true but parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n            }\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n                opt.hooks.block = blockType;\n            }\n            const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n            const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n        return parse;\n    }\n    onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport { _getDefaults, changeDefaults, _defaults, } from './defaults.ts';\nconst markedInstance = new Marked();\nexport function marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\n"], "mappings": ";;;;;AAGO,SAAS,eAAe;AAC3B,SAAO;IACH,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,KAAK;IACL,OAAO;IACP,UAAU;IACV,UAAU;IACV,QAAQ;IACR,WAAW;IACX,YAAY;EACpB;AACA;AACU,IAAC,YAAY,aAAY;AAC5B,SAAS,eAAe,aAAa;AACxC,cAAY;AAChB;ACpBA,IAAM,WAAW,EAAE,MAAM,MAAM,KAAI;AACnC,SAAS,KAAK,OAAO,MAAM,IAAI;AAC3B,MAAI,SAAS,OAAO,UAAU,WAAW,QAAQ,MAAM;AACvD,QAAM,MAAM;IACR,SAAS,CAAC,MAAM,QAAQ;AACpB,UAAI,YAAY,OAAO,QAAQ,WAAW,MAAM,IAAI;AACpD,kBAAY,UAAU,QAAQ,MAAM,OAAO,IAAI;AAC/C,eAAS,OAAO,QAAQ,MAAM,SAAS;AACvC,aAAO;IACnB;IACQ,UAAU,MAAM;AACZ,aAAO,IAAI,OAAO,QAAQ,GAAG;IACzC;EACA;AACI,SAAO;AACX;AACO,IAAM,QAAQ;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,wBAAwB;EACxB,gBAAgB;EAChB,YAAY;EACZ,mBAAmB;EACnB,iBAAiB;EACjB,cAAc;EACd,mBAAmB;EACnB,eAAe;EACf,qBAAqB;EACrB,WAAW;EACX,iBAAiB;EACjB,iBAAiB;EACjB,yBAAyB;EACzB,0BAA0B;EAC1B,iBAAiB;EACjB,oBAAoB;EACpB,YAAY;EACZ,iBAAiB;EACjB,SAAS;EACT,cAAc;EACd,gBAAgB;EAChB,iBAAiB;EACjB,mBAAmB;EACnB,iBAAiB;EACjB,kBAAkB;EAClB,gBAAgB;EAChB,WAAW;EACX,SAAS;EACT,mBAAmB;EACnB,iBAAiB;EACjB,mBAAmB;EACnB,iBAAiB;EACjB,mBAAmB;EACnB,qBAAqB;EACrB,YAAY;EACZ,eAAe;EACf,oBAAoB;EACpB,uBAAuB;EACvB,cAAc;EACd,OAAO;EACP,eAAe;EACf,UAAU;EACV,WAAW;EACX,WAAW;EACX,gBAAgB;EAChB,WAAW;EACX,eAAe;EACf,eAAe;EACf,eAAe,CAAC,SAAS,IAAI,OAAO,WAAW,IAAI,8BAA+B;EAClF,iBAAiB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAqD;EAC5H,SAAS,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;EACnH,kBAAkB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB;EACzF,mBAAmB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI;EAC7E,gBAAgB,CAAC,WAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,sBAAsB,GAAG;AACnG;AAIA,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,SAAS;AACf,IAAM,KAAK;AACX,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,WAAW,KAAK,YAAY,EAC7B,QAAQ,SAAS,MAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,YAAY,EAAE,EACtB,SAAQ;AACb,IAAM,cAAc,KAAK,YAAY,EAChC,QAAQ,SAAS,MAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,UAAU,mCAAmC,EACrD,SAAQ;AACb,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,MAAM,KAAK,6GAA6G,EACzH,QAAQ,SAAS,WAAW,EAC5B,QAAQ,SAAS,8DAA8D,EAC/E,SAAQ;AACb,IAAM,OAAO,KAAK,sCAAsC,EACnD,QAAQ,SAAS,MAAM,EACvB,SAAQ;AACb,IAAM,OAAO;AAMb,IAAM,WAAW;AACjB,IAAM,OAAO,KAAK,6dASP,GAAG,EACT,QAAQ,WAAW,QAAQ,EAC3B,QAAQ,OAAO,IAAI,EACnB,QAAQ,aAAa,0EAA0E,EAC/F,SAAQ;AACb,IAAM,YAAY,KAAK,UAAU,EAC5B,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAQ;AACb,IAAM,aAAa,KAAK,yCAAyC,EAC5D,QAAQ,aAAa,SAAS,EAC9B,SAAQ;AAIb,IAAM,cAAc;EAChB;EACA,MAAM;EACN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO;EACP,MAAM;AACV;AAIA,IAAM,WAAW,KAAK,6JAEsE,EACvF,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,cAAc,SAAS,EAC/B,QAAQ,QAAQ,wBAAyB,EACzC,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAQ;AACb,IAAM,WAAW;EACb,GAAG;EACH,UAAU;EACV,OAAO;EACP,WAAW,KAAK,UAAU,EACrB,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAAS,QAAQ,EACzB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,IAAI,EACnB,SAAQ;AACjB;AAIA,IAAM,gBAAgB;EAClB,GAAG;EACH,MAAM,KAAK,wIAEiE,EACvE,QAAQ,WAAW,QAAQ,EAC3B,QAAQ,QAAQ,mKAGgB,EAChC,SAAQ;EACb,KAAK;EACL,SAAS;EACT,QAAQ;;EACR,UAAU;EACV,WAAW,KAAK,UAAU,EACrB,QAAQ,MAAM,EAAE,EAChB,QAAQ,WAAW,iBAAiB,EACpC,QAAQ,YAAY,QAAQ,EAC5B,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,WAAW,EAAE,EACrB,QAAQ,SAAS,EAAE,EACnB,QAAQ,SAAS,EAAE,EACnB,QAAQ,QAAQ,EAAE,EAClB,SAAQ;AACjB;AAIA,IAAMA,WAAS;AACf,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,aAAa;AAEnB,IAAM,eAAe;AACrB,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,cAAc,KAAK,yBAAyB,GAAG,EAChD,QAAQ,eAAe,mBAAmB,EAAE,SAAQ;AAEzD,IAAM,0BAA0B;AAChC,IAAM,iCAAiC;AACvC,IAAM,oCAAoC;AAE1C,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB,KAAK,oBAAoB,GAAG,EAC9C,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,oBAAoB,KAAK,oBAAoB,GAAG,EACjD,QAAQ,UAAU,uBAAuB,EACzC,SAAQ;AACb,IAAM,wBAAwB;AAQ9B,IAAM,oBAAoB,KAAK,uBAAuB,IAAI,EACrD,QAAQ,kBAAkB,sBAAsB,EAChD,QAAQ,eAAe,mBAAmB,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,uBAAuB,KAAK,uBAAuB,IAAI,EACxD,QAAQ,kBAAkB,iCAAiC,EAC3D,QAAQ,eAAe,8BAA8B,EACrD,QAAQ,UAAU,uBAAuB,EACzC,SAAQ;AAEb,IAAM,oBAAoB,KAAK,oNAMQ,IAAI,EACtC,QAAQ,kBAAkB,sBAAsB,EAChD,QAAQ,eAAe,mBAAmB,EAC1C,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,iBAAiB,KAAK,aAAa,IAAI,EACxC,QAAQ,UAAU,YAAY,EAC9B,SAAQ;AACb,IAAM,WAAW,KAAK,qCAAqC,EACtD,QAAQ,UAAU,8BAA8B,EAChD,QAAQ,SAAS,8IAA8I,EAC/J,SAAQ;AACb,IAAM,iBAAiB,KAAK,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAQ;AAC1E,IAAM,MAAM,KAAK,0JAKuB,EACnC,QAAQ,WAAW,cAAc,EACjC,QAAQ,aAAa,6EAA6E,EAClG,SAAQ;AACb,IAAM,eAAe;AACrB,IAAM,OAAO,KAAK,+CAA+C,EAC5D,QAAQ,SAAS,YAAY,EAC7B,QAAQ,QAAQ,sCAAsC,EACtD,QAAQ,SAAS,6DAA6D,EAC9E,SAAQ;AACb,IAAM,UAAU,KAAK,yBAAyB,EACzC,QAAQ,SAAS,YAAY,EAC7B,QAAQ,OAAO,WAAW,EAC1B,SAAQ;AACb,IAAM,SAAS,KAAK,uBAAuB,EACtC,QAAQ,OAAO,WAAW,EAC1B,SAAQ;AACb,IAAM,gBAAgB,KAAK,yBAAyB,GAAG,EAClD,QAAQ,WAAW,OAAO,EAC1B,QAAQ,UAAU,MAAM,EACxB,SAAQ;AAIb,IAAM,eAAe;EACjB,YAAY;;EACZ;EACA;EACA;EACA;EACA,MAAM;EACN,KAAK;EACL;EACA;EACA;EACJ,QAAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM;EACN,KAAK;AACT;AAIA,IAAM,iBAAiB;EACnB,GAAG;EACH,MAAM,KAAK,yBAAyB,EAC/B,QAAQ,SAAS,YAAY,EAC7B,SAAQ;EACb,SAAS,KAAK,+BAA+B,EACxC,QAAQ,SAAS,YAAY,EAC7B,SAAQ;AACjB;AAIA,IAAM,YAAY;EACd,GAAG;EACH,mBAAmB;EACnB,gBAAgB;EAChB,KAAK,KAAK,oEAAoE,GAAG,EAC5E,QAAQ,SAAS,2EAA2E,EAC5F,SAAQ;EACb,YAAY;EACZ,KAAK;EACL,MAAM;AACV;AAIA,IAAM,eAAe;EACjB,GAAG;EACH,IAAI,KAAK,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAQ;EAC1C,MAAM,KAAK,UAAU,IAAI,EACpB,QAAQ,QAAQ,eAAe,EAC/B,QAAQ,WAAW,GAAG,EACtB,SAAQ;AACjB;AAIO,IAAM,QAAQ;EACjB,QAAQ;EACR,KAAK;EACL,UAAU;AACd;AACO,IAAM,SAAS;EAClB,QAAQ;EACR,KAAK;EACL,QAAQ;EACR,UAAU;AACd;AClYA,IAAM,qBAAqB;EACvB,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACT;AACA,IAAM,uBAAuB,CAAC,OAAO,mBAAmB,EAAE;AACnD,SAAS,OAAOC,OAAM,QAAQ;AACjC,MAAI,QAAQ;AACR,QAAI,MAAM,WAAW,KAAKA,KAAI,GAAG;AAC7B,aAAOA,MAAK,QAAQ,MAAM,eAAe,oBAAoB;IACzE;EACA,OACS;AACD,QAAI,MAAM,mBAAmB,KAAKA,KAAI,GAAG;AACrC,aAAOA,MAAK,QAAQ,MAAM,uBAAuB,oBAAoB;IACjF;EACA;AACI,SAAOA;AACX;AAeO,SAAS,SAAS,MAAM;AAC3B,MAAI;AACA,WAAO,UAAU,IAAI,EAAE,QAAQ,MAAM,eAAe,GAAG;EAC/D,QACU;AACF,WAAO;EACf;AACI,SAAO;AACX;AACO,SAAS,WAAW,UAAU,OAAO;;AAGxC,QAAM,MAAM,SAAS,QAAQ,MAAM,UAAU,CAAC,OAAO,QAAQ,QAAQ;AACjE,QAAI,UAAU;AACd,QAAI,OAAO;AACX,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM;AAChC,gBAAU,CAAC;AACf,QAAI,SAAS;AAGT,aAAO;IACnB,OACa;AAED,aAAO;IACnB;EACA,CAAK,GAAG,QAAQ,IAAI,MAAM,MAAM,SAAS;AACrC,MAAI,IAAI;AAER,MAAI,CAAC,MAAM,CAAC,EAAE,KAAI,GAAI;AAClB,UAAM,MAAK;EACnB;AACI,MAAI,MAAM,SAAS,KAAK,GAAC,WAAM,GAAG,EAAE,MAAX,mBAAc,SAAQ;AAC3C,UAAM,IAAG;EACjB;AACI,MAAI,OAAO;AACP,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,OAAO,KAAK;IAC9B,OACa;AACD,aAAO,MAAM,SAAS;AAClB,cAAM,KAAK,EAAE;IAC7B;EACA;AACI,SAAO,IAAI,MAAM,QAAQ,KAAK;AAE1B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAI,EAAG,QAAQ,MAAM,WAAW,GAAG;EAC/D;AACI,SAAO;AACX;AASO,SAAS,MAAM,KAAK,GAAG,QAAQ;AAClC,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACT,WAAO;EACf;AAEI,MAAI,UAAU;AAEd,SAAO,UAAU,GAAG;AAChB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,MAAS;AAC3B;IACZ,OAIa;AACD;IACZ;EACA;AACI,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACnC;AACO,SAAS,mBAAmB,KAAK,GAAG;AACvC,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC1B,WAAO;EACf;AACI,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,IAAI,CAAC,MAAM,MAAM;AACjB;IACZ,WACiB,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACtB;IACZ,WACiB,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACtB;AACA,UAAI,QAAQ,GAAG;AACX,eAAO;MACvB;IACA;EACA;AACI,SAAO;AACX;ACzIA,SAAS,WAAW,KAAKC,OAAM,KAAKC,QAAO,OAAO;AAC9C,QAAM,OAAOD,MAAK;AAClB,QAAM,QAAQA,MAAK,SAAS;AAC5B,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,MAAM,MAAM,mBAAmB,IAAI;AAC/D,MAAI,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK;AAC1B,IAAAC,OAAM,MAAM,SAAS;AACrB,UAAM,QAAQ;MACV,MAAM;MACN;MACA;MACA;MACA;MACA,QAAQA,OAAM,aAAa,IAAI;IAC3C;AACQ,IAAAA,OAAM,MAAM,SAAS;AACrB,WAAO;EACf;AACI,SAAO;IACH,MAAM;IACN;IACA;IACA;IACA;EACR;AACA;AACA,SAAS,uBAAuB,KAAK,MAAM,OAAO;AAC9C,QAAM,oBAAoB,IAAI,MAAM,MAAM,MAAM,sBAAsB;AACtE,MAAI,sBAAsB,MAAM;AAC5B,WAAO;EACf;AACI,QAAM,eAAe,kBAAkB,CAAC;AACxC,SAAO,KACF,MAAM,IAAI,EACV,IAAI,UAAQ;AACb,UAAM,oBAAoB,KAAK,MAAM,MAAM,MAAM,cAAc;AAC/D,QAAI,sBAAsB,MAAM;AAC5B,aAAO;IACnB;AACQ,UAAM,CAAC,YAAY,IAAI;AACvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC5C,aAAO,KAAK,MAAM,aAAa,MAAM;IACjD;AACQ,WAAO;EACf,CAAK,EACI,KAAK,IAAI;AAClB;AAIO,IAAM,aAAN,MAAiB;;EAIpB,YAAYC,UAAS;AAHrB;AACA;AACA;;AAEI,SAAK,UAAUA,YAAW;EAClC;EACI,MAAM,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC1B,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;MAC1B;IACA;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAkB,EAAE;AACjE,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,gBAAgB;QAChB,MAAM,CAAC,KAAK,QAAQ,WACd,MAAM,MAAM,IAAI,IAChB;MACtB;IACA;EACA;EACI,OAAO,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACL,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK;AACjE,aAAO;QACH,MAAM;QACN;QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAI,EAAG,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;QACpF;MAChB;IACA;EACA;EACI,QAAQ,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,KAAI;AAEtB,UAAI,KAAK,MAAM,MAAM,WAAW,KAAK,IAAI,GAAG;AACxC,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACvB,iBAAO,QAAQ,KAAI;QACvC,WACyB,CAAC,WAAW,KAAK,MAAM,MAAM,gBAAgB,KAAK,OAAO,GAAG;AAEjE,iBAAO,QAAQ,KAAI;QACvC;MACA;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,OAAO,IAAI,CAAC,EAAE;QACd;QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;MAC9C;IACA;EACA;EACI,GAAG,KAAK;AACJ,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,MAAM,IAAI,CAAC,GAAG,IAAI;MACvC;IACA;EACA;EACI,WAAW,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACL,UAAI,QAAQ,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,IAAI;AAC1C,UAAI,MAAM;AACV,UAAI,OAAO;AACX,YAAM,SAAS,CAAA;AACf,aAAO,MAAM,SAAS,GAAG;AACrB,YAAI,eAAe;AACnB,cAAM,eAAe,CAAA;AACrB,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAE/B,cAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,GAAG;AACjD,yBAAa,KAAK,MAAM,CAAC,CAAC;AAC1B,2BAAe;UACvC,WAC6B,CAAC,cAAc;AACpB,yBAAa,KAAK,MAAM,CAAC,CAAC;UAClD,OACyB;AACD;UACxB;QACA;AACgB,gBAAQ,MAAM,MAAM,CAAC;AACrB,cAAM,aAAa,aAAa,KAAK,IAAI;AACzC,cAAM,cAAc,WAEf,QAAQ,KAAK,MAAM,MAAM,yBAAyB,UAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,0BAA0B,EAAE;AAC1D,cAAM,MAAM,GAAG,GAAG;EAAK,UAAU,KAAK;AACtC,eAAO,OAAO,GAAG,IAAI;EAAK,WAAW,KAAK;AAG1C,cAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,YAAY,aAAa,QAAQ,IAAI;AAChD,aAAK,MAAM,MAAM,MAAM;AAEvB,YAAI,MAAM,WAAW,GAAG;AACpB;QACpB;AACgB,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,aAAI,uCAAW,UAAS,QAAQ;AAE5B;QACpB,YACyB,uCAAW,UAAS,cAAc;AAEvC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,WAAW,OAAO;AACxC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACpE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,KAAK,MAAM,IAAI,SAAS;AACxE;QACpB,YACyB,uCAAW,UAAS,QAAQ;AAEjC,gBAAM,WAAW;AACjB,gBAAM,UAAU,SAAS,MAAM,OAAO,MAAM,KAAK,IAAI;AACrD,gBAAM,WAAW,KAAK,KAAK,OAAO;AAClC,iBAAO,OAAO,SAAS,CAAC,IAAI;AAC5B,gBAAM,IAAI,UAAU,GAAG,IAAI,SAAS,UAAU,IAAI,MAAM,IAAI,SAAS;AACrE,iBAAO,KAAK,UAAU,GAAG,KAAK,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACvE,kBAAQ,QAAQ,UAAU,OAAO,GAAG,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,IAAI;AAC9D;QACpB;MACA;AACY,aAAO;QACH,MAAM;QACN;QACA;QACA;MAChB;IACA;EACA;EACI,KAAK,KAAK;AACN,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,KAAI;AACtB,YAAM,YAAY,KAAK,SAAS;AAChC,YAAMC,QAAO;QACT,MAAM;QACN,KAAK;QACL,SAAS;QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;QACxC,OAAO;QACP,OAAO,CAAA;MACvB;AACY,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAC5D,UAAI,KAAK,QAAQ,UAAU;AACvB,eAAO,YAAY,OAAO;MAC1C;AAEY,YAAM,YAAY,KAAK,MAAM,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB;AAExB,aAAO,KAAK;AACR,YAAI,WAAW;AACf,YAAI,MAAM;AACV,YAAI,eAAe;AACnB,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAC9B;QACpB;AACgB,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AAC/B;QACpB;AACgB,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAC9B,YAAI,OAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,CAAC,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AAC7G,YAAI,WAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACnC,YAAI,YAAY,CAAC,KAAK,KAAI;AAC1B,YAAI,SAAS;AACb,YAAI,KAAK,QAAQ,UAAU;AACvB,mBAAS;AACT,yBAAe,KAAK,UAAS;QACjD,WACyB,WAAW;AAChB,mBAAS,IAAI,CAAC,EAAE,SAAS;QAC7C,OACqB;AACD,mBAAS,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY;AACpD,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;QACrC;AACgB,YAAI,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,QAAQ,GAAG;AACxD,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;QAC/B;AACgB,YAAI,CAAC,UAAU;AACX,gBAAM,kBAAkB,KAAK,MAAM,MAAM,gBAAgB,MAAM;AAC/D,gBAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/C,gBAAM,mBAAmB,KAAK,MAAM,MAAM,iBAAiB,MAAM;AACjE,gBAAM,oBAAoB,KAAK,MAAM,MAAM,kBAAkB,MAAM;AACnE,gBAAM,iBAAiB,KAAK,MAAM,MAAM,eAAe,MAAM;AAE7D,iBAAO,KAAK;AACR,kBAAM,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACpC,gBAAI;AACJ,uBAAW;AAEX,gBAAI,KAAK,QAAQ,UAAU;AACvB,yBAAW,SAAS,QAAQ,KAAK,MAAM,MAAM,oBAAoB,IAAI;AACrE,oCAAsB;YAClD,OAC6B;AACD,oCAAsB,SAAS,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM;YACzG;AAEwB,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACjC;YAC5B;AAEwB,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AAClC;YAC5B;AAEwB,gBAAI,eAAe,KAAK,QAAQ,GAAG;AAC/B;YAC5B;AAEwB,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAChC;YAC5B;AAEwB,gBAAI,QAAQ,KAAK,QAAQ,GAAG;AACxB;YAC5B;AACwB,gBAAI,oBAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,UAAU,CAAC,SAAS,KAAI,GAAI;AACzF,8BAAgB,OAAO,oBAAoB,MAAM,MAAM;YACnF,OAC6B;AAED,kBAAI,WAAW;AACX;cAChC;AAE4B,kBAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AACjG;cAChC;AAC4B,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC7B;cAChC;AAC4B,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAC9B;cAChC;AAC4B,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACpB;cAChC;AAC4B,8BAAgB,OAAO;YACnD;AACwB,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAI,GAAI;AAChC,0BAAY;YACxC;AACwB,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,oBAAoB,MAAM,MAAM;UAC/D;QACA;AACgB,YAAI,CAACA,MAAK,OAAO;AAEb,cAAI,mBAAmB;AACnB,YAAAA,MAAK,QAAQ;UACrC,WAC6B,KAAK,MAAM,MAAM,gBAAgB,KAAK,GAAG,GAAG;AACjD,gCAAoB;UAC5C;QACA;AACgB,YAAI,SAAS;AACb,YAAI;AAEJ,YAAI,KAAK,QAAQ,KAAK;AAClB,mBAAS,KAAK,MAAM,MAAM,WAAW,KAAK,YAAY;AACtD,cAAI,QAAQ;AACR,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE;UAChG;QACA;AACgB,QAAAA,MAAK,MAAM,KAAK;UACZ,MAAM;UACN;UACA,MAAM,CAAC,CAAC;UACR,SAAS;UACT,OAAO;UACP,MAAM;UACN,QAAQ,CAAA;QAC5B,CAAiB;AACD,QAAAA,MAAK,OAAO;MAC5B;AAEY,YAAM,WAAWA,MAAK,MAAM,GAAG,EAAE;AACjC,UAAI,UAAU;AACV,iBAAS,MAAM,SAAS,IAAI,QAAO;AACnC,iBAAS,OAAO,SAAS,KAAK,QAAO;MACrD,OACiB;AAED;MAChB;AACY,MAAAA,MAAK,MAAMA,MAAK,IAAI,QAAO;AAE3B,eAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AACxC,aAAK,MAAM,MAAM,MAAM;AACvB,QAAAA,MAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAYA,MAAK,MAAM,CAAC,EAAE,MAAM,CAAA,CAAE;AACpE,YAAI,CAACA,MAAK,OAAO;AAEb,gBAAM,UAAUA,MAAK,MAAM,CAAC,EAAE,OAAO,OAAO,OAAK,EAAE,SAAS,OAAO;AACnE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,OAAK,KAAK,MAAM,MAAM,QAAQ,KAAK,EAAE,GAAG,CAAC;AAC1G,UAAAA,MAAK,QAAQ;QACjC;MACA;AAEY,UAAIA,MAAK,OAAO;AACZ,iBAAS,IAAI,GAAG,IAAIA,MAAK,MAAM,QAAQ,KAAK;AACxC,UAAAA,MAAK,MAAM,CAAC,EAAE,QAAQ;QAC1C;MACA;AACY,aAAOA;IACnB;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,YAAM,QAAQ;QACV,MAAM;QACN,OAAO;QACP,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;QAC3D,MAAM,IAAI,CAAC;MAC3B;AACY,aAAO;IACnB;EACA;EACI,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACL,YAAMC,OAAM,IAAI,CAAC,EAAE,YAAW,EAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AAClF,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,cAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;AAC5H,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IAAI,CAAC;AACrH,aAAO;QACH,MAAM;QACN,KAAAA;QACA,KAAK,IAAI,CAAC;QACV;QACA;MAChB;IACA;EACA;EACI,MAAM,KAAK;;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,CAAC,KAAK;AACN;IACZ;AACQ,QAAI,CAAC,KAAK,MAAM,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,GAAG;AAE/C;IACZ;AACQ,UAAM,UAAU,WAAW,IAAI,CAAC,CAAC;AACjC,UAAM,SAAS,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,EAAE,MAAM,GAAG;AAC7E,UAAM,SAAO,SAAI,CAAC,MAAL,mBAAQ,UAAS,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,EAAE,EAAE,MAAM,IAAI,IAAI,CAAA;AACnG,UAAM,OAAO;MACT,MAAM;MACN,KAAK,IAAI,CAAC;MACV,QAAQ,CAAA;MACR,OAAO,CAAA;MACP,MAAM,CAAA;IAClB;AACQ,QAAI,QAAQ,WAAW,OAAO,QAAQ;AAElC;IACZ;AACQ,eAAW,SAAS,QAAQ;AACxB,UAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,KAAK,GAAG;AAC9C,aAAK,MAAM,KAAK,OAAO;MACvC,WACqB,KAAK,MAAM,MAAM,iBAAiB,KAAK,KAAK,GAAG;AACpD,aAAK,MAAM,KAAK,QAAQ;MACxC,WACqB,KAAK,MAAM,MAAM,eAAe,KAAK,KAAK,GAAG;AAClD,aAAK,MAAM,KAAK,MAAM;MACtC,OACiB;AACD,aAAK,MAAM,KAAK,IAAI;MACpC;IACA;AACQ,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,WAAK,OAAO,KAAK;QACb,MAAM,QAAQ,CAAC;QACf,QAAQ,KAAK,MAAM,OAAO,QAAQ,CAAC,CAAC;QACpC,QAAQ;QACR,OAAO,KAAK,MAAM,CAAC;MACnC,CAAa;IACb;AACQ,eAAW,OAAO,MAAM;AACpB,WAAK,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,MAAM,MAAM;AAChE,eAAO;UACH,MAAM;UACN,QAAQ,KAAK,MAAM,OAAO,IAAI;UAC9B,QAAQ;UACR,OAAO,KAAK,MAAM,CAAC;QACvC;MACA,CAAa,CAAC;IACd;AACQ,WAAO;EACf;EACI,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;QACtC,MAAM,IAAI,CAAC;QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;MAChD;IACA;EACA;EACI,UAAU,KAAK;AACX,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACL,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAC5C,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAClB,IAAI,CAAC;AACX,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;MAC9C;IACA;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;MAChD;IACA;EACA;EACI,OAAO,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;MAC3B;IACA;EACA;EACI,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AACrE,aAAK,MAAM,MAAM,SAAS;MAC1C,WACqB,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AACvE,aAAK,MAAM,MAAM,SAAS;MAC1C;AACY,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,GAAG;AACjF,aAAK,MAAM,MAAM,aAAa;MAC9C,WACqB,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,GAAG;AACnF,aAAK,MAAM,MAAM,aAAa;MAC9C;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,QAAQ,KAAK,MAAM,MAAM;QACzB,YAAY,KAAK,MAAM,MAAM;QAC7B,OAAO;QACP,MAAM,IAAI,CAAC;MAC3B;IACA;EACA;EACI,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,YAAM,aAAa,IAAI,CAAC,EAAE,KAAI;AAC9B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,kBAAkB,KAAK,UAAU,GAAG;AAE/E,YAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAI;AACtD;QACpB;AAEgB,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACnD;QACpB;MACA,OACiB;AAED,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,iBAAiB,IAAI;AACrB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAI;AAC1C,cAAI,CAAC,IAAI;QAC7B;MACA;AACY,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AAEvB,cAAMJ,QAAO,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI;AACzD,YAAIA,OAAM;AACN,iBAAOA,MAAK,CAAC;AACb,kBAAQA,MAAK,CAAC;QAClC;MACA,OACiB;AACD,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;MACvD;AACY,aAAO,KAAK,KAAI;AAChB,UAAI,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,GAAG;AAC/C,YAAI,KAAK,QAAQ,YAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK,UAAU,GAAI;AAE/E,iBAAO,KAAK,MAAM,CAAC;QACvC,OACqB;AACD,iBAAO,KAAK,MAAM,GAAG,EAAE;QAC3C;MACA;AACY,aAAO,WAAW,KAAK;QACnB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;QACpE,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI;MACvF,GAAe,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;IAC7C;EACA;EACI,QAAQ,KAAK,OAAO;AAChB,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OACrC,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AAC/C,YAAM,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG;AACvF,YAAMA,QAAO,MAAM,WAAW,YAAW,CAAE;AAC3C,UAAI,CAACA,OAAM;AACP,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;UACH,MAAM;UACN,KAAK;UACL;QACpB;MACA;AACY,aAAO,WAAW,KAAKA,OAAM,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;IACvE;EACA;EACI,SAAS,KAAK,WAAW,WAAW,IAAI;AACpC,QAAI,QAAQ,KAAK,MAAM,OAAO,eAAe,KAAK,GAAG;AACrD,QAAI,CAAC;AACD;AAEJ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB;AAC/D;AACJ,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AACzC,QAAI,CAAC,YAAY,CAAC,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,GAAG;AAExE,YAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS;AACvC,UAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;AAC3D,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,oBAAoB,KAAK,MAAM,OAAO;AAC7F,aAAO,YAAY;AAEnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AACrD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC7C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAC5E,YAAI,CAAC;AACD;AACJ,kBAAU,CAAC,GAAG,MAAM,EAAE;AACtB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACtB,wBAAc;AACd;QACpB,WACyB,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC3B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC3C,6BAAiB;AACjB;UACxB;QACA;AACgB,sBAAc;AACd,YAAI,aAAa;AACb;AAEJ,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAEhE,cAAM,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AACxC,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,QAAQ,iBAAiB,OAAO;AAEzE,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAChC,gBAAMK,QAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,iBAAO;YACH,MAAM;YACN;YACA,MAAAA;YACA,QAAQ,KAAK,MAAM,aAAaA,KAAI;UAC5D;QACA;AAEgB,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;UACH,MAAM;UACN;UACA;UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;QACxD;MACA;IACA;EACA;EACI,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,GAAG;AACjE,YAAM,mBAAmB,KAAK,MAAM,MAAM,aAAa,KAAK,IAAI;AAChE,YAAM,0BAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAK,IAAI,KAAK,KAAK,MAAM,MAAM,gBAAgB,KAAK,IAAI;AAC3H,UAAI,oBAAoB,yBAAyB;AAC7C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;MACxD;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;MAChB;IACA;EACA;EACI,GAAG,KAAK;AACJ,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;MAC1B;IACA;EACA;EACI,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;MACtD;IACA;EACA;EACI,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACL,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAChB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;MACnC,OACiB;AACD,eAAO,IAAI,CAAC;AACZ,eAAO;MACvB;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;QACA;QACA,QAAQ;UACJ;YACI,MAAM;YACN,KAAK;YACL;UACxB;QACA;MACA;IACA;EACA;EACI,IAAI,KAAK;;AACL,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACvC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAChB,eAAO,IAAI,CAAC;AACZ,eAAO,YAAY;MACnC,OACiB;AAED,YAAI;AACJ,WAAG;AACC,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,MAAI,UAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,MAAxC,mBAA4C,OAAM;QAC/E,SAAyB,gBAAgB,IAAI,CAAC;AAC9B,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,CAAC,MAAM,QAAQ;AACnB,iBAAO,YAAY,IAAI,CAAC;QAC5C,OACqB;AACD,iBAAO,IAAI,CAAC;QAChC;MACA;AACY,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV;QACA;QACA,QAAQ;UACJ;YACI,MAAM;YACN,KAAK;YACL;UACxB;QACA;MACA;IACA;EACA;EACI,WAAW,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,YAAM,UAAU,KAAK,MAAM,MAAM;AACjC,aAAO;QACH,MAAM;QACN,KAAK,IAAI,CAAC;QACV,MAAM,IAAI,CAAC;QACX;MAChB;IACA;EACA;AACA;AClxBO,IAAM,SAAN,MAAM,QAAO;EAMhB,YAAYH,UAAS;AALrB;AACA;AACA;AACA;AACA;AAGI,SAAK,SAAS,CAAA;AACd,SAAK,OAAO,QAAQ,uBAAO,OAAO,IAAI;AACtC,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,WAAU;AACjE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAA;AACnB,SAAK,QAAQ;MACT,QAAQ;MACR,YAAY;MACZ,KAAK;IACjB;AACQ,UAAM,QAAQ;MACV;MACA,OAAO,MAAM;MACb,QAAQ,OAAO;IAC3B;AACQ,QAAI,KAAK,QAAQ,UAAU;AACvB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;IAClC,WACiB,KAAK,QAAQ,KAAK;AACvB,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACrB,cAAM,SAAS,OAAO;MACtC,OACiB;AACD,cAAM,SAAS,OAAO;MACtC;IACA;AACQ,SAAK,UAAU,QAAQ;EAC/B;;;;EAII,WAAW,QAAQ;AACf,WAAO;MACH;MACA;IACZ;EACA;;;;EAII,OAAO,IAAI,KAAKA,UAAS;AACrB,UAAMD,SAAQ,IAAI,QAAOC,QAAO;AAChC,WAAOD,OAAM,IAAI,GAAG;EAC5B;;;;EAII,OAAO,UAAU,KAAKC,UAAS;AAC3B,UAAMD,SAAQ,IAAI,QAAOC,QAAO;AAChC,WAAOD,OAAM,aAAa,GAAG;EACrC;;;;EAII,IAAI,KAAK;AACL,UAAM,IAAI,QAAQ,MAAM,gBAAgB,IAAI;AAC5C,SAAK,YAAY,KAAK,KAAK,MAAM;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;IACnD;AACQ,SAAK,cAAc,CAAA;AACnB,WAAO,KAAK;EACpB;EACI,YAAY,KAAK,SAAS,CAAA,GAAI,uBAAuB,OAAO;;AACxD,QAAI,KAAK,QAAQ,UAAU;AACvB,YAAM,IAAI,QAAQ,MAAM,eAAe,MAAM,EAAE,QAAQ,MAAM,WAAW,EAAE;IACtF;AACQ,WAAO,KAAK;AACR,UAAI;AACJ,WAAI,gBAAK,QAAQ,eAAb,mBAAyB,UAAzB,mBAAgC,KAAK,CAAC,iBAAiB;AACvD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAI,GAAI,KAAK,MAAM,GAAG;AACzD,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;QAC3B;AACgB,eAAO;MACvB,IAAgB;AACA;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,IAAI,WAAW,KAAK,cAAc,QAAW;AAGnD,oBAAU,OAAO;QACrC,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAE9B,aAAI,uCAAW,UAAS,gBAAe,uCAAW,UAAS,QAAQ;AAC/D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,aAAI,uCAAW,UAAS,gBAAe,uCAAW,UAAS,QAAQ;AAC/D,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,WACyB,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACpC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;YAC3B,MAAM,MAAM;YACZ,OAAO,MAAM;UACrC;QACA;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAGY,UAAI,SAAS;AACb,WAAI,UAAK,QAAQ,eAAb,mBAAyB,YAAY;AACrC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,CAAC,kBAAkB;AAC1D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAI,GAAI,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACjD,yBAAa,KAAK,IAAI,YAAY,SAAS;UACnE;QACA,CAAiB;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC1C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;QAC5D;MACA;AACY,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAC9D,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,yBAAwB,uCAAW,UAAS,aAAa;AACzD,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAG;AACpB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB,+BAAuB,OAAO,WAAW,IAAI;AAC7C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,aAAI,uCAAW,UAAS,QAAQ;AAC5B,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAG;AACpB,eAAK,YAAY,GAAG,EAAE,EAAE,MAAM,UAAU;QAC5D,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AACY,UAAI,KAAK;AACL,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAQ,MAAM,MAAM;AACpB;QACpB,OACqB;AACD,gBAAM,IAAI,MAAM,MAAM;QAC1C;MACA;IACA;AACQ,SAAK,MAAM,MAAM;AACjB,WAAO;EACf;EACI,OAAO,KAAK,SAAS,CAAA,GAAI;AACrB,SAAK,YAAY,KAAK,EAAE,KAAK,OAAM,CAAE;AACrC,WAAO;EACf;;;;EAII,aAAa,KAAK,SAAS,CAAA,GAAI;;AAE3B,QAAI,YAAY;AAChB,QAAI,QAAQ;AAEZ,QAAI,KAAK,OAAO,OAAO;AACnB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AAClB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAChF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACnE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IACpC,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MACxC,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;UACjG;QACA;MACA;IACA;AAEQ,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK,SAAS,MAAM,MAAM;AACjF,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;IACrI;AAEQ,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC5E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;IACvK;AACQ,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,WAAO,KAAK;AACR,UAAI,CAAC,cAAc;AACf,mBAAW;MAC3B;AACY,qBAAe;AACf,UAAI;AAEJ,WAAI,gBAAK,QAAQ,eAAb,mBAAyB,WAAzB,mBAAiC,KAAK,CAAC,iBAAiB;AACxD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAI,GAAI,KAAK,MAAM,GAAG;AACzD,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;QAC3B;AACgB,eAAO;MACvB,IAAgB;AACA;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AACxD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,YAAI,MAAM,SAAS,WAAU,uCAAW,UAAS,QAAQ;AACrD,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;QAC5C,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC3D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAEY,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI;AACzD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;MAChB;AAGY,UAAI,SAAS;AACb,WAAI,UAAK,QAAQ,eAAb,mBAAyB,aAAa;AACtC,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,CAAC,kBAAkB;AAC3D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAI,GAAI,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACjD,yBAAa,KAAK,IAAI,YAAY,SAAS;UACnE;QACA,CAAiB;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC1C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;QAC5D;MACA;AACY,UAAI,QAAQ,KAAK,UAAU,WAAW,MAAM,GAAG;AAC3C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC7B,qBAAW,MAAM,IAAI,MAAM,EAAE;QACjD;AACgB,uBAAe;AACf,cAAM,YAAY,OAAO,GAAG,EAAE;AAC9B,aAAI,uCAAW,UAAS,QAAQ;AAC5B,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;QAC5C,OACqB;AACD,iBAAO,KAAK,KAAK;QACrC;AACgB;MAChB;AACY,UAAI,KAAK;AACL,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAQ,MAAM,MAAM;AACpB;QACpB,OACqB;AACD,gBAAM,IAAI,MAAM,MAAM;QAC1C;MACA;IACA;AACQ,WAAO;EACf;AACA;AC5ZO,IAAM,YAAN,MAAgB;;EAGnB,YAAYC,UAAS;AAFrB;AACA;AAEI,SAAK,UAAUA,YAAW;EAClC;EACI,MAAM,OAAO;AACT,WAAO;EACf;EACI,KAAK,EAAE,MAAM,MAAM,QAAO,GAAI;;AAC1B,UAAM,cAAc,cAAQ,IAAI,MAAM,MAAM,aAAa,MAArC,mBAAyC;AAC7D,UAAM,OAAO,KAAK,QAAQ,MAAM,eAAe,EAAE,IAAI;AACrD,QAAI,CAAC,YAAY;AACb,aAAO,iBACA,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;IAClB;AACQ,WAAO,gCACD,OAAO,UAAU,IACjB,QACC,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;EACd;EACI,WAAW,EAAE,OAAM,GAAI;AACnB,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM;AACrC,WAAO;EAAiB,IAAI;;EACpC;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO;EACf;EACI,QAAQ,EAAE,QAAQ,MAAK,GAAI;AACvB,WAAO,KAAK,KAAK,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,MAAM,KAAK;;EACvE;EACI,GAAG,OAAO;AACN,WAAO;EACf;EACI,KAAK,OAAO;AACR,UAAM,UAAU,MAAM;AACtB,UAAM,QAAQ,MAAM;AACpB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AACzC,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAQ,KAAK,SAAS,IAAI;IACtC;AACQ,UAAM,OAAO,UAAU,OAAO;AAC9B,UAAM,YAAa,WAAW,UAAU,IAAM,aAAa,QAAQ,MAAO;AAC1E,WAAO,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO;EACrE;EACI,SAAS,MAAM;;AACX,QAAI,WAAW;AACf,QAAI,KAAK,MAAM;AACX,YAAM,WAAW,KAAK,SAAS,EAAE,SAAS,CAAC,CAAC,KAAK,QAAO,CAAE;AAC1D,UAAI,KAAK,OAAO;AACZ,cAAI,UAAK,OAAO,CAAC,MAAb,mBAAgB,UAAS,aAAa;AACtC,eAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,cAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACvG,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,OAAO,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI;AACrF,iBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;UAC3D;QACA,OACqB;AACD,eAAK,OAAO,QAAQ;YAChB,MAAM;YACN,KAAK,WAAW;YAChB,MAAM,WAAW;YACjB,SAAS;UACjC,CAAqB;QACrB;MACA,OACiB;AACD,oBAAY,WAAW;MACvC;IACA;AACQ,gBAAY,KAAK,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK;AACvD,WAAO,OAAO,QAAQ;;EAC9B;EACI,SAAS,EAAE,QAAO,GAAI;AAClB,WAAO,aACA,UAAU,gBAAgB,MAC3B;EACd;EACI,UAAU,EAAE,OAAM,GAAI;AAClB,WAAO,MAAM,KAAK,OAAO,YAAY,MAAM,CAAC;;EACpD;EACI,MAAM,OAAO;AACT,QAAI,SAAS;AAEb,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC1C,cAAQ,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;IAClD;AACQ,cAAU,KAAK,SAAS,EAAE,MAAM,KAAI,CAAE;AACtC,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAM,MAAM,MAAM,KAAK,CAAC;AACxB,aAAO;AACP,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,gBAAQ,KAAK,UAAU,IAAI,CAAC,CAAC;MAC7C;AACY,cAAQ,KAAK,SAAS,EAAE,MAAM,KAAI,CAAE;IAChD;AACQ,QAAI;AACA,aAAO,UAAU,IAAI;AACzB,WAAO,uBAED,SACA,eACA,OACA;EACd;EACI,SAAS,EAAE,KAAI,GAAI;AACf,WAAO;EAAS,IAAI;;EAC5B;EACI,UAAU,OAAO;AACb,UAAM,UAAU,KAAK,OAAO,YAAY,MAAM,MAAM;AACpD,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAME,OAAM,MAAM,QACZ,IAAI,IAAI,WAAW,MAAM,KAAK,OAC9B,IAAI,IAAI;AACd,WAAOA,OAAM,UAAU,KAAK,IAAI;;EACxC;;;;EAII,OAAO,EAAE,OAAM,GAAI;AACf,WAAO,WAAW,KAAK,OAAO,YAAY,MAAM,CAAC;EACzD;EACI,GAAG,EAAE,OAAM,GAAI;AACX,WAAO,OAAO,KAAK,OAAO,YAAY,MAAM,CAAC;EACrD;EACI,SAAS,EAAE,KAAI,GAAI;AACf,WAAO,SAAS,OAAO,MAAM,IAAI,CAAC;EAC1C;EACI,GAAG,OAAO;AACN,WAAO;EACf;EACI,IAAI,EAAE,OAAM,GAAI;AACZ,WAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC;EACtD;EACI,KAAK,EAAE,MAAM,OAAO,OAAM,GAAI;AAC1B,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM;AAC3C,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO;IACnB;AACQ,WAAO;AACP,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACP,aAAO,aAAc,OAAO,KAAK,IAAK;IAClD;AACQ,WAAO,MAAM,OAAO;AACpB,WAAO;EACf;EACI,MAAM,EAAE,MAAM,OAAO,KAAI,GAAI;AACzB,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO,OAAO,IAAI;IAC9B;AACQ,WAAO;AACP,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACP,aAAO,WAAW,OAAO,KAAK,CAAC;IAC3C;AACQ,WAAO;AACP,WAAO;EACf;EACI,KAAK,OAAO;AACR,WAAO,YAAY,SAAS,MAAM,SAC5B,KAAK,OAAO,YAAY,MAAM,MAAM,IACnC,aAAa,SAAS,MAAM,UAAU,MAAM,OAAO,OAAO,MAAM,IAAI;EACnF;AACA;AC7KO,IAAM,gBAAN,MAAoB;;EAEvB,OAAO,EAAE,KAAI,GAAI;AACb,WAAO;EACf;EACI,GAAG,EAAE,KAAI,GAAI;AACT,WAAO;EACf;EACI,SAAS,EAAE,KAAI,GAAI;AACf,WAAO;EACf;EACI,IAAI,EAAE,KAAI,GAAI;AACV,WAAO;EACf;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO;EACf;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO;EACf;EACI,KAAK,EAAE,KAAI,GAAI;AACX,WAAO,KAAK;EACpB;EACI,MAAM,EAAE,KAAI,GAAI;AACZ,WAAO,KAAK;EACpB;EACI,KAAK;AACD,WAAO;EACf;AACA;AC3BO,IAAM,UAAN,MAAM,SAAQ;EAIjB,YAAYF,UAAS;AAHrB;AACA;AACA;AAEI,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,UAAS;AAC9D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,SAAS,SAAS;AACvB,SAAK,eAAe,IAAI,cAAa;EAC7C;;;;EAII,OAAO,MAAM,QAAQA,UAAS;AAC1B,UAAMI,UAAS,IAAI,SAAQJ,QAAO;AAClC,WAAOI,QAAO,MAAM,MAAM;EAClC;;;;EAII,OAAO,YAAY,QAAQJ,UAAS;AAChC,UAAMI,UAAS,IAAI,SAAQJ,QAAO;AAClC,WAAOI,QAAO,YAAY,MAAM;EACxC;;;;EAII,MAAM,QAAQ,MAAM,MAAM;;AACtB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,WAAW,OAAO,CAAC;AAEzB,WAAI,gBAAK,QAAQ,eAAb,mBAAyB,cAAzB,mBAAqC,SAAS,OAAO;AACrD,cAAM,eAAe;AACrB,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,aAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAI,GAAI,YAAY;AACpG,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,aAAa,IAAI,GAAG;AAC9I,iBAAO,OAAO;AACd;QACpB;MACA;AACY,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAI;QACd,KAAK,SAAS;AACV,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;QACpB;QACgB,KAAK,MAAM;AACP,iBAAO,KAAK,SAAS,GAAG,KAAK;AAC7B;QACpB;QACgB,KAAK,WAAW;AACZ,iBAAO,KAAK,SAAS,QAAQ,KAAK;AAClC;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;QACpB;QACgB,KAAK,SAAS;AACV,iBAAO,KAAK,SAAS,MAAM,KAAK;AAChC;QACpB;QACgB,KAAK,cAAc;AACf,iBAAO,KAAK,SAAS,WAAW,KAAK;AACrC;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,KAAK,SAAS,KAAK,KAAK;AAC/B;QACpB;QACgB,KAAK,aAAa;AACd,iBAAO,KAAK,SAAS,UAAU,KAAK;AACpC;QACpB;QACgB,KAAK,QAAQ;AACT,cAAI,YAAY;AAChB,cAAI,OAAO,KAAK,SAAS,KAAK,SAAS;AACvC,iBAAO,IAAI,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AAC3D,wBAAY,OAAO,EAAE,CAAC;AACtB,oBAAQ,OAAO,KAAK,SAAS,KAAK,SAAS;UACnE;AACoB,cAAI,KAAK;AACL,mBAAO,KAAK,SAAS,UAAU;cAC3B,MAAM;cACN,KAAK;cACL,MAAM;cACN,QAAQ,CAAC,EAAE,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,KAAI,CAAE;YAC3F,CAAyB;UACzB,OACyB;AACD,mBAAO;UAC/B;AACoB;QACpB;QACgB,SAAS;AACL,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACrB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;UAC/B,OACyB;AACD,kBAAM,IAAI,MAAM,MAAM;UAC9C;QACA;MACA;IACA;AACQ,WAAO;EACf;;;;EAII,YAAY,QAAQ,WAAW,KAAK,UAAU;;AAC1C,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,WAAW,OAAO,CAAC;AAEzB,WAAI,gBAAK,QAAQ,eAAb,mBAAyB,cAAzB,mBAAqC,SAAS,OAAO;AACrD,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,SAAS,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAI,GAAI,QAAQ;AAC5F,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,SAAS,IAAI,GAAG;AAChI,iBAAO,OAAO;AACd;QACpB;MACA;AACY,YAAM,QAAQ;AACd,cAAQ,MAAM,MAAI;QACd,KAAK,UAAU;AACX,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,KAAK,SAAS;AACV,iBAAO,SAAS,MAAM,KAAK;AAC3B;QACpB;QACgB,KAAK,UAAU;AACX,iBAAO,SAAS,OAAO,KAAK;AAC5B;QACpB;QACgB,KAAK,MAAM;AACP,iBAAO,SAAS,GAAG,KAAK;AACxB;QACpB;QACgB,KAAK,YAAY;AACb,iBAAO,SAAS,SAAS,KAAK;AAC9B;QACpB;QACgB,KAAK,MAAM;AACP,iBAAO,SAAS,GAAG,KAAK;AACxB;QACpB;QACgB,KAAK,OAAO;AACR,iBAAO,SAAS,IAAI,KAAK;AACzB;QACpB;QACgB,KAAK,QAAQ;AACT,iBAAO,SAAS,KAAK,KAAK;AAC1B;QACpB;QACgB,SAAS;AACL,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACrB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;UAC/B,OACyB;AACD,kBAAM,IAAI,MAAM,MAAM;UAC9C;QACA;MACA;IACA;AACQ,WAAO;EACf;AACA;AC5LO,IAAM,SAAN,MAAa;EAGhB,YAAYJ,UAAS;AAFrB;AACA;AAEI,SAAK,UAAUA,YAAW;EAClC;;;;EASI,WAAW,UAAU;AACjB,WAAO;EACf;;;;EAII,YAAYH,OAAM;AACd,WAAOA;EACf;;;;EAII,iBAAiB,QAAQ;AACrB,WAAO;EACf;;;;EAII,eAAe;AACX,WAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;EAChD;;;;EAII,gBAAgB;AACZ,WAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;EACpD;AACA;AAnCI,cANS,QAMF,oBAAmB,oBAAI,IAAI;EAC9B;EACA;EACA;AACR,CAAK;ACLE,IAAM,SAAN,MAAa;EAWhB,eAAe,MAAM;AAVrB,oCAAW,aAAY;AACvB,mCAAU,KAAK;AACf,iCAAQ,KAAK,cAAc,IAAI;AAC/B,uCAAc,KAAK,cAAc,KAAK;AACtC,kCAAS;AACT,oCAAW;AACX,wCAAe;AACf,iCAAQ;AACR,qCAAY;AACZ,iCAAQ;AAEJ,SAAK,IAAI,GAAG,IAAI;EACxB;;;;EAII,WAAW,QAAQ,UAAU;;AACzB,QAAI,SAAS,CAAA;AACb,eAAW,SAAS,QAAQ;AACxB,eAAS,OAAO,OAAO,SAAS,KAAK,MAAM,KAAK,CAAC;AACjD,cAAQ,MAAM,MAAI;QACd,KAAK,SAAS;AACV,gBAAM,aAAa;AACnB,qBAAW,QAAQ,WAAW,QAAQ;AAClC,qBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;UACrF;AACoB,qBAAW,OAAO,WAAW,MAAM;AAC/B,uBAAW,QAAQ,KAAK;AACpB,uBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;YACzF;UACA;AACoB;QACpB;QACgB,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,mBAAS,OAAO,OAAO,KAAK,WAAW,UAAU,OAAO,QAAQ,CAAC;AACjE;QACpB;QACgB,SAAS;AACL,gBAAM,eAAe;AACrB,eAAI,gBAAK,SAAS,eAAd,mBAA0B,gBAA1B,mBAAwC,aAAa,OAAO;AAC5D,iBAAK,SAAS,WAAW,YAAY,aAAa,IAAI,EAAE,QAAQ,CAAC,gBAAgB;AAC7E,oBAAMQ,UAAS,aAAa,WAAW,EAAE,KAAK,QAAQ;AACtD,uBAAS,OAAO,OAAO,KAAK,WAAWA,SAAQ,QAAQ,CAAC;YACpF,CAAyB;UACzB,WAC6B,aAAa,QAAQ;AAC1B,qBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,QAAQ,QAAQ,CAAC;UAC7F;QACA;MACA;IACA;AACQ,WAAO;EACf;EACI,OAAO,MAAM;AACT,UAAM,aAAa,KAAK,SAAS,cAAc,EAAE,WAAW,CAAA,GAAI,aAAa,CAAA,EAAE;AAC/E,SAAK,QAAQ,CAAC,SAAS;AAEnB,YAAM,OAAO,EAAE,GAAG,KAAI;AAEtB,WAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,SAAS;AAElD,UAAI,KAAK,YAAY;AACjB,aAAK,WAAW,QAAQ,CAAC,QAAQ;AAC7B,cAAI,CAAC,IAAI,MAAM;AACX,kBAAM,IAAI,MAAM,yBAAyB;UACjE;AACoB,cAAI,cAAc,KAAK;AACnB,kBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,gBAAI,cAAc;AAEd,yBAAW,UAAU,IAAI,IAAI,IAAI,YAAaC,OAAM;AAChD,oBAAI,MAAM,IAAI,SAAS,MAAM,MAAMA,KAAI;AACvC,oBAAI,QAAQ,OAAO;AACf,wBAAM,aAAa,MAAM,MAAMA,KAAI;gBACvE;AACgC,uBAAO;cACvC;YACA,OAC6B;AACD,yBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;YACjE;UACA;AACoB,cAAI,eAAe,KAAK;AACpB,gBAAI,CAAC,IAAI,SAAU,IAAI,UAAU,WAAW,IAAI,UAAU,UAAW;AACjE,oBAAM,IAAI,MAAM,6CAA6C;YACzF;AACwB,kBAAM,WAAW,WAAW,IAAI,KAAK;AACrC,gBAAI,UAAU;AACV,uBAAS,QAAQ,IAAI,SAAS;YAC1D,OAC6B;AACD,yBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;YAClE;AACwB,gBAAI,IAAI,OAAO;AACX,kBAAI,IAAI,UAAU,SAAS;AACvB,oBAAI,WAAW,YAAY;AACvB,6BAAW,WAAW,KAAK,IAAI,KAAK;gBACxE,OACqC;AACD,6BAAW,aAAa,CAAC,IAAI,KAAK;gBACtE;cACA,WACqC,IAAI,UAAU,UAAU;AAC7B,oBAAI,WAAW,aAAa;AACxB,6BAAW,YAAY,KAAK,IAAI,KAAK;gBACzE,OACqC;AACD,6BAAW,cAAc,CAAC,IAAI,KAAK;gBACvE;cACA;YACA;UACA;AACoB,cAAI,iBAAiB,OAAO,IAAI,aAAa;AACzC,uBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;UAC/D;QACA,CAAiB;AACD,aAAK,aAAa;MAClC;AAEY,UAAI,KAAK,UAAU;AACf,cAAM,WAAW,KAAK,SAAS,YAAY,IAAI,UAAU,KAAK,QAAQ;AACtE,mBAAW,QAAQ,KAAK,UAAU;AAC9B,cAAI,EAAE,QAAQ,WAAW;AACrB,kBAAM,IAAI,MAAM,aAAa,IAAI,kBAAkB;UAC3E;AACoB,cAAI,CAAC,WAAW,QAAQ,EAAE,SAAS,IAAI,GAAG;AAEtC;UACxB;AACoB,gBAAM,eAAe;AACrB,gBAAM,eAAe,KAAK,SAAS,YAAY;AAC/C,gBAAM,eAAe,SAAS,YAAY;AAE1C,mBAAS,YAAY,IAAI,IAAIA,UAAS;AAClC,gBAAI,MAAM,aAAa,MAAM,UAAUA,KAAI;AAC3C,gBAAI,QAAQ,OAAO;AACf,oBAAM,aAAa,MAAM,UAAUA,KAAI;YACnE;AACwB,mBAAO,OAAO;UACtC;QACA;AACgB,aAAK,WAAW;MAChC;AACY,UAAI,KAAK,WAAW;AAChB,cAAM,YAAY,KAAK,SAAS,aAAa,IAAI,WAAW,KAAK,QAAQ;AACzE,mBAAW,QAAQ,KAAK,WAAW;AAC/B,cAAI,EAAE,QAAQ,YAAY;AACtB,kBAAM,IAAI,MAAM,cAAc,IAAI,kBAAkB;UAC5E;AACoB,cAAI,CAAC,WAAW,SAAS,OAAO,EAAE,SAAS,IAAI,GAAG;AAE9C;UACxB;AACoB,gBAAM,gBAAgB;AACtB,gBAAM,gBAAgB,KAAK,UAAU,aAAa;AAClD,gBAAM,gBAAgB,UAAU,aAAa;AAG7C,oBAAU,aAAa,IAAI,IAAIA,UAAS;AACpC,gBAAI,MAAM,cAAc,MAAM,WAAWA,KAAI;AAC7C,gBAAI,QAAQ,OAAO;AACf,oBAAM,cAAc,MAAM,WAAWA,KAAI;YACrE;AACwB,mBAAO;UAC/B;QACA;AACgB,aAAK,YAAY;MACjC;AAEY,UAAI,KAAK,OAAO;AACZ,cAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAM;AAC/C,mBAAW,QAAQ,KAAK,OAAO;AAC3B,cAAI,EAAE,QAAQ,QAAQ;AAClB,kBAAM,IAAI,MAAM,SAAS,IAAI,kBAAkB;UACvE;AACoB,cAAI,CAAC,WAAW,OAAO,EAAE,SAAS,IAAI,GAAG;AAErC;UACxB;AACoB,gBAAM,YAAY;AAClB,gBAAM,YAAY,KAAK,MAAM,SAAS;AACtC,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,OAAO,iBAAiB,IAAI,IAAI,GAAG;AAEnC,kBAAM,SAAS,IAAI,CAAC,QAAQ;AACxB,kBAAI,KAAK,SAAS,OAAO;AACrB,uBAAO,QAAQ,QAAQ,UAAU,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,CAAAC,SAAO;AAC3D,yBAAO,SAAS,KAAK,OAAOA,IAAG;gBACnE,CAAiC;cACjC;AAC4B,oBAAM,MAAM,UAAU,KAAK,OAAO,GAAG;AACrC,qBAAO,SAAS,KAAK,OAAO,GAAG;YAC3D;UACA,OACyB;AAED,kBAAM,SAAS,IAAI,IAAID,UAAS;AAC5B,kBAAI,MAAM,UAAU,MAAM,OAAOA,KAAI;AACrC,kBAAI,QAAQ,OAAO;AACf,sBAAM,SAAS,MAAM,OAAOA,KAAI;cAChE;AAC4B,qBAAO;YACnC;UACA;QACA;AACgB,aAAK,QAAQ;MAC7B;AAEY,UAAI,KAAK,YAAY;AACjB,cAAME,cAAa,KAAK,SAAS;AACjC,cAAM,iBAAiB,KAAK;AAC5B,aAAK,aAAa,SAAU,OAAO;AAC/B,cAAI,SAAS,CAAA;AACb,iBAAO,KAAK,eAAe,KAAK,MAAM,KAAK,CAAC;AAC5C,cAAIA,aAAY;AACZ,qBAAS,OAAO,OAAOA,YAAW,KAAK,MAAM,KAAK,CAAC;UAC3E;AACoB,iBAAO;QAC3B;MACA;AACY,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,KAAI;IACvD,CAAS;AACD,WAAO;EACf;EACI,WAAW,KAAK;AACZ,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,IAAG;AAC1C,WAAO;EACf;EACI,MAAM,KAAKR,UAAS;AAChB,WAAO,OAAO,IAAI,KAAKA,YAAW,KAAK,QAAQ;EACvD;EACI,OAAO,QAAQA,UAAS;AACpB,WAAO,QAAQ,MAAM,QAAQA,YAAW,KAAK,QAAQ;EAC7D;EACI,cAAc,WAAW;AAErB,UAAMS,SAAQ,CAAC,KAAKT,aAAY;AAC5B,YAAM,UAAU,EAAE,GAAGA,SAAO;AAC5B,YAAM,MAAM,EAAE,GAAG,KAAK,UAAU,GAAG,QAAO;AAC1C,YAAM,aAAa,KAAK,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK;AAEzD,UAAI,KAAK,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACzD,eAAO,WAAW,IAAI,MAAM,oIAAoI,CAAC;MACjL;AAEY,UAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC5C,eAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;MAC7F;AACY,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,WAAW,IAAI,MAAM,0CACtB,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;MAChF;AACY,UAAI,IAAI,OAAO;AACX,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,QAAQ;MAClC;AACY,YAAMD,SAAQ,IAAI,QAAQ,IAAI,MAAM,aAAY,IAAM,YAAY,OAAO,MAAM,OAAO;AACtF,YAAMK,UAAS,IAAI,QAAQ,IAAI,MAAM,cAAa,IAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC5F,UAAI,IAAI,OAAO;AACX,eAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAC7D,KAAK,CAAAM,SAAOX,OAAMW,MAAK,GAAG,CAAC,EAC3B,KAAK,YAAU,IAAI,QAAQ,IAAI,MAAM,iBAAiB,MAAM,IAAI,MAAM,EACtE,KAAK,YAAU,IAAI,aAAa,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAChH,KAAK,YAAUN,QAAO,QAAQ,GAAG,CAAC,EAClC,KAAK,CAAAP,UAAQ,IAAI,QAAQ,IAAI,MAAM,YAAYA,KAAI,IAAIA,KAAI,EAC3D,MAAM,UAAU;MACrC;AACY,UAAI;AACA,YAAI,IAAI,OAAO;AACX,gBAAM,IAAI,MAAM,WAAW,GAAG;QAClD;AACgB,YAAI,SAASE,OAAM,KAAK,GAAG;AAC3B,YAAI,IAAI,OAAO;AACX,mBAAS,IAAI,MAAM,iBAAiB,MAAM;QAC9D;AACgB,YAAI,IAAI,YAAY;AAChB,eAAK,WAAW,QAAQ,IAAI,UAAU;QAC1D;AACgB,YAAIF,QAAOO,QAAO,QAAQ,GAAG;AAC7B,YAAI,IAAI,OAAO;AACX,UAAAP,QAAO,IAAI,MAAM,YAAYA,KAAI;QACrD;AACgB,eAAOA;MACvB,SACmB,GAAG;AACN,eAAO,WAAW,CAAC;MACnC;IACA;AACQ,WAAOY;EACf;EACI,QAAQ,QAAQ,OAAO;AACnB,WAAO,CAAC,MAAM;AACV,QAAE,WAAW;AACb,UAAI,QAAQ;AACR,cAAM,MAAM,mCACN,OAAO,EAAE,UAAU,IAAI,IAAI,IAC3B;AACN,YAAI,OAAO;AACP,iBAAO,QAAQ,QAAQ,GAAG;QAC9C;AACgB,eAAO;MACvB;AACY,UAAI,OAAO;AACP,eAAO,QAAQ,OAAO,CAAC;MACvC;AACY,YAAM;IAClB;EACA;AACA;ACtTA,IAAM,iBAAiB,IAAI,OAAM;AAC1B,SAAS,OAAO,KAAK,KAAK;AAC7B,SAAO,eAAe,MAAM,KAAK,GAAG;AACxC;AAMA,OAAO,UACH,OAAO,aAAa,SAAUT,UAAS;AACnC,iBAAe,WAAWA,QAAO;AACjC,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACf;AAIA,OAAO,cAAc;AACrB,OAAO,WAAW;AAIlB,OAAO,MAAM,YAAa,MAAM;AAC5B,iBAAe,IAAI,GAAG,IAAI;AAC1B,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACX;AAIA,OAAO,aAAa,SAAU,QAAQ,UAAU;AAC5C,SAAO,eAAe,WAAW,QAAQ,QAAQ;AACrD;AAQA,OAAO,cAAc,eAAe;AAIpC,OAAO,SAAS;AAChB,OAAO,SAAS,QAAQ;AACxB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,OAAO;AACtB,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACH,IAAC,UAAU,OAAO;AAClB,IAAC,aAAa,OAAO;AACrB,IAAC,MAAM,OAAO;AACd,IAAC,aAAa,OAAO;AACrB,IAAC,cAAc,OAAO;AACtB,IAAC,QAAQ;AACT,IAAC,SAAS,QAAQ;AAClB,IAAC,QAAQ,OAAO;", "names": ["escape", "html", "link", "lexer", "options", "list", "tag", "text", "parser", "tokens", "args", "ret", "walkTokens", "parse", "src"]}