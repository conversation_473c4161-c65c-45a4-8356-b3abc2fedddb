{"version": 3, "sources": ["../../.pnpm/svelte@5.27.0/node_modules/svelte/src/animate/index.js"], "sourcesContent": ["/** @import { FlipParams, AnimationConfig } from './public.js' */\nimport { cubicOut } from '../easing/index.js';\n\n/**\n * The flip function calculates the start and end position of an element and animates between them, translating the x and y values.\n * `flip` stands for [First, Last, Invert, Play](https://aerotwist.com/blog/flip-your-animations/).\n *\n * @param {Element} node\n * @param {{ from: DOMRect; to: DOMRect }} fromTo\n * @param {FlipParams} params\n * @returns {AnimationConfig}\n */\nexport function flip(node, { from, to }, params = {}) {\n\tvar { delay = 0, duration = (d) => Math.sqrt(d) * 120, easing = cubicOut } = params;\n\n\tvar style = getComputedStyle(node);\n\n\t// find the transform origin, expressed as a pair of values between 0 and 1\n\tvar transform = style.transform === 'none' ? '' : style.transform;\n\tvar [ox, oy] = style.transformOrigin.split(' ').map(parseFloat);\n\tox /= node.clientWidth;\n\toy /= node.clientHeight;\n\n\t// calculate effect of parent transforms and zoom\n\tvar zoom = get_zoom(node); // https://drafts.csswg.org/css-viewport/#effective-zoom\n\tvar sx = node.clientWidth / to.width / zoom;\n\tvar sy = node.clientHeight / to.height / zoom;\n\n\t// find the starting position of the transform origin\n\tvar fx = from.left + from.width * ox;\n\tvar fy = from.top + from.height * oy;\n\n\t// find the ending position of the transform origin\n\tvar tx = to.left + to.width * ox;\n\tvar ty = to.top + to.height * oy;\n\n\t// find the translation at the start of the transform\n\tvar dx = (fx - tx) * sx;\n\tvar dy = (fy - ty) * sy;\n\n\t// find the relative scale at the start of the transform\n\tvar dsx = from.width / to.width;\n\tvar dsy = from.height / to.height;\n\n\treturn {\n\t\tdelay,\n\t\tduration: typeof duration === 'function' ? duration(Math.sqrt(dx * dx + dy * dy)) : duration,\n\t\teasing,\n\t\tcss: (t, u) => {\n\t\t\tvar x = u * dx;\n\t\t\tvar y = u * dy;\n\t\t\tvar sx = t + u * dsx;\n\t\t\tvar sy = t + u * dsy;\n\n\t\t\treturn `transform: ${transform} translate(${x}px, ${y}px) scale(${sx}, ${sy});`;\n\t\t}\n\t};\n}\n\n/**\n * @param {Element} element\n */\nfunction get_zoom(element) {\n\tif ('currentCSSZoom' in element) {\n\t\treturn /** @type {number} */ (element.currentCSSZoom);\n\t}\n\n\t/** @type {Element | null} */\n\tvar current = element;\n\tvar zoom = 1;\n\n\twhile (current !== null) {\n\t\tzoom *= +getComputedStyle(current).zoom;\n\t\tcurrent = /** @type {Element | null} */ (current.parentElement);\n\t}\n\n\treturn zoom;\n}\n"], "mappings": ";;;;;;AAYO,SAAS,KAAK,MAAM,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG;AACrD,MAAI,EAAE,QAAQ,GAAG,WAAW,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,SAAS,SAAS,IAAI;AAE7E,MAAI,QAAQ,iBAAiB,IAAI;AAGjC,MAAI,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AACxD,MAAI,CAAC,IAAI,EAAE,IAAI,MAAM,gBAAgB,MAAM,GAAG,EAAE,IAAI,UAAU;AAC9D,QAAM,KAAK;AACX,QAAM,KAAK;AAGX,MAAI,OAAO,SAAS,IAAI;AACxB,MAAI,KAAK,KAAK,cAAc,GAAG,QAAQ;AACvC,MAAI,KAAK,KAAK,eAAe,GAAG,SAAS;AAGzC,MAAI,KAAK,KAAK,OAAO,KAAK,QAAQ;AAClC,MAAI,KAAK,KAAK,MAAM,KAAK,SAAS;AAGlC,MAAI,KAAK,GAAG,OAAO,GAAG,QAAQ;AAC9B,MAAI,KAAK,GAAG,MAAM,GAAG,SAAS;AAG9B,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,MAAM,KAAK,MAAM;AAGrB,MAAI,MAAM,KAAK,QAAQ,GAAG;AAC1B,MAAI,MAAM,KAAK,SAAS,GAAG;AAE3B,SAAO;AAAA,IACN;AAAA,IACA,UAAU,OAAO,aAAa,aAAa,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,IAAI;AAAA,IACpF;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AACd,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAIA,MAAK,IAAI,IAAI;AACjB,UAAIC,MAAK,IAAI,IAAI;AAEjB,aAAO,cAAc,SAAS,cAAc,CAAC,OAAO,CAAC,aAAaD,GAAE,KAAKC,GAAE;AAAA,IAC5E;AAAA,EACD;AACD;AAKA,SAAS,SAAS,SAAS;AAC1B,MAAI,oBAAoB,SAAS;AAChC;AAAA;AAAA,MAA8B,QAAQ;AAAA;AAAA,EACvC;AAGA,MAAI,UAAU;AACd,MAAI,OAAO;AAEX,SAAO,YAAY,MAAM;AACxB,YAAQ,CAAC,iBAAiB,OAAO,EAAE;AACnC;AAAA,IAAyC,QAAQ;AAAA,EAClD;AAEA,SAAO;AACR;", "names": ["sx", "sy"]}