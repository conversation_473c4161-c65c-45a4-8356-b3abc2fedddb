import {
  add_locations,
  check_target,
  each,
  hmr,
  html,
  if_block,
  legacy_api,
  prop,
  set_attribute,
  set_attributes,
  validate_each_keys
} from "./chunk-H224UGWD.js";
import {
  append,
  comment,
  head,
  template
} from "./chunk-QCRQRFT2.js";
import {
  $document,
  FILENAME,
  HMR,
  first_child,
  get,
  pop,
  push,
  replay_events,
  set,
  sibling,
  state,
  strict_equals,
  template_effect,
  user_derived,
  user_effect
} from "./chunk-QMEREK2J.js";
import "./chunk-ZSBB7BNI.js";
import "./chunk-3LUZOQLA.js";
import "./chunk-6B4FAQTZ.js";
import "./chunk-4VAFAIFS.js";
import "./chunk-B4Q33VKO.js";

// node_modules/.pnpm/svelte-meta-tags@4.3.0_svelte@5.27.0/node_modules/svelte-meta-tags/dist/MetaTags.svelte
MetaTags[FILENAME] = "node_modules/.pnpm/svelte-meta-tags@4.3.0_svelte@5.27.0/node_modules/svelte-meta-tags/dist/MetaTags.svelte";
var root_3 = add_locations(template(`<meta name="robots">`), MetaTags[FILENAME], [[56, 4]]);
var root_4 = add_locations(template(`<meta name="description">`), MetaTags[FILENAME], [[60, 4]]);
var root_5 = add_locations(template(`<link rel="canonical">`), MetaTags[FILENAME], [[64, 4]]);
var root_6 = add_locations(template(`<meta name="keywords">`), MetaTags[FILENAME], [[68, 4]]);
var root_7 = add_locations(template(`<link rel="alternate">`), MetaTags[FILENAME], [[72, 4]]);
var root_9 = add_locations(template(`<link rel="alternate">`), MetaTags[FILENAME], [[77, 6]]);
var root_11 = add_locations(template(`<meta name="twitter:card">`), MetaTags[FILENAME], [[83, 6]]);
var root_12 = add_locations(template(`<meta name="twitter:site">`), MetaTags[FILENAME], [[86, 6]]);
var root_13 = add_locations(template(`<meta name="twitter:title">`), MetaTags[FILENAME], [[89, 6]]);
var root_14 = add_locations(template(`<meta name="twitter:description">`), MetaTags[FILENAME], [[92, 6]]);
var root_15 = add_locations(template(`<meta name="twitter:creator">`), MetaTags[FILENAME], [[95, 6]]);
var root_16 = add_locations(template(`<meta name="twitter:creator:id">`), MetaTags[FILENAME], [[98, 6]]);
var root_17 = add_locations(template(`<meta name="twitter:image">`), MetaTags[FILENAME], [[101, 6]]);
var root_18 = add_locations(template(`<meta name="twitter:image:alt">`), MetaTags[FILENAME], [[104, 6]]);
var root_19 = add_locations(template(`<meta name="twitter:player">`), MetaTags[FILENAME], [[107, 6]]);
var root_20 = add_locations(template(`<meta name="twitter:player:width">`), MetaTags[FILENAME], [[110, 6]]);
var root_21 = add_locations(template(`<meta name="twitter:player:height">`), MetaTags[FILENAME], [[113, 6]]);
var root_22 = add_locations(template(`<meta name="twitter:player:stream">`), MetaTags[FILENAME], [[116, 6]]);
var root_23 = add_locations(template(`<meta name="twitter:app:name:iphone">`), MetaTags[FILENAME], [[119, 6]]);
var root_24 = add_locations(template(`<meta name="twitter:app:id:iphone">`), MetaTags[FILENAME], [[122, 6]]);
var root_25 = add_locations(template(`<meta name="twitter:app:url:iphone">`), MetaTags[FILENAME], [[125, 6]]);
var root_26 = add_locations(template(`<meta name="twitter:app:name:ipad">`), MetaTags[FILENAME], [[128, 6]]);
var root_27 = add_locations(template(`<meta name="twitter:app:id:ipad">`), MetaTags[FILENAME], [[131, 6]]);
var root_28 = add_locations(template(`<meta name="twitter:app:url:ipad">`), MetaTags[FILENAME], [[134, 6]]);
var root_29 = add_locations(template(`<meta name="twitter:app:name:googleplay">`), MetaTags[FILENAME], [[137, 6]]);
var root_30 = add_locations(template(`<meta name="twitter:app:id:googleplay">`), MetaTags[FILENAME], [[140, 6]]);
var root_31 = add_locations(template(`<meta name="twitter:app:url:googleplay">`), MetaTags[FILENAME], [[143, 6]]);
var root_10 = add_locations(template(`<!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
var root_32 = add_locations(template(`<meta property="fb:app_id">`), MetaTags[FILENAME], [[148, 4]]);
var root_34 = add_locations(template(`<meta property="og:url">`), MetaTags[FILENAME], [[153, 6]]);
var root_37 = add_locations(template(`<meta property="profile:first_name">`), MetaTags[FILENAME], [[160, 10]]);
var root_38 = add_locations(template(`<meta property="profile:last_name">`), MetaTags[FILENAME], [[164, 10]]);
var root_39 = add_locations(template(`<meta property="profile:username">`), MetaTags[FILENAME], [[168, 10]]);
var root_40 = add_locations(template(`<meta property="profile:gender">`), MetaTags[FILENAME], [[172, 10]]);
var root_36 = add_locations(template(`<!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
var root_44 = add_locations(template(`<meta property="book:author">`), MetaTags[FILENAME], [[177, 12]]);
var root_45 = add_locations(template(`<meta property="book:isbn">`), MetaTags[FILENAME], [[182, 10]]);
var root_46 = add_locations(template(`<meta property="book:release_date">`), MetaTags[FILENAME], [[186, 10]]);
var root_48 = add_locations(template(`<meta property="book:tag">`), MetaTags[FILENAME], [[191, 12]]);
var root_42 = add_locations(template(`<!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
var root_51 = add_locations(template(`<meta property="article:published_time">`), MetaTags[FILENAME], [[196, 10]]);
var root_52 = add_locations(template(`<meta property="article:modified_time">`), MetaTags[FILENAME], [[200, 10]]);
var root_53 = add_locations(template(`<meta property="article:expiration_time">`), MetaTags[FILENAME], [[204, 10]]);
var root_55 = add_locations(template(`<meta property="article:author">`), MetaTags[FILENAME], [[209, 12]]);
var root_56 = add_locations(template(`<meta property="article:section">`), MetaTags[FILENAME], [[214, 10]]);
var root_58 = add_locations(template(`<meta property="article:tag">`), MetaTags[FILENAME], [[219, 12]]);
var root_50 = add_locations(template(`<!> <!> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
var root_63 = add_locations(template(`<meta property="video:actor">`), MetaTags[FILENAME], [[226, 14]]);
var root_64 = add_locations(template(`<meta property="video:actor:role">`), MetaTags[FILENAME], [[229, 14]]);
var root_62 = add_locations(template(`<!> <!>`, 1), MetaTags[FILENAME], []);
var root_66 = add_locations(template(`<meta property="video:director">`), MetaTags[FILENAME], [[236, 12]]);
var root_68 = add_locations(template(`<meta property="video:writer">`), MetaTags[FILENAME], [[242, 12]]);
var root_69 = add_locations(template(`<meta property="video:duration">`), MetaTags[FILENAME], [[247, 10]]);
var root_70 = add_locations(template(`<meta property="video:release_date">`), MetaTags[FILENAME], [[251, 10]]);
var root_72 = add_locations(template(`<meta property="video:tag">`), MetaTags[FILENAME], [[256, 12]]);
var root_73 = add_locations(template(`<meta property="video:series">`), MetaTags[FILENAME], [[261, 10]]);
var root_60 = add_locations(template(`<!> <!> <!> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
var root_35 = add_locations(template(`<meta property="og:type"> <!>`, 1), MetaTags[FILENAME], [[157, 6]]);
var root_74 = add_locations(template(`<meta property="og:title">`), MetaTags[FILENAME], [[267, 6]]);
var root_75 = add_locations(template(`<meta property="og:description">`), MetaTags[FILENAME], [[271, 6]]);
var root_78 = add_locations(template(`<meta property="og:image:alt">`), MetaTags[FILENAME], [[278, 10]]);
var root_79 = add_locations(template(`<meta property="og:image:width">`), MetaTags[FILENAME], [[281, 10]]);
var root_80 = add_locations(template(`<meta property="og:image:height">`), MetaTags[FILENAME], [[284, 10]]);
var root_81 = add_locations(template(`<meta property="og:image:secure_url">`), MetaTags[FILENAME], [[287, 10]]);
var root_82 = add_locations(template(`<meta property="og:image:type">`), MetaTags[FILENAME], [[290, 10]]);
var root_77 = add_locations(template(`<meta property="og:image"> <!> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], [[276, 8]]);
var root_85 = add_locations(template(`<meta property="og:video:width">`), MetaTags[FILENAME], [[299, 10]]);
var root_86 = add_locations(template(`<meta property="og:video:height">`), MetaTags[FILENAME], [[302, 10]]);
var root_87 = add_locations(template(`<meta property="og:video:secure_url">`), MetaTags[FILENAME], [[305, 10]]);
var root_88 = add_locations(template(`<meta property="og:video:type">`), MetaTags[FILENAME], [[308, 10]]);
var root_84 = add_locations(template(`<meta property="og:video"> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], [[297, 8]]);
var root_91 = add_locations(template(`<meta property="og:audio:secure_url">`), MetaTags[FILENAME], [[317, 10]]);
var root_92 = add_locations(template(`<meta property="og:audio:type">`), MetaTags[FILENAME], [[320, 10]]);
var root_90 = add_locations(template(`<meta property="og:audio"> <!> <!>`, 1), MetaTags[FILENAME], [[315, 8]]);
var root_93 = add_locations(template(`<meta property="og:locale">`), MetaTags[FILENAME], [[326, 6]]);
var root_94 = add_locations(template(`<meta property="og:site_name">`), MetaTags[FILENAME], [[330, 6]]);
var root_33 = add_locations(template(`<!> <!> <!> <!> <!> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
var root_96 = add_locations(template(`<meta>`), MetaTags[FILENAME], [[336, 6]]);
var root_98 = add_locations(template(`<link>`), MetaTags[FILENAME], [[342, 6]]);
var root_1 = add_locations(template(`<!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!> <!>`, 1), MetaTags[FILENAME], []);
function MetaTags($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, MetaTags);
  let title = prop($$props, "title", 3, void 0), titleTemplate = prop($$props, "titleTemplate", 3, void 0), robots = prop($$props, "robots", 3, "index,follow"), additionalRobotsProps = prop($$props, "additionalRobotsProps", 3, void 0), description = prop($$props, "description", 3, void 0), mobileAlternate = prop($$props, "mobileAlternate", 3, void 0), languageAlternates = prop($$props, "languageAlternates", 3, void 0), twitter = prop($$props, "twitter", 3, void 0), facebook = prop($$props, "facebook", 3, void 0), openGraph = prop($$props, "openGraph", 3, void 0), canonical = prop($$props, "canonical", 3, void 0), keywords = prop($$props, "keywords", 3, void 0), additionalMetaTags = prop($$props, "additionalMetaTags", 3, void 0), additionalLinkTags = prop($$props, "additionalLinkTags", 3, void 0);
  let updatedTitle = user_derived(() => titleTemplate() ? title() ? titleTemplate().replace(/%s/g, title()) : title() : title());
  let robotsParams = state("");
  if (additionalRobotsProps()) {
    const {
      nosnippet,
      maxSnippet,
      maxImagePreview,
      maxVideoPreview,
      noarchive,
      noimageindex,
      notranslate,
      unavailableAfter
    } = additionalRobotsProps();
    set(robotsParams, `${nosnippet ? ",nosnippet" : ""}${maxSnippet ? `,max-snippet:${maxSnippet}` : ""}${maxImagePreview ? `,max-image-preview:${maxImagePreview}` : ""}${noarchive ? ",noarchive" : ""}${unavailableAfter ? `,unavailable_after:${unavailableAfter}` : ""}${noimageindex ? ",noimageindex" : ""}${maxVideoPreview ? `,max-video-preview:${maxVideoPreview}` : ""}${notranslate ? ",notranslate" : ""}`);
  }
  user_effect(() => {
    if (!robots() && additionalRobotsProps()) {
      console.warn("additionalRobotsProps cannot be used when robots is set to false");
    }
  });
  head(($$anchor2) => {
    var fragment = root_1();
    var node = first_child(fragment);
    {
      var consequent = ($$anchor3) => {
        template_effect(() => $document.title = get(updatedTitle));
      };
      if_block(node, ($$render) => {
        if (get(updatedTitle)) $$render(consequent);
      });
    }
    var node_1 = sibling(node, 2);
    {
      var consequent_1 = ($$anchor3) => {
        var meta = root_3();
        template_effect(() => set_attribute(meta, "content", `${robots() ?? ""}${get(robotsParams) ?? ""}`));
        append($$anchor3, meta);
      };
      if_block(node_1, ($$render) => {
        if (strict_equals(robots(), false, false)) $$render(consequent_1);
      });
    }
    var node_2 = sibling(node_1, 2);
    {
      var consequent_2 = ($$anchor3) => {
        var meta_1 = root_4();
        template_effect(() => set_attribute(meta_1, "content", description()));
        append($$anchor3, meta_1);
      };
      if_block(node_2, ($$render) => {
        if (description()) $$render(consequent_2);
      });
    }
    var node_3 = sibling(node_2, 2);
    {
      var consequent_3 = ($$anchor3) => {
        var link = root_5();
        template_effect(() => set_attribute(link, "href", canonical()));
        append($$anchor3, link);
      };
      if_block(node_3, ($$render) => {
        if (canonical()) $$render(consequent_3);
      });
    }
    var node_4 = sibling(node_3, 2);
    {
      var consequent_4 = ($$anchor3) => {
        var meta_2 = root_6();
        template_effect(($0) => set_attribute(meta_2, "content", $0), [() => keywords().join(", ")]);
        append($$anchor3, meta_2);
      };
      if_block(node_4, ($$render) => {
        var _a;
        if ((_a = keywords()) == null ? void 0 : _a.length) $$render(consequent_4);
      });
    }
    var node_5 = sibling(node_4, 2);
    {
      var consequent_5 = ($$anchor3) => {
        var link_1 = root_7();
        template_effect(() => {
          set_attribute(link_1, "media", mobileAlternate().media);
          set_attribute(link_1, "href", mobileAlternate().href);
        });
        append($$anchor3, link_1);
      };
      if_block(node_5, ($$render) => {
        if (mobileAlternate()) $$render(consequent_5);
      });
    }
    var node_6 = sibling(node_5, 2);
    {
      var consequent_6 = ($$anchor3) => {
        var fragment_1 = comment();
        var node_7 = first_child(fragment_1);
        validate_each_keys(languageAlternates, (languageAlternate) => languageAlternate);
        each(node_7, 16, languageAlternates, (languageAlternate) => languageAlternate, ($$anchor4, languageAlternate) => {
          var link_2 = root_9();
          template_effect(() => {
            set_attribute(link_2, "hreflang", languageAlternate.hrefLang);
            set_attribute(link_2, "href", languageAlternate.href);
          });
          append($$anchor4, link_2);
        });
        append($$anchor3, fragment_1);
      };
      if_block(node_6, ($$render) => {
        if (languageAlternates() && languageAlternates().length > 0) $$render(consequent_6);
      });
    }
    var node_8 = sibling(node_6, 2);
    {
      var consequent_28 = ($$anchor3) => {
        var fragment_2 = root_10();
        var node_9 = first_child(fragment_2);
        {
          var consequent_7 = ($$anchor4) => {
            var meta_3 = root_11();
            template_effect(() => set_attribute(meta_3, "content", twitter().cardType));
            append($$anchor4, meta_3);
          };
          if_block(node_9, ($$render) => {
            if (twitter().cardType) $$render(consequent_7);
          });
        }
        var node_10 = sibling(node_9, 2);
        {
          var consequent_8 = ($$anchor4) => {
            var meta_4 = root_12();
            template_effect(() => set_attribute(meta_4, "content", twitter().site));
            append($$anchor4, meta_4);
          };
          if_block(node_10, ($$render) => {
            if (twitter().site) $$render(consequent_8);
          });
        }
        var node_11 = sibling(node_10, 2);
        {
          var consequent_9 = ($$anchor4) => {
            var meta_5 = root_13();
            template_effect(() => set_attribute(meta_5, "content", twitter().title));
            append($$anchor4, meta_5);
          };
          if_block(node_11, ($$render) => {
            if (twitter().title) $$render(consequent_9);
          });
        }
        var node_12 = sibling(node_11, 2);
        {
          var consequent_10 = ($$anchor4) => {
            var meta_6 = root_14();
            template_effect(() => set_attribute(meta_6, "content", twitter().description));
            append($$anchor4, meta_6);
          };
          if_block(node_12, ($$render) => {
            if (twitter().description) $$render(consequent_10);
          });
        }
        var node_13 = sibling(node_12, 2);
        {
          var consequent_11 = ($$anchor4) => {
            var meta_7 = root_15();
            template_effect(() => set_attribute(meta_7, "content", twitter().creator));
            append($$anchor4, meta_7);
          };
          if_block(node_13, ($$render) => {
            if (twitter().creator) $$render(consequent_11);
          });
        }
        var node_14 = sibling(node_13, 2);
        {
          var consequent_12 = ($$anchor4) => {
            var meta_8 = root_16();
            template_effect(() => set_attribute(meta_8, "content", twitter().creatorId));
            append($$anchor4, meta_8);
          };
          if_block(node_14, ($$render) => {
            if (twitter().creatorId) $$render(consequent_12);
          });
        }
        var node_15 = sibling(node_14, 2);
        {
          var consequent_13 = ($$anchor4) => {
            var meta_9 = root_17();
            template_effect(() => set_attribute(meta_9, "content", twitter().image));
            append($$anchor4, meta_9);
          };
          if_block(node_15, ($$render) => {
            if (twitter().image) $$render(consequent_13);
          });
        }
        var node_16 = sibling(node_15, 2);
        {
          var consequent_14 = ($$anchor4) => {
            var meta_10 = root_18();
            template_effect(() => set_attribute(meta_10, "content", twitter().imageAlt));
            append($$anchor4, meta_10);
          };
          if_block(node_16, ($$render) => {
            if (twitter().imageAlt) $$render(consequent_14);
          });
        }
        var node_17 = sibling(node_16, 2);
        {
          var consequent_15 = ($$anchor4) => {
            var meta_11 = root_19();
            template_effect(() => set_attribute(meta_11, "content", twitter().player));
            append($$anchor4, meta_11);
          };
          if_block(node_17, ($$render) => {
            if (twitter().player) $$render(consequent_15);
          });
        }
        var node_18 = sibling(node_17, 2);
        {
          var consequent_16 = ($$anchor4) => {
            var meta_12 = root_20();
            template_effect(($0) => set_attribute(meta_12, "content", $0), [() => twitter().playerWidth.toString()]);
            append($$anchor4, meta_12);
          };
          if_block(node_18, ($$render) => {
            if (twitter().playerWidth) $$render(consequent_16);
          });
        }
        var node_19 = sibling(node_18, 2);
        {
          var consequent_17 = ($$anchor4) => {
            var meta_13 = root_21();
            template_effect(($0) => set_attribute(meta_13, "content", $0), [
              () => twitter().playerHeight.toString()
            ]);
            append($$anchor4, meta_13);
          };
          if_block(node_19, ($$render) => {
            if (twitter().playerHeight) $$render(consequent_17);
          });
        }
        var node_20 = sibling(node_19, 2);
        {
          var consequent_18 = ($$anchor4) => {
            var meta_14 = root_22();
            template_effect(() => set_attribute(meta_14, "content", twitter().playerStream));
            append($$anchor4, meta_14);
          };
          if_block(node_20, ($$render) => {
            if (twitter().playerStream) $$render(consequent_18);
          });
        }
        var node_21 = sibling(node_20, 2);
        {
          var consequent_19 = ($$anchor4) => {
            var meta_15 = root_23();
            template_effect(() => set_attribute(meta_15, "content", twitter().appNameIphone));
            append($$anchor4, meta_15);
          };
          if_block(node_21, ($$render) => {
            if (twitter().appNameIphone) $$render(consequent_19);
          });
        }
        var node_22 = sibling(node_21, 2);
        {
          var consequent_20 = ($$anchor4) => {
            var meta_16 = root_24();
            template_effect(() => set_attribute(meta_16, "content", twitter().appIdIphone));
            append($$anchor4, meta_16);
          };
          if_block(node_22, ($$render) => {
            if (twitter().appIdIphone) $$render(consequent_20);
          });
        }
        var node_23 = sibling(node_22, 2);
        {
          var consequent_21 = ($$anchor4) => {
            var meta_17 = root_25();
            template_effect(() => set_attribute(meta_17, "content", twitter().appUrlIphone));
            append($$anchor4, meta_17);
          };
          if_block(node_23, ($$render) => {
            if (twitter().appUrlIphone) $$render(consequent_21);
          });
        }
        var node_24 = sibling(node_23, 2);
        {
          var consequent_22 = ($$anchor4) => {
            var meta_18 = root_26();
            template_effect(() => set_attribute(meta_18, "content", twitter().appNameIpad));
            append($$anchor4, meta_18);
          };
          if_block(node_24, ($$render) => {
            if (twitter().appNameIpad) $$render(consequent_22);
          });
        }
        var node_25 = sibling(node_24, 2);
        {
          var consequent_23 = ($$anchor4) => {
            var meta_19 = root_27();
            template_effect(() => set_attribute(meta_19, "content", twitter().appIdIpad));
            append($$anchor4, meta_19);
          };
          if_block(node_25, ($$render) => {
            if (twitter().appIdIpad) $$render(consequent_23);
          });
        }
        var node_26 = sibling(node_25, 2);
        {
          var consequent_24 = ($$anchor4) => {
            var meta_20 = root_28();
            template_effect(() => set_attribute(meta_20, "content", twitter().appUrlIpad));
            append($$anchor4, meta_20);
          };
          if_block(node_26, ($$render) => {
            if (twitter().appUrlIpad) $$render(consequent_24);
          });
        }
        var node_27 = sibling(node_26, 2);
        {
          var consequent_25 = ($$anchor4) => {
            var meta_21 = root_29();
            template_effect(() => set_attribute(meta_21, "content", twitter().appNameGoogleplay));
            append($$anchor4, meta_21);
          };
          if_block(node_27, ($$render) => {
            if (twitter().appNameGoogleplay) $$render(consequent_25);
          });
        }
        var node_28 = sibling(node_27, 2);
        {
          var consequent_26 = ($$anchor4) => {
            var meta_22 = root_30();
            template_effect(() => set_attribute(meta_22, "content", twitter().appIdGoogleplay));
            append($$anchor4, meta_22);
          };
          if_block(node_28, ($$render) => {
            if (twitter().appIdGoogleplay) $$render(consequent_26);
          });
        }
        var node_29 = sibling(node_28, 2);
        {
          var consequent_27 = ($$anchor4) => {
            var meta_23 = root_31();
            template_effect(() => set_attribute(meta_23, "content", twitter().appUrlGoogleplay));
            append($$anchor4, meta_23);
          };
          if_block(node_29, ($$render) => {
            if (twitter().appUrlGoogleplay) $$render(consequent_27);
          });
        }
        append($$anchor3, fragment_2);
      };
      if_block(node_8, ($$render) => {
        if (twitter()) $$render(consequent_28);
      });
    }
    var node_30 = sibling(node_8, 2);
    {
      var consequent_29 = ($$anchor3) => {
        var meta_24 = root_32();
        template_effect(() => set_attribute(meta_24, "content", facebook().appId));
        append($$anchor3, meta_24);
      };
      if_block(node_30, ($$render) => {
        if (facebook()) $$render(consequent_29);
      });
    }
    var node_31 = sibling(node_30, 2);
    {
      var consequent_77 = ($$anchor3) => {
        var fragment_3 = root_33();
        var node_32 = first_child(fragment_3);
        {
          var consequent_30 = ($$anchor4) => {
            var meta_25 = root_34();
            template_effect(() => set_attribute(meta_25, "content", openGraph().url || canonical()));
            append($$anchor4, meta_25);
          };
          if_block(node_32, ($$render) => {
            if (openGraph().url || canonical()) $$render(consequent_30);
          });
        }
        var node_33 = sibling(node_32, 2);
        {
          var consequent_58 = ($$anchor4) => {
            var fragment_4 = root_35();
            var meta_26 = first_child(fragment_4);
            var node_34 = sibling(meta_26, 2);
            {
              var consequent_35 = ($$anchor5) => {
                var fragment_5 = root_36();
                var node_35 = first_child(fragment_5);
                {
                  var consequent_31 = ($$anchor6) => {
                    var meta_27 = root_37();
                    template_effect(() => set_attribute(meta_27, "content", openGraph().profile.firstName));
                    append($$anchor6, meta_27);
                  };
                  if_block(node_35, ($$render) => {
                    if (openGraph().profile.firstName) $$render(consequent_31);
                  });
                }
                var node_36 = sibling(node_35, 2);
                {
                  var consequent_32 = ($$anchor6) => {
                    var meta_28 = root_38();
                    template_effect(() => set_attribute(meta_28, "content", openGraph().profile.lastName));
                    append($$anchor6, meta_28);
                  };
                  if_block(node_36, ($$render) => {
                    if (openGraph().profile.lastName) $$render(consequent_32);
                  });
                }
                var node_37 = sibling(node_36, 2);
                {
                  var consequent_33 = ($$anchor6) => {
                    var meta_29 = root_39();
                    template_effect(() => set_attribute(meta_29, "content", openGraph().profile.username));
                    append($$anchor6, meta_29);
                  };
                  if_block(node_37, ($$render) => {
                    if (openGraph().profile.username) $$render(consequent_33);
                  });
                }
                var node_38 = sibling(node_37, 2);
                {
                  var consequent_34 = ($$anchor6) => {
                    var meta_30 = root_40();
                    template_effect(() => set_attribute(meta_30, "content", openGraph().profile.gender));
                    append($$anchor6, meta_30);
                  };
                  if_block(node_38, ($$render) => {
                    if (openGraph().profile.gender) $$render(consequent_34);
                  });
                }
                append($$anchor5, fragment_5);
              };
              var alternate = ($$anchor5, $$elseif) => {
                {
                  var consequent_40 = ($$anchor6) => {
                    var fragment_6 = root_42();
                    var node_39 = first_child(fragment_6);
                    {
                      var consequent_36 = ($$anchor7) => {
                        var fragment_7 = comment();
                        var node_40 = first_child(fragment_7);
                        validate_each_keys(() => openGraph().book.authors, (author) => author);
                        each(node_40, 16, () => openGraph().book.authors, (author) => author, ($$anchor8, author) => {
                          var meta_31 = root_44();
                          template_effect(() => set_attribute(meta_31, "content", author));
                          append($$anchor8, meta_31);
                        });
                        append($$anchor7, fragment_7);
                      };
                      if_block(node_39, ($$render) => {
                        if (openGraph().book.authors && openGraph().book.authors.length) $$render(consequent_36);
                      });
                    }
                    var node_41 = sibling(node_39, 2);
                    {
                      var consequent_37 = ($$anchor7) => {
                        var meta_32 = root_45();
                        template_effect(() => set_attribute(meta_32, "content", openGraph().book.isbn));
                        append($$anchor7, meta_32);
                      };
                      if_block(node_41, ($$render) => {
                        if (openGraph().book.isbn) $$render(consequent_37);
                      });
                    }
                    var node_42 = sibling(node_41, 2);
                    {
                      var consequent_38 = ($$anchor7) => {
                        var meta_33 = root_46();
                        template_effect(() => set_attribute(meta_33, "content", openGraph().book.releaseDate));
                        append($$anchor7, meta_33);
                      };
                      if_block(node_42, ($$render) => {
                        if (openGraph().book.releaseDate) $$render(consequent_38);
                      });
                    }
                    var node_43 = sibling(node_42, 2);
                    {
                      var consequent_39 = ($$anchor7) => {
                        var fragment_8 = comment();
                        var node_44 = first_child(fragment_8);
                        validate_each_keys(() => openGraph().book.tags, (tag) => tag);
                        each(node_44, 16, () => openGraph().book.tags, (tag) => tag, ($$anchor8, tag) => {
                          var meta_34 = root_48();
                          template_effect(() => set_attribute(meta_34, "content", tag));
                          append($$anchor8, meta_34);
                        });
                        append($$anchor7, fragment_8);
                      };
                      if_block(node_43, ($$render) => {
                        if (openGraph().book.tags && openGraph().book.tags.length) $$render(consequent_39);
                      });
                    }
                    append($$anchor6, fragment_6);
                  };
                  var alternate_1 = ($$anchor6, $$elseif2) => {
                    {
                      var consequent_47 = ($$anchor7) => {
                        var fragment_9 = root_50();
                        var node_45 = first_child(fragment_9);
                        {
                          var consequent_41 = ($$anchor8) => {
                            var meta_35 = root_51();
                            template_effect(() => set_attribute(meta_35, "content", openGraph().article.publishedTime));
                            append($$anchor8, meta_35);
                          };
                          if_block(node_45, ($$render) => {
                            if (openGraph().article.publishedTime) $$render(consequent_41);
                          });
                        }
                        var node_46 = sibling(node_45, 2);
                        {
                          var consequent_42 = ($$anchor8) => {
                            var meta_36 = root_52();
                            template_effect(() => set_attribute(meta_36, "content", openGraph().article.modifiedTime));
                            append($$anchor8, meta_36);
                          };
                          if_block(node_46, ($$render) => {
                            if (openGraph().article.modifiedTime) $$render(consequent_42);
                          });
                        }
                        var node_47 = sibling(node_46, 2);
                        {
                          var consequent_43 = ($$anchor8) => {
                            var meta_37 = root_53();
                            template_effect(() => set_attribute(meta_37, "content", openGraph().article.expirationTime));
                            append($$anchor8, meta_37);
                          };
                          if_block(node_47, ($$render) => {
                            if (openGraph().article.expirationTime) $$render(consequent_43);
                          });
                        }
                        var node_48 = sibling(node_47, 2);
                        {
                          var consequent_44 = ($$anchor8) => {
                            var fragment_10 = comment();
                            var node_49 = first_child(fragment_10);
                            validate_each_keys(() => openGraph().article.authors, (author) => author);
                            each(node_49, 16, () => openGraph().article.authors, (author) => author, ($$anchor9, author) => {
                              var meta_38 = root_55();
                              template_effect(() => set_attribute(meta_38, "content", author));
                              append($$anchor9, meta_38);
                            });
                            append($$anchor8, fragment_10);
                          };
                          if_block(node_48, ($$render) => {
                            if (openGraph().article.authors && openGraph().article.authors.length) $$render(consequent_44);
                          });
                        }
                        var node_50 = sibling(node_48, 2);
                        {
                          var consequent_45 = ($$anchor8) => {
                            var meta_39 = root_56();
                            template_effect(() => set_attribute(meta_39, "content", openGraph().article.section));
                            append($$anchor8, meta_39);
                          };
                          if_block(node_50, ($$render) => {
                            if (openGraph().article.section) $$render(consequent_45);
                          });
                        }
                        var node_51 = sibling(node_50, 2);
                        {
                          var consequent_46 = ($$anchor8) => {
                            var fragment_11 = comment();
                            var node_52 = first_child(fragment_11);
                            validate_each_keys(() => openGraph().article.tags, (tag) => tag);
                            each(node_52, 16, () => openGraph().article.tags, (tag) => tag, ($$anchor9, tag) => {
                              var meta_40 = root_58();
                              template_effect(() => set_attribute(meta_40, "content", tag));
                              append($$anchor9, meta_40);
                            });
                            append($$anchor8, fragment_11);
                          };
                          if_block(node_51, ($$render) => {
                            if (openGraph().article.tags && openGraph().article.tags.length) $$render(consequent_46);
                          });
                        }
                        append($$anchor7, fragment_9);
                      };
                      var alternate_2 = ($$anchor7, $$elseif3) => {
                        {
                          var consequent_57 = ($$anchor8) => {
                            var fragment_12 = root_60();
                            var node_53 = first_child(fragment_12);
                            {
                              var consequent_50 = ($$anchor9) => {
                                var fragment_13 = comment();
                                var node_54 = first_child(fragment_13);
                                validate_each_keys(() => openGraph().video.actors, (actor) => actor);
                                each(node_54, 16, () => openGraph().video.actors, (actor) => actor, ($$anchor10, actor) => {
                                  var fragment_14 = root_62();
                                  var node_55 = first_child(fragment_14);
                                  {
                                    var consequent_48 = ($$anchor11) => {
                                      var meta_41 = root_63();
                                      template_effect(() => set_attribute(meta_41, "content", actor.profile));
                                      append($$anchor11, meta_41);
                                    };
                                    if_block(node_55, ($$render) => {
                                      if (actor.profile) $$render(consequent_48);
                                    });
                                  }
                                  var node_56 = sibling(node_55, 2);
                                  {
                                    var consequent_49 = ($$anchor11) => {
                                      var meta_42 = root_64();
                                      template_effect(() => set_attribute(meta_42, "content", actor.role));
                                      append($$anchor11, meta_42);
                                    };
                                    if_block(node_56, ($$render) => {
                                      if (actor.role) $$render(consequent_49);
                                    });
                                  }
                                  append($$anchor10, fragment_14);
                                });
                                append($$anchor9, fragment_13);
                              };
                              if_block(node_53, ($$render) => {
                                var _a;
                                if (((_a = openGraph().video) == null ? void 0 : _a.actors) && openGraph().video.actors.length) $$render(consequent_50);
                              });
                            }
                            var node_57 = sibling(node_53, 2);
                            {
                              var consequent_51 = ($$anchor9) => {
                                var fragment_15 = comment();
                                var node_58 = first_child(fragment_15);
                                validate_each_keys(() => openGraph().video.directors, (director) => director);
                                each(node_58, 16, () => openGraph().video.directors, (director) => director, ($$anchor10, director) => {
                                  var meta_43 = root_66();
                                  template_effect(() => set_attribute(meta_43, "content", director));
                                  append($$anchor10, meta_43);
                                });
                                append($$anchor9, fragment_15);
                              };
                              if_block(node_57, ($$render) => {
                                var _a;
                                if (((_a = openGraph().video) == null ? void 0 : _a.directors) && openGraph().video.directors.length) $$render(consequent_51);
                              });
                            }
                            var node_59 = sibling(node_57, 2);
                            {
                              var consequent_52 = ($$anchor9) => {
                                var fragment_16 = comment();
                                var node_60 = first_child(fragment_16);
                                validate_each_keys(() => openGraph().video.writers, (writer) => writer);
                                each(node_60, 16, () => openGraph().video.writers, (writer) => writer, ($$anchor10, writer) => {
                                  var meta_44 = root_68();
                                  template_effect(() => set_attribute(meta_44, "content", writer));
                                  append($$anchor10, meta_44);
                                });
                                append($$anchor9, fragment_16);
                              };
                              if_block(node_59, ($$render) => {
                                var _a;
                                if (((_a = openGraph().video) == null ? void 0 : _a.writers) && openGraph().video.writers.length) $$render(consequent_52);
                              });
                            }
                            var node_61 = sibling(node_59, 2);
                            {
                              var consequent_53 = ($$anchor9) => {
                                var meta_45 = root_69();
                                template_effect(($0) => set_attribute(meta_45, "content", $0), [
                                  () => openGraph().video.duration.toString()
                                ]);
                                append($$anchor9, meta_45);
                              };
                              if_block(node_61, ($$render) => {
                                var _a;
                                if ((_a = openGraph().video) == null ? void 0 : _a.duration) $$render(consequent_53);
                              });
                            }
                            var node_62 = sibling(node_61, 2);
                            {
                              var consequent_54 = ($$anchor9) => {
                                var meta_46 = root_70();
                                template_effect(() => set_attribute(meta_46, "content", openGraph().video.releaseDate));
                                append($$anchor9, meta_46);
                              };
                              if_block(node_62, ($$render) => {
                                var _a;
                                if ((_a = openGraph().video) == null ? void 0 : _a.releaseDate) $$render(consequent_54);
                              });
                            }
                            var node_63 = sibling(node_62, 2);
                            {
                              var consequent_55 = ($$anchor9) => {
                                var fragment_17 = comment();
                                var node_64 = first_child(fragment_17);
                                validate_each_keys(() => openGraph().video.tags, (tag) => tag);
                                each(node_64, 16, () => openGraph().video.tags, (tag) => tag, ($$anchor10, tag) => {
                                  var meta_47 = root_72();
                                  template_effect(() => set_attribute(meta_47, "content", tag));
                                  append($$anchor10, meta_47);
                                });
                                append($$anchor9, fragment_17);
                              };
                              if_block(node_63, ($$render) => {
                                var _a;
                                if (((_a = openGraph().video) == null ? void 0 : _a.tags) && openGraph().video.tags.length) $$render(consequent_55);
                              });
                            }
                            var node_65 = sibling(node_63, 2);
                            {
                              var consequent_56 = ($$anchor9) => {
                                var meta_48 = root_73();
                                template_effect(() => set_attribute(meta_48, "content", openGraph().video.series));
                                append($$anchor9, meta_48);
                              };
                              if_block(node_65, ($$render) => {
                                var _a;
                                if ((_a = openGraph().video) == null ? void 0 : _a.series) $$render(consequent_56);
                              });
                            }
                            append($$anchor8, fragment_12);
                          };
                          if_block(
                            $$anchor7,
                            ($$render) => {
                              if (strict_equals(openGraph().type.toLowerCase(), "video.movie") || strict_equals(openGraph().type.toLowerCase(), "video.episode") || strict_equals(openGraph().type.toLowerCase(), "video.tv_show") || strict_equals(openGraph().type.toLowerCase(), "video.other") && openGraph().video) $$render(consequent_57);
                            },
                            $$elseif3
                          );
                        }
                      };
                      if_block(
                        $$anchor6,
                        ($$render) => {
                          if (strict_equals(openGraph().type.toLowerCase(), "article") && openGraph().article) $$render(consequent_47);
                          else $$render(alternate_2, false);
                        },
                        $$elseif2
                      );
                    }
                  };
                  if_block(
                    $$anchor5,
                    ($$render) => {
                      if (strict_equals(openGraph().type.toLowerCase(), "book") && openGraph().book) $$render(consequent_40);
                      else $$render(alternate_1, false);
                    },
                    $$elseif
                  );
                }
              };
              if_block(node_34, ($$render) => {
                if (strict_equals(openGraph().type.toLowerCase(), "profile") && openGraph().profile) $$render(consequent_35);
                else $$render(alternate, false);
              });
            }
            template_effect(($0) => set_attribute(meta_26, "content", $0), [() => openGraph().type.toLowerCase()]);
            append($$anchor4, fragment_4);
          };
          if_block(node_33, ($$render) => {
            if (openGraph().type) $$render(consequent_58);
          });
        }
        var node_66 = sibling(node_33, 2);
        {
          var consequent_59 = ($$anchor4) => {
            var meta_49 = root_74();
            template_effect(() => set_attribute(meta_49, "content", openGraph().title || get(updatedTitle)));
            append($$anchor4, meta_49);
          };
          if_block(node_66, ($$render) => {
            if (openGraph().title || get(updatedTitle)) $$render(consequent_59);
          });
        }
        var node_67 = sibling(node_66, 2);
        {
          var consequent_60 = ($$anchor4) => {
            var meta_50 = root_75();
            template_effect(() => set_attribute(meta_50, "content", openGraph().description || description()));
            append($$anchor4, meta_50);
          };
          if_block(node_67, ($$render) => {
            if (openGraph().description || description()) $$render(consequent_60);
          });
        }
        var node_68 = sibling(node_67, 2);
        {
          var consequent_66 = ($$anchor4) => {
            var fragment_18 = comment();
            var node_69 = first_child(fragment_18);
            validate_each_keys(() => openGraph().images, (image) => image);
            each(node_69, 16, () => openGraph().images, (image) => image, ($$anchor5, image) => {
              var fragment_19 = root_77();
              var meta_51 = first_child(fragment_19);
              var node_70 = sibling(meta_51, 2);
              {
                var consequent_61 = ($$anchor6) => {
                  var meta_52 = root_78();
                  template_effect(() => set_attribute(meta_52, "content", image.alt));
                  append($$anchor6, meta_52);
                };
                if_block(node_70, ($$render) => {
                  if (image.alt) $$render(consequent_61);
                });
              }
              var node_71 = sibling(node_70, 2);
              {
                var consequent_62 = ($$anchor6) => {
                  var meta_53 = root_79();
                  template_effect(($0) => set_attribute(meta_53, "content", $0), [() => image.width.toString()]);
                  append($$anchor6, meta_53);
                };
                if_block(node_71, ($$render) => {
                  if (image.width) $$render(consequent_62);
                });
              }
              var node_72 = sibling(node_71, 2);
              {
                var consequent_63 = ($$anchor6) => {
                  var meta_54 = root_80();
                  template_effect(($0) => set_attribute(meta_54, "content", $0), [() => image.height.toString()]);
                  append($$anchor6, meta_54);
                };
                if_block(node_72, ($$render) => {
                  if (image.height) $$render(consequent_63);
                });
              }
              var node_73 = sibling(node_72, 2);
              {
                var consequent_64 = ($$anchor6) => {
                  var meta_55 = root_81();
                  template_effect(($0) => set_attribute(meta_55, "content", $0), [() => image.secureUrl.toString()]);
                  append($$anchor6, meta_55);
                };
                if_block(node_73, ($$render) => {
                  if (image.secureUrl) $$render(consequent_64);
                });
              }
              var node_74 = sibling(node_73, 2);
              {
                var consequent_65 = ($$anchor6) => {
                  var meta_56 = root_82();
                  template_effect(($0) => set_attribute(meta_56, "content", $0), [() => image.type.toString()]);
                  append($$anchor6, meta_56);
                };
                if_block(node_74, ($$render) => {
                  if (image.type) $$render(consequent_65);
                });
              }
              template_effect(() => set_attribute(meta_51, "content", image.url));
              append($$anchor5, fragment_19);
            });
            append($$anchor4, fragment_18);
          };
          if_block(node_68, ($$render) => {
            if (openGraph().images && openGraph().images.length) $$render(consequent_66);
          });
        }
        var node_75 = sibling(node_68, 2);
        {
          var consequent_71 = ($$anchor4) => {
            var fragment_20 = comment();
            var node_76 = first_child(fragment_20);
            validate_each_keys(() => openGraph().videos, (video) => video);
            each(node_76, 16, () => openGraph().videos, (video) => video, ($$anchor5, video) => {
              var fragment_21 = root_84();
              var meta_57 = first_child(fragment_21);
              var node_77 = sibling(meta_57, 2);
              {
                var consequent_67 = ($$anchor6) => {
                  var meta_58 = root_85();
                  template_effect(($0) => set_attribute(meta_58, "content", $0), [() => video.width.toString()]);
                  append($$anchor6, meta_58);
                };
                if_block(node_77, ($$render) => {
                  if (video.width) $$render(consequent_67);
                });
              }
              var node_78 = sibling(node_77, 2);
              {
                var consequent_68 = ($$anchor6) => {
                  var meta_59 = root_86();
                  template_effect(($0) => set_attribute(meta_59, "content", $0), [() => video.height.toString()]);
                  append($$anchor6, meta_59);
                };
                if_block(node_78, ($$render) => {
                  if (video.height) $$render(consequent_68);
                });
              }
              var node_79 = sibling(node_78, 2);
              {
                var consequent_69 = ($$anchor6) => {
                  var meta_60 = root_87();
                  template_effect(($0) => set_attribute(meta_60, "content", $0), [() => video.secureUrl.toString()]);
                  append($$anchor6, meta_60);
                };
                if_block(node_79, ($$render) => {
                  if (video.secureUrl) $$render(consequent_69);
                });
              }
              var node_80 = sibling(node_79, 2);
              {
                var consequent_70 = ($$anchor6) => {
                  var meta_61 = root_88();
                  template_effect(($0) => set_attribute(meta_61, "content", $0), [() => video.type.toString()]);
                  append($$anchor6, meta_61);
                };
                if_block(node_80, ($$render) => {
                  if (video.type) $$render(consequent_70);
                });
              }
              template_effect(() => set_attribute(meta_57, "content", video.url));
              append($$anchor5, fragment_21);
            });
            append($$anchor4, fragment_20);
          };
          if_block(node_75, ($$render) => {
            if (openGraph().videos && openGraph().videos.length) $$render(consequent_71);
          });
        }
        var node_81 = sibling(node_75, 2);
        {
          var consequent_74 = ($$anchor4) => {
            var fragment_22 = comment();
            var node_82 = first_child(fragment_22);
            validate_each_keys(() => openGraph().audio, (audio) => audio);
            each(node_82, 16, () => openGraph().audio, (audio) => audio, ($$anchor5, audio) => {
              var fragment_23 = root_90();
              var meta_62 = first_child(fragment_23);
              var node_83 = sibling(meta_62, 2);
              {
                var consequent_72 = ($$anchor6) => {
                  var meta_63 = root_91();
                  template_effect(($0) => set_attribute(meta_63, "content", $0), [() => audio.secureUrl.toString()]);
                  append($$anchor6, meta_63);
                };
                if_block(node_83, ($$render) => {
                  if (audio.secureUrl) $$render(consequent_72);
                });
              }
              var node_84 = sibling(node_83, 2);
              {
                var consequent_73 = ($$anchor6) => {
                  var meta_64 = root_92();
                  template_effect(($0) => set_attribute(meta_64, "content", $0), [() => audio.type.toString()]);
                  append($$anchor6, meta_64);
                };
                if_block(node_84, ($$render) => {
                  if (audio.type) $$render(consequent_73);
                });
              }
              template_effect(() => set_attribute(meta_62, "content", audio.url));
              append($$anchor5, fragment_23);
            });
            append($$anchor4, fragment_22);
          };
          if_block(node_81, ($$render) => {
            if (openGraph().audio && openGraph().audio.length) $$render(consequent_74);
          });
        }
        var node_85 = sibling(node_81, 2);
        {
          var consequent_75 = ($$anchor4) => {
            var meta_65 = root_93();
            template_effect(() => set_attribute(meta_65, "content", openGraph().locale));
            append($$anchor4, meta_65);
          };
          if_block(node_85, ($$render) => {
            if (openGraph().locale) $$render(consequent_75);
          });
        }
        var node_86 = sibling(node_85, 2);
        {
          var consequent_76 = ($$anchor4) => {
            var meta_66 = root_94();
            template_effect(() => set_attribute(meta_66, "content", openGraph().siteName));
            append($$anchor4, meta_66);
          };
          if_block(node_86, ($$render) => {
            if (openGraph().siteName) $$render(consequent_76);
          });
        }
        append($$anchor3, fragment_3);
      };
      if_block(node_31, ($$render) => {
        if (openGraph()) $$render(consequent_77);
      });
    }
    var node_87 = sibling(node_31, 2);
    {
      var consequent_78 = ($$anchor3) => {
        var fragment_24 = comment();
        var node_88 = first_child(fragment_24);
        validate_each_keys(additionalMetaTags, (tag) => tag);
        each(node_88, 16, additionalMetaTags, (tag) => tag, ($$anchor4, tag) => {
          var meta_67 = root_96();
          let attributes;
          template_effect(($0) => attributes = set_attributes(meta_67, attributes, { ...$0 }), [
            () => tag.httpEquiv ? { ...tag, "http-equiv": tag.httpEquiv } : tag
          ]);
          append($$anchor4, meta_67);
        });
        append($$anchor3, fragment_24);
      };
      if_block(node_87, ($$render) => {
        if (additionalMetaTags() && Array.isArray(additionalMetaTags())) $$render(consequent_78);
      });
    }
    var node_89 = sibling(node_87, 2);
    {
      var consequent_79 = ($$anchor3) => {
        var fragment_25 = comment();
        var node_90 = first_child(fragment_25);
        validate_each_keys(additionalLinkTags, (tag) => tag);
        each(node_90, 16, additionalLinkTags, (tag) => tag, ($$anchor4, tag) => {
          var link_3 = root_98();
          let attributes_1;
          template_effect(() => attributes_1 = set_attributes(link_3, attributes_1, { ...tag }));
          replay_events(link_3);
          append($$anchor4, link_3);
        });
        append($$anchor3, fragment_25);
      };
      if_block(node_89, ($$render) => {
        var _a;
        if ((_a = additionalLinkTags()) == null ? void 0 : _a.length) $$render(consequent_79);
      });
    }
    append($$anchor2, fragment);
  });
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  MetaTags = hmr(MetaTags, () => MetaTags[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = MetaTags[HMR].source;
    set(MetaTags[HMR].source, module.default[HMR].original);
  });
}
var MetaTags_default = MetaTags;

// node_modules/.pnpm/svelte-meta-tags@4.3.0_svelte@5.27.0/node_modules/svelte-meta-tags/dist/JsonLd.svelte
JsonLd[FILENAME] = "node_modules/.pnpm/svelte-meta-tags@4.3.0_svelte@5.27.0/node_modules/svelte-meta-tags/dist/JsonLd.svelte";
function JsonLd($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, JsonLd);
  let output = prop($$props, "output", 3, "head"), schema = prop($$props, "schema", 3, void 0);
  let isValid = user_derived(() => schema() && strict_equals(typeof schema(), "object"));
  const createSchema = (schema2) => {
    const addContext = (context) => ({ "@context": "https://schema.org", ...context });
    return Array.isArray(schema2) ? schema2.map((context) => addContext(context)) : addContext(schema2);
  };
  let json = user_derived(() => `${'<script type="application/ld+json">'}${JSON.stringify(createSchema(schema()))}${"<\/script>"}`);
  var fragment_2 = comment();
  head(($$anchor2) => {
    var fragment = comment();
    var node = first_child(fragment);
    {
      var consequent = ($$anchor3) => {
        var fragment_1 = comment();
        var node_1 = first_child(fragment_1);
        html(node_1, () => get(json), false, false);
        append($$anchor3, fragment_1);
      };
      if_block(node, ($$render) => {
        if (get(isValid) && strict_equals(output(), "head")) $$render(consequent);
      });
    }
    append($$anchor2, fragment);
  });
  var node_2 = first_child(fragment_2);
  {
    var consequent_1 = ($$anchor2) => {
      var fragment_3 = comment();
      var node_3 = first_child(fragment_3);
      html(node_3, () => get(json), false, false);
      append($$anchor2, fragment_3);
    };
    if_block(node_2, ($$render) => {
      if (get(isValid) && strict_equals(output(), "body")) $$render(consequent_1);
    });
  }
  append($$anchor, fragment_2);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  JsonLd = hmr(JsonLd, () => JsonLd[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = JsonLd[HMR].source;
    set(JsonLd[HMR].source, module.default[HMR].original);
  });
}
var JsonLd_default = JsonLd;

// node_modules/.pnpm/svelte-meta-tags@4.3.0_svelte@5.27.0/node_modules/svelte-meta-tags/dist/deepMerge.js
var deepMerge = (target, source) => {
  if (!target || !source)
    return target ?? source ?? {};
  const result = { ...target };
  for (const [key, value] of Object.entries(source)) {
    const targetValue = target[key];
    if (targetValue instanceof Date || typeof targetValue === "function") {
      result[key] = targetValue;
    } else if (value instanceof Date || typeof value === "function") {
      result[key] = value;
    } else if (isObject(targetValue) && isObject(value)) {
      result[key] = deepMerge(targetValue, value);
    } else if (isArray(targetValue) && isArray(value)) {
      result[key] = value;
    } else {
      result[key] = value !== void 0 ? value : targetValue;
    }
  }
  return result;
};
var isObject = (obj) => obj !== null && typeof obj === "object" && !Array.isArray(obj);
var isArray = (obj) => Array.isArray(obj);
export {
  JsonLd_default as JsonLd,
  MetaTags_default as MetaTags,
  deepMerge
};
//# sourceMappingURL=svelte-meta-tags.js.map
