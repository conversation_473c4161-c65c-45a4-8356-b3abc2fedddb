{"version": 3, "sources": ["../../.pnpm/svelte-dnd-action@0.9.60_svelte@5.27.0/node_modules/svelte-dnd-action/dist/index.mjs"], "sourcesContent": ["function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (!it) {\n    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n      if (it) o = it;\n      var i = 0;\n      var F = function () {};\n      return {\n        s: F,\n        n: function () {\n          if (i >= o.length) return {\n            done: true\n          };\n          return {\n            done: false,\n            value: o[i++]\n          };\n        },\n        e: function (e) {\n          throw e;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var normalCompletion = true,\n    didErr = false,\n    err;\n  return {\n    s: function () {\n      it = it.call(o);\n    },\n    n: function () {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function (e) {\n      didErr = true;\n      err = e;\n    },\n    f: function () {\n      try {\n        if (!normalCompletion && it.return != null) it.return();\n      } finally {\n        if (didErr) throw err;\n      }\n    }\n  };\n}\n\n// external events\nvar FINALIZE_EVENT_NAME = \"finalize\";\nvar CONSIDER_EVENT_NAME = \"consider\";\n\n/**\n * @typedef {Object} Info\n * @property {string} trigger\n * @property {string} id\n * @property {string} source\n * @param {Node} el\n * @param {Array} items\n * @param {Info} info\n */\nfunction dispatchFinalizeEvent(el, items, info) {\n  el.dispatchEvent(new CustomEvent(FINALIZE_EVENT_NAME, {\n    detail: {\n      items: items,\n      info: info\n    }\n  }));\n}\n\n/**\n * Dispatches a consider event\n * @param {Node} el\n * @param {Array} items\n * @param {Info} info\n */\nfunction dispatchConsiderEvent(el, items, info) {\n  el.dispatchEvent(new CustomEvent(CONSIDER_EVENT_NAME, {\n    detail: {\n      items: items,\n      info: info\n    }\n  }));\n}\n\n// internal events\nvar DRAGGED_ENTERED_EVENT_NAME = \"draggedEntered\";\nvar DRAGGED_LEFT_EVENT_NAME = \"draggedLeft\";\nvar DRAGGED_OVER_INDEX_EVENT_NAME = \"draggedOverIndex\";\nvar DRAGGED_LEFT_DOCUMENT_EVENT_NAME = \"draggedLeftDocument\";\nvar DRAGGED_LEFT_TYPES = {\n  LEFT_FOR_ANOTHER: \"leftForAnother\",\n  OUTSIDE_OF_ANY: \"outsideOfAny\"\n};\nfunction dispatchDraggedElementEnteredContainer(containerEl, indexObj, draggedEl) {\n  containerEl.dispatchEvent(new CustomEvent(DRAGGED_ENTERED_EVENT_NAME, {\n    detail: {\n      indexObj: indexObj,\n      draggedEl: draggedEl\n    }\n  }));\n}\n\n/**\n * @param containerEl - the dropzone the element left\n * @param draggedEl - the dragged element\n * @param theOtherDz - the new dropzone the element entered\n */\nfunction dispatchDraggedElementLeftContainerForAnother(containerEl, draggedEl, theOtherDz) {\n  containerEl.dispatchEvent(new CustomEvent(DRAGGED_LEFT_EVENT_NAME, {\n    detail: {\n      draggedEl: draggedEl,\n      type: DRAGGED_LEFT_TYPES.LEFT_FOR_ANOTHER,\n      theOtherDz: theOtherDz\n    }\n  }));\n}\nfunction dispatchDraggedElementLeftContainerForNone(containerEl, draggedEl) {\n  containerEl.dispatchEvent(new CustomEvent(DRAGGED_LEFT_EVENT_NAME, {\n    detail: {\n      draggedEl: draggedEl,\n      type: DRAGGED_LEFT_TYPES.OUTSIDE_OF_ANY\n    }\n  }));\n}\nfunction dispatchDraggedElementIsOverIndex(containerEl, indexObj, draggedEl) {\n  containerEl.dispatchEvent(new CustomEvent(DRAGGED_OVER_INDEX_EVENT_NAME, {\n    detail: {\n      indexObj: indexObj,\n      draggedEl: draggedEl\n    }\n  }));\n}\nfunction dispatchDraggedLeftDocument(draggedEl) {\n  window.dispatchEvent(new CustomEvent(DRAGGED_LEFT_DOCUMENT_EVENT_NAME, {\n    detail: {\n      draggedEl: draggedEl\n    }\n  }));\n}\n\nvar TRIGGERS = {\n  DRAG_STARTED: \"dragStarted\",\n  DRAGGED_ENTERED: DRAGGED_ENTERED_EVENT_NAME,\n  DRAGGED_ENTERED_ANOTHER: \"dragEnteredAnother\",\n  DRAGGED_OVER_INDEX: DRAGGED_OVER_INDEX_EVENT_NAME,\n  DRAGGED_LEFT: DRAGGED_LEFT_EVENT_NAME,\n  DRAGGED_LEFT_ALL: \"draggedLeftAll\",\n  DROPPED_INTO_ZONE: \"droppedIntoZone\",\n  DROPPED_INTO_ANOTHER: \"droppedIntoAnother\",\n  DROPPED_OUTSIDE_OF_ANY: \"droppedOutsideOfAny\",\n  DRAG_STOPPED: \"dragStopped\"\n};\nvar SOURCES = {\n  POINTER: \"pointer\",\n  KEYBOARD: \"keyboard\"\n};\nvar SHADOW_ITEM_MARKER_PROPERTY_NAME = \"isDndShadowItem\";\nvar SHADOW_ELEMENT_ATTRIBUTE_NAME = \"data-is-dnd-shadow-item-internal\";\nvar SHADOW_ELEMENT_HINT_ATTRIBUTE_NAME = \"data-is-dnd-shadow-item-hint\";\nvar SHADOW_PLACEHOLDER_ITEM_ID = \"id:dnd-shadow-placeholder-0000\";\nvar DRAGGED_ELEMENT_ID = \"dnd-action-dragged-el\";\nvar ITEM_ID_KEY = \"id\";\nvar activeDndZoneCount = 0;\nfunction incrementActiveDropZoneCount() {\n  activeDndZoneCount++;\n}\nfunction decrementActiveDropZoneCount() {\n  if (activeDndZoneCount === 0) {\n    throw new Error(\"Bug! trying to decrement when there are no dropzones\");\n  }\n  activeDndZoneCount--;\n}\n\n/**\n * Allows using another key instead of \"id\" in the items data. This is global and applies to all dndzones.\n * Has to be called when there are no rendered dndzones whatsoever.\n * @param {String} newKeyName\n * @throws {Error} if it was called when there are rendered dndzones or if it is given the wrong type (not a string)\n */\nfunction overrideItemIdKeyNameBeforeInitialisingDndZones(newKeyName) {\n  if (activeDndZoneCount > 0) {\n    throw new Error(\"can only override the id key before initialising any dndzone\");\n  }\n  if (typeof newKeyName !== \"string\") {\n    throw new Error(\"item id key has to be a string\");\n  }\n  printDebug(function () {\n    return [\"overriding item id key name\", newKeyName];\n  });\n  ITEM_ID_KEY = newKeyName;\n}\nvar isOnServer = typeof window === \"undefined\";\nvar printDebug = function printDebug() {};\n\n/**\n * Allows the user to show/hide console debug output\n * * @param {boolean} isDebug\n */\nfunction setDebugMode(isDebug) {\n  if (isDebug) {\n    printDebug = function printDebug(generateMessage) {\n      var logFunction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : console.debug;\n      var message = generateMessage();\n      if (Array.isArray(message)) {\n        logFunction.apply(void 0, _toConsumableArray(message));\n      } else {\n        logFunction(message);\n      }\n    };\n  } else {\n    printDebug = function printDebug() {};\n  }\n}\n\n// This is based off https://stackoverflow.com/questions/27745438/how-to-compute-getboundingclientrect-without-considering-transforms/57876601#57876601\n// It removes the transforms that are potentially applied by the flip animations\n/**\n * Gets the bounding rect but removes transforms (ex: flip animation)\n * @param {HTMLElement} el\n * @param {boolean} [onlyVisible] - use the visible rect defaults to true\n * @return {{top: number, left: number, bottom: number, right: number}}\n */\nfunction getBoundingRectNoTransforms(el) {\n  var onlyVisible = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var ta;\n  var rect = onlyVisible ? getVisibleRectRecursive(el) : el.getBoundingClientRect();\n  var style = getComputedStyle(el);\n  var tx = style.transform;\n  if (tx) {\n    var sx, sy, dx, dy;\n    if (tx.startsWith(\"matrix3d(\")) {\n      ta = tx.slice(9, -1).split(/, /);\n      sx = +ta[0];\n      sy = +ta[5];\n      dx = +ta[12];\n      dy = +ta[13];\n    } else if (tx.startsWith(\"matrix(\")) {\n      ta = tx.slice(7, -1).split(/, /);\n      sx = +ta[0];\n      sy = +ta[3];\n      dx = +ta[4];\n      dy = +ta[5];\n    } else {\n      return rect;\n    }\n    var to = style.transformOrigin;\n    var x = rect.x - dx - (1 - sx) * parseFloat(to);\n    var y = rect.y - dy - (1 - sy) * parseFloat(to.slice(to.indexOf(\" \") + 1));\n    var w = sx ? rect.width / sx : el.offsetWidth;\n    var h = sy ? rect.height / sy : el.offsetHeight;\n    return {\n      x: x,\n      y: y,\n      width: w,\n      height: h,\n      top: y,\n      right: x + w,\n      bottom: y + h,\n      left: x\n    };\n  } else {\n    return rect;\n  }\n}\n\n/**\n * Gets the absolute bounding rect (accounts for the window's scroll position and removes transforms)\n * @param {HTMLElement} el\n * @return {{top: number, left: number, bottom: number, right: number}}\n */\nfunction getAbsoluteRectNoTransforms(el) {\n  var rect = getBoundingRectNoTransforms(el);\n  return {\n    top: rect.top + window.scrollY,\n    bottom: rect.bottom + window.scrollY,\n    left: rect.left + window.scrollX,\n    right: rect.right + window.scrollX\n  };\n}\n\n/**\n * Gets the absolute bounding rect (accounts for the window's scroll position)\n * @param {HTMLElement} el\n * @return {{top: number, left: number, bottom: number, right: number}}\n */\nfunction getAbsoluteRect(el) {\n  var rect = el.getBoundingClientRect();\n  return {\n    top: rect.top + window.scrollY,\n    bottom: rect.bottom + window.scrollY,\n    left: rect.left + window.scrollX,\n    right: rect.right + window.scrollX\n  };\n}\n\n/**\n * finds the center :)\n * @typedef {Object} Rect\n * @property {number} top\n * @property {number} bottom\n * @property {number} left\n * @property {number} right\n * @param {Rect} rect\n * @return {{x: number, y: number}}\n */\nfunction findCenter(rect) {\n  return {\n    x: (rect.left + rect.right) / 2,\n    y: (rect.top + rect.bottom) / 2\n  };\n}\n\n/**\n * @typedef {Object} Point\n * @property {number} x\n * @property {number} y\n * @param {Point} pointA\n * @param {Point} pointB\n * @return {number}\n */\nfunction calcDistance(pointA, pointB) {\n  return Math.sqrt(Math.pow(pointA.x - pointB.x, 2) + Math.pow(pointA.y - pointB.y, 2));\n}\n\n/**\n * @param {Point} point\n * @param {Rect} rect\n * @return {boolean|boolean}\n */\nfunction isPointInsideRect(point, rect) {\n  return point.y <= rect.bottom && point.y >= rect.top && point.x >= rect.left && point.x <= rect.right;\n}\n\n/**\n * find the absolute coordinates of the center of a dom element\n * @param el {HTMLElement}\n * @returns {{x: number, y: number}}\n */\nfunction findCenterOfElement(el) {\n  return findCenter(getAbsoluteRect(el));\n}\n\n/**\n * @param {HTMLElement} elA\n * @param {HTMLElement} elB\n * @return {boolean}\n */\nfunction isCenterOfAInsideB(elA, elB) {\n  var centerOfA = findCenterOfElement(elA);\n  var rectOfB = getAbsoluteRectNoTransforms(elB);\n  return isPointInsideRect(centerOfA, rectOfB);\n}\n\n/**\n * @param {HTMLElement|ChildNode} elA\n * @param {HTMLElement|ChildNode} elB\n * @return {number}\n */\nfunction calcDistanceBetweenCenters(elA, elB) {\n  var centerOfA = findCenterOfElement(elA);\n  var centerOfB = findCenterOfElement(elB);\n  return calcDistance(centerOfA, centerOfB);\n}\n\n/**\n * @param {HTMLElement} el - the element to check\n * @returns {boolean} - true if the element in its entirety is off-screen including the scrollable area (the normal dom events look at the mouse rather than the element)\n */\nfunction isElementOffDocument(el) {\n  var rect = getAbsoluteRect(el);\n  return rect.right < 0 || rect.left > document.documentElement.scrollWidth || rect.bottom < 0 || rect.top > document.documentElement.scrollHeight;\n}\nfunction getVisibleRectRecursive(element) {\n  var rect = element.getBoundingClientRect();\n  var visibleRect = {\n    top: rect.top,\n    bottom: rect.bottom,\n    left: rect.left,\n    right: rect.right\n  };\n\n  // Traverse up the DOM hierarchy, checking for scrollable ancestors\n  var parent = element.parentElement;\n  while (parent && parent !== document.body) {\n    var parentRect = parent.getBoundingClientRect();\n\n    // Check if the parent has a scrollable overflow\n    var overflowY = window.getComputedStyle(parent).overflowY;\n    var overflowX = window.getComputedStyle(parent).overflowX;\n    var isScrollableY = overflowY === \"scroll\" || overflowY === \"auto\";\n    var isScrollableX = overflowX === \"scroll\" || overflowX === \"auto\";\n\n    // Constrain the visible area to the parent's visible area\n    if (isScrollableY) {\n      visibleRect.top = Math.max(visibleRect.top, parentRect.top);\n      visibleRect.bottom = Math.min(visibleRect.bottom, parentRect.bottom);\n    }\n    if (isScrollableX) {\n      visibleRect.left = Math.max(visibleRect.left, parentRect.left);\n      visibleRect.right = Math.min(visibleRect.right, parentRect.right);\n    }\n    parent = parent.parentElement;\n  }\n\n  // Finally, constrain the visible rect to the viewport\n  visibleRect.top = Math.max(visibleRect.top, 0);\n  visibleRect.bottom = Math.min(visibleRect.bottom, window.innerHeight);\n  visibleRect.left = Math.max(visibleRect.left, 0);\n  visibleRect.right = Math.min(visibleRect.right, window.innerWidth);\n\n  // Return the visible rectangle, ensuring that all values are valid\n  return {\n    top: visibleRect.top,\n    bottom: visibleRect.bottom,\n    left: visibleRect.left,\n    right: visibleRect.right,\n    width: Math.max(0, visibleRect.right - visibleRect.left),\n    height: Math.max(0, visibleRect.bottom - visibleRect.top)\n  };\n}\n\nvar dzToShadowIndexToRect;\n\n/**\n * Resets the cache that allows for smarter \"would be index\" resolution. Should be called after every drag operation\n */\nfunction resetIndexesCache() {\n  printDebug(function () {\n    return \"resetting indexes cache\";\n  });\n  dzToShadowIndexToRect = new Map();\n}\nresetIndexesCache();\n\n/**\n * Caches the coordinates of the shadow element when it's in a certain index in a certain dropzone.\n * Helpful in order to determine \"would be index\" more effectively\n * @param {HTMLElement} dz\n * @return {number} - the shadow element index\n */\nfunction cacheShadowRect(dz) {\n  var shadowElIndex = Array.from(dz.children).findIndex(function (child) {\n    return child.getAttribute(SHADOW_ELEMENT_ATTRIBUTE_NAME);\n  });\n  if (shadowElIndex >= 0) {\n    if (!dzToShadowIndexToRect.has(dz)) {\n      dzToShadowIndexToRect.set(dz, new Map());\n    }\n    dzToShadowIndexToRect.get(dz).set(shadowElIndex, getAbsoluteRectNoTransforms(dz.children[shadowElIndex]));\n    return shadowElIndex;\n  }\n  return undefined;\n}\n\n/**\n * @typedef {Object} Index\n * @property {number} index - the would be index\n * @property {boolean} isProximityBased - false if the element is actually over the index, true if it is not over it but this index is the closest\n */\n/**\n * Find the index for the dragged element in the list it is dragged over\n * @param {HTMLElement} floatingAboveEl\n * @param {HTMLElement} collectionBelowEl\n * @returns {Index|null} -  if the element is over the container the Index object otherwise null\n */\nfunction findWouldBeIndex(floatingAboveEl, collectionBelowEl) {\n  if (!isCenterOfAInsideB(floatingAboveEl, collectionBelowEl)) {\n    return null;\n  }\n  var children = collectionBelowEl.children;\n  // the container is empty, floating element should be the first\n  if (children.length === 0) {\n    return {\n      index: 0,\n      isProximityBased: true\n    };\n  }\n  var shadowElIndex = cacheShadowRect(collectionBelowEl);\n\n  // the search could be more efficient but keeping it simple for now\n  // a possible improvement: pass in the lastIndex it was found in and check there first, then expand from there\n  for (var i = 0; i < children.length; i++) {\n    if (isCenterOfAInsideB(floatingAboveEl, children[i])) {\n      var cachedShadowRect = dzToShadowIndexToRect.has(collectionBelowEl) && dzToShadowIndexToRect.get(collectionBelowEl).get(i);\n      if (cachedShadowRect) {\n        if (!isPointInsideRect(findCenterOfElement(floatingAboveEl), cachedShadowRect)) {\n          return {\n            index: shadowElIndex,\n            isProximityBased: false\n          };\n        }\n      }\n      return {\n        index: i,\n        isProximityBased: false\n      };\n    }\n  }\n  // this can happen if there is space around the children so the floating element has\n  //entered the container but not any of the children, in this case we will find the nearest child\n  var minDistanceSoFar = Number.MAX_VALUE;\n  var indexOfMin = undefined;\n  // we are checking all of them because we don't know whether we are dealing with a horizontal or vertical container and where the floating element entered from\n  for (var _i = 0; _i < children.length; _i++) {\n    var distance = calcDistanceBetweenCenters(floatingAboveEl, children[_i]);\n    if (distance < minDistanceSoFar) {\n      minDistanceSoFar = distance;\n      indexOfMin = _i;\n    }\n  }\n  return {\n    index: indexOfMin,\n    isProximityBased: true\n  };\n}\n\n/**\n * @param {Object} object\n * @return {string}\n */\nfunction toString(object) {\n  return JSON.stringify(object, null, 2);\n}\n\n/**\n * Finds the depth of the given node in the DOM tree\n * @param {HTMLElement} node\n * @return {number} - the depth of the node\n */\nfunction getDepth(node) {\n  if (!node) {\n    throw new Error(\"cannot get depth of a falsy node\");\n  }\n  return _getDepth(node, 0);\n}\nfunction _getDepth(node) {\n  var countSoFar = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  if (!node.parentElement) {\n    return countSoFar - 1;\n  }\n  return _getDepth(node.parentElement, countSoFar + 1);\n}\n\n/**\n * A simple util to shallow compare objects quickly, it doesn't validate the arguments so pass objects in\n * @param {Object} objA\n * @param {Object} objB\n * @return {boolean} - true if objA and objB are shallow equal\n */\nfunction areObjectsShallowEqual(objA, objB) {\n  if (Object.keys(objA).length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (var keyA in objA) {\n    if (!{}.hasOwnProperty.call(objB, keyA) || objB[keyA] !== objA[keyA]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Shallow compares two arrays\n * @param arrA\n * @param arrB\n * @return {boolean} - whether the arrays are shallow equal\n */\nfunction areArraysShallowEqualSameOrder(arrA, arrB) {\n  if (arrA.length !== arrB.length) {\n    return false;\n  }\n  for (var i = 0; i < arrA.length; i++) {\n    if (arrA[i] !== arrB[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar INTERVAL_MS = 200;\nvar TOLERANCE_PX = 10;\nvar next;\n\n/**\n * Tracks the dragged elements and performs the side effects when it is dragged over a drop zone (basically dispatching custom-events scrolling)\n * @param {Set<HTMLElement>} dropZones\n * @param {HTMLElement} draggedEl\n * @param {number} [intervalMs = INTERVAL_MS]\n * @param {MultiScroller} multiScroller\n */\nfunction observe(draggedEl, dropZones) {\n  var intervalMs = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : INTERVAL_MS;\n  var multiScroller = arguments.length > 3 ? arguments[3] : undefined;\n  // initialization\n  var lastDropZoneFound;\n  var lastIndexFound;\n  var lastIsDraggedInADropZone = false;\n  var lastCentrePositionOfDragged;\n  // We are sorting to make sure that in case of nested zones of the same type the one \"on top\" is considered first\n  var dropZonesFromDeepToShallow = Array.from(dropZones).sort(function (dz1, dz2) {\n    return getDepth(dz2) - getDepth(dz1);\n  });\n\n  /**\n   * The main function in this module. Tracks where everything is/ should be a take the actions\n   */\n  function andNow() {\n    var currentCenterOfDragged = findCenterOfElement(draggedEl);\n    var scrolled = multiScroller.multiScrollIfNeeded();\n    // we only want to make a new decision after the element was moved a bit to prevent flickering\n    if (!scrolled && lastCentrePositionOfDragged && Math.abs(lastCentrePositionOfDragged.x - currentCenterOfDragged.x) < TOLERANCE_PX && Math.abs(lastCentrePositionOfDragged.y - currentCenterOfDragged.y) < TOLERANCE_PX) {\n      next = window.setTimeout(andNow, intervalMs);\n      return;\n    }\n    if (isElementOffDocument(draggedEl)) {\n      printDebug(function () {\n        return \"off document\";\n      });\n      dispatchDraggedLeftDocument(draggedEl);\n      return;\n    }\n    lastCentrePositionOfDragged = currentCenterOfDragged;\n    // this is a simple algorithm, potential improvement: first look at lastDropZoneFound\n    var isDraggedInADropZone = false;\n    var _iterator = _createForOfIteratorHelper(dropZonesFromDeepToShallow),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var dz = _step.value;\n        if (scrolled) resetIndexesCache();\n        var indexObj = findWouldBeIndex(draggedEl, dz);\n        if (indexObj === null) {\n          // it is not inside\n          continue;\n        }\n        var index = indexObj.index;\n        isDraggedInADropZone = true;\n        // the element is over a container\n        if (dz !== lastDropZoneFound) {\n          lastDropZoneFound && dispatchDraggedElementLeftContainerForAnother(lastDropZoneFound, draggedEl, dz);\n          dispatchDraggedElementEnteredContainer(dz, indexObj, draggedEl);\n          lastDropZoneFound = dz;\n        } else if (index !== lastIndexFound) {\n          dispatchDraggedElementIsOverIndex(dz, indexObj, draggedEl);\n          lastIndexFound = index;\n        }\n        // we handle looping with the 'continue' statement above\n        break;\n      }\n      // the first time the dragged element is not in any dropzone we need to notify the last dropzone it was in\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    if (!isDraggedInADropZone && lastIsDraggedInADropZone && lastDropZoneFound) {\n      dispatchDraggedElementLeftContainerForNone(lastDropZoneFound, draggedEl);\n      lastDropZoneFound = undefined;\n      lastIndexFound = undefined;\n      lastIsDraggedInADropZone = false;\n    } else {\n      lastIsDraggedInADropZone = true;\n    }\n    next = window.setTimeout(andNow, intervalMs);\n  }\n  andNow();\n}\n\n// assumption - we can only observe one dragged element at a time, this could be changed in the future\nfunction unobserve() {\n  printDebug(function () {\n    return \"unobserving\";\n  });\n  clearTimeout(next);\n  resetIndexesCache();\n}\n\nvar SCROLL_ZONE_PX = 30;\n\n/**\n * Will make a scroller that can scroll any element given to it in any direction\n * @returns {{scrollIfNeeded: function(Point, HTMLElement): boolean, resetScrolling: function(void):void}}\n */\nfunction makeScroller() {\n  var scrollingInfo;\n  function resetScrolling() {\n    scrollingInfo = {\n      directionObj: undefined,\n      stepPx: 0\n    };\n  }\n  resetScrolling();\n  // directionObj {x: 0|1|-1, y:0|1|-1} - 1 means down in y and right in x\n  function scrollContainer(containerEl) {\n    var _scrollingInfo = scrollingInfo,\n      directionObj = _scrollingInfo.directionObj,\n      stepPx = _scrollingInfo.stepPx;\n    if (directionObj) {\n      containerEl.scrollBy(directionObj.x * stepPx, directionObj.y * stepPx);\n      window.requestAnimationFrame(function () {\n        return scrollContainer(containerEl);\n      });\n    }\n  }\n  function calcScrollStepPx(distancePx) {\n    return SCROLL_ZONE_PX - distancePx;\n  }\n\n  /**\n   * @param {Point} pointer - the pointer will be used to decide in which direction to scroll\n   * @param {HTMLElement} elementToScroll - the scroll container\n   * If the pointer is next to the sides of the element to scroll, will trigger scrolling\n   * Can be called repeatedly with updated pointer and elementToScroll values without issues\n   * @return {boolean} - true if scrolling was needed\n   */\n  function scrollIfNeeded(pointer, elementToScroll) {\n    if (!elementToScroll) {\n      return false;\n    }\n    var distances = calcInnerDistancesBetweenPointAndSidesOfElement(pointer, elementToScroll);\n    var isAlreadyScrolling = !!scrollingInfo.directionObj;\n    if (distances === null) {\n      if (isAlreadyScrolling) resetScrolling();\n      return false;\n    }\n    var scrollingVertically = false,\n      scrollingHorizontally = false;\n    // vertical\n    if (elementToScroll.scrollHeight > elementToScroll.clientHeight) {\n      if (distances.bottom < SCROLL_ZONE_PX) {\n        scrollingVertically = true;\n        scrollingInfo.directionObj = {\n          x: 0,\n          y: 1\n        };\n        scrollingInfo.stepPx = calcScrollStepPx(distances.bottom);\n      } else if (distances.top < SCROLL_ZONE_PX) {\n        scrollingVertically = true;\n        scrollingInfo.directionObj = {\n          x: 0,\n          y: -1\n        };\n        scrollingInfo.stepPx = calcScrollStepPx(distances.top);\n      }\n      if (!isAlreadyScrolling && scrollingVertically) {\n        scrollContainer(elementToScroll);\n        return true;\n      }\n    }\n    // horizontal\n    if (elementToScroll.scrollWidth > elementToScroll.clientWidth) {\n      if (distances.right < SCROLL_ZONE_PX) {\n        scrollingHorizontally = true;\n        scrollingInfo.directionObj = {\n          x: 1,\n          y: 0\n        };\n        scrollingInfo.stepPx = calcScrollStepPx(distances.right);\n      } else if (distances.left < SCROLL_ZONE_PX) {\n        scrollingHorizontally = true;\n        scrollingInfo.directionObj = {\n          x: -1,\n          y: 0\n        };\n        scrollingInfo.stepPx = calcScrollStepPx(distances.left);\n      }\n      if (!isAlreadyScrolling && scrollingHorizontally) {\n        scrollContainer(elementToScroll);\n        return true;\n      }\n    }\n    resetScrolling();\n    return false;\n  }\n  return {\n    scrollIfNeeded: scrollIfNeeded,\n    resetScrolling: resetScrolling\n  };\n}\n\n/**\n * If the point is inside the element returns its distances from the sides, otherwise returns null\n * @param {Point} point\n * @param {HTMLElement} el\n * @return {null|{top: number, left: number, bottom: number, right: number}}\n */\nfunction calcInnerDistancesBetweenPointAndSidesOfElement(point, el) {\n  // Even if the scrolling element is small it acts as a scroller for the viewport\n  var rect = el === document.scrollingElement ? {\n    top: 0,\n    bottom: window.innerHeight,\n    left: 0,\n    right: window.innerWidth\n  } : el.getBoundingClientRect();\n  if (!isPointInsideRect(point, rect)) {\n    return null;\n  }\n  return {\n    top: point.y - rect.top,\n    bottom: rect.bottom - point.y,\n    left: point.x - rect.left,\n    right: rect.right - point.x\n  };\n}\n\n/**\n @typedef {Object} MultiScroller\n @property {function():boolean} multiScrollIfNeeded - call this on every \"tick\" to scroll containers if needed, returns true if anything was scrolled\n/**\n * Creates a scroller than can scroll any of the provided containers or any of their scrollable parents (including the document's scrolling element)\n * @param {HTMLElement[]} baseElementsForScrolling\n * @param {function():Point} getPointerPosition\n * @return {MultiScroller}\n */\nfunction createMultiScroller() {\n  var baseElementsForScrolling = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var getPointerPosition = arguments.length > 1 ? arguments[1] : undefined;\n  printDebug(function () {\n    return \"creating multi-scroller\";\n  });\n  var scrollingContainersSet = findRelevantScrollContainers(baseElementsForScrolling);\n  var scrollingContainersDeepToShallow = Array.from(scrollingContainersSet).sort(function (dz1, dz2) {\n    return getDepth(dz2) - getDepth(dz1);\n  });\n  var _makeScroller = makeScroller(),\n    scrollIfNeeded = _makeScroller.scrollIfNeeded,\n    resetScrolling = _makeScroller.resetScrolling;\n\n  /**\n   * @return {boolean} - was any container scrolled\n   */\n  function tick() {\n    var mousePosition = getPointerPosition();\n    if (!mousePosition || !scrollingContainersDeepToShallow) {\n      return false;\n    }\n    var scrollContainersUnderCursor = scrollingContainersDeepToShallow.filter(function (el) {\n      return isPointInsideRect(mousePosition, el.getBoundingClientRect()) || el === document.scrollingElement;\n    });\n    for (var i = 0; i < scrollContainersUnderCursor.length; i++) {\n      var scrolled = scrollIfNeeded(mousePosition, scrollContainersUnderCursor[i]);\n      if (scrolled) {\n        return true;\n      }\n    }\n    return false;\n  }\n  return {\n    multiScrollIfNeeded: scrollingContainersSet.size > 0 ? tick : function () {\n      return false;\n    },\n    destroy: function destroy() {\n      return resetScrolling();\n    }\n  };\n}\n\n// internal utils\nfunction findScrollableParents(element) {\n  if (!element) {\n    return [];\n  }\n  var scrollableContainers = [];\n  var parent = element;\n  while (parent) {\n    var _window$getComputedSt = window.getComputedStyle(parent),\n      overflow = _window$getComputedSt.overflow;\n    if (overflow.split(\" \").some(function (o) {\n      return o.includes(\"auto\") || o.includes(\"scroll\");\n    })) {\n      scrollableContainers.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return scrollableContainers;\n}\nfunction findRelevantScrollContainers(dropZones) {\n  var scrollingContainers = new Set();\n  var _iterator = _createForOfIteratorHelper(dropZones),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var dz = _step.value;\n      findScrollableParents(dz).forEach(function (container) {\n        return scrollingContainers.add(container);\n      });\n    }\n    // The scrolling element might have overflow visible and still be scrollable\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  if (document.scrollingElement.scrollHeight > document.scrollingElement.clientHeight || document.scrollingElement.scrollWidth > document.scrollingElement.clientHeight) {\n    scrollingContainers.add(document.scrollingElement);\n  }\n  return scrollingContainers;\n}\n\n/**\n * Fixes svelte issue when cloning node containing (or being) <select> which will loose it's value.\n * Since svelte manages select value internally.\n * @see https://github.com/sveltejs/svelte/issues/6717\n * @see https://github.com/isaacHagoel/svelte-dnd-action/issues/306\n *\n * @param {HTMLElement} el\n * @returns\n */\nfunction svelteNodeClone(el) {\n  var cloned = el.cloneNode(true);\n  var values = [];\n  var elIsSelect = el.tagName === \"SELECT\";\n  var selects = elIsSelect ? [el] : _toConsumableArray(el.querySelectorAll(\"select\"));\n  var _iterator = _createForOfIteratorHelper(selects),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var _select = _step.value;\n      values.push(_select.value);\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  if (selects.length > 0) {\n    var clonedSelects = elIsSelect ? [cloned] : _toConsumableArray(cloned.querySelectorAll(\"select\"));\n    for (var i = 0; i < clonedSelects.length; i++) {\n      var select = clonedSelects[i];\n      var value = values[i];\n      var optionEl = select.querySelector(\"option[value=\\\"\".concat(value, \"\\\"\"));\n      if (optionEl) {\n        optionEl.setAttribute(\"selected\", true);\n      }\n    }\n  }\n  var elIsCanvas = el.tagName === \"CANVAS\";\n  var canvases = elIsCanvas ? [el] : _toConsumableArray(el.querySelectorAll(\"canvas\"));\n  if (canvases.length > 0) {\n    var clonedCanvases = elIsCanvas ? [cloned] : _toConsumableArray(cloned.querySelectorAll(\"canvas\"));\n    for (var _i = 0; _i < clonedCanvases.length; _i++) {\n      var canvas = canvases[_i];\n      var clonedCanvas = clonedCanvases[_i];\n      clonedCanvas.width = canvas.width;\n      clonedCanvas.height = canvas.height;\n      if (canvas.width > 0 && canvas.height > 0) {\n        clonedCanvas.getContext(\"2d\").drawImage(canvas, 0, 0);\n      }\n    }\n  }\n  return cloned;\n}\n\n/**\n * @type {{USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT: string}}\n */\nvar FEATURE_FLAG_NAMES = Object.freeze({\n  // This flag exists as a workaround for issue 454 (basically a browser bug) - seems like these rect values take time to update when in grid layout. Setting it to true can cause strange behaviour in the REPL for non-grid zones, see issue 470\n  USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT: \"USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT\"\n});\nvar featureFlagsMap = _defineProperty({}, FEATURE_FLAG_NAMES.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT, false);\n\n/**\n * @param {FEATURE_FLAG_NAMES} flagName\n * @param {boolean} flagValue\n */\nfunction setFeatureFlag(flagName, flagValue) {\n  if (!FEATURE_FLAG_NAMES[flagName]) throw new Error(\"Can't set non existing feature flag \".concat(flagName, \"! Supported flags: \").concat(Object.keys(FEATURE_FLAG_NAMES)));\n  featureFlagsMap[flagName] = !!flagValue;\n}\n\n/**\n *\n * @param {FEATURE_FLAG_NAMES} flagName\n * @return {boolean}\n */\nfunction getFeatureFlag(flagName) {\n  if (!FEATURE_FLAG_NAMES[flagName]) throw new Error(\"Can't get non existing feature flag \".concat(flagName, \"! Supported flags: \").concat(Object.keys(FEATURE_FLAG_NAMES)));\n  return featureFlagsMap[flagName];\n}\n\nvar TRANSITION_DURATION_SECONDS = 0.2;\n\n/**\n * private helper function - creates a transition string for a property\n * @param {string} property\n * @return {string} - the transition string\n */\nfunction trs(property) {\n  return \"\".concat(property, \" \").concat(TRANSITION_DURATION_SECONDS, \"s ease\");\n}\n/**\n * clones the given element and applies proper styles and transitions to the dragged element\n * @param {HTMLElement} originalElement\n * @param {Point} [positionCenterOnXY]\n * @return {Node} - the cloned, styled element\n */\nfunction createDraggedElementFrom(originalElement, positionCenterOnXY) {\n  var rect = originalElement.getBoundingClientRect();\n  var draggedEl = svelteNodeClone(originalElement);\n  copyStylesFromTo(originalElement, draggedEl);\n  draggedEl.id = DRAGGED_ELEMENT_ID;\n  draggedEl.style.position = \"fixed\";\n  var elTopPx = rect.top;\n  var elLeftPx = rect.left;\n  draggedEl.style.top = \"\".concat(elTopPx, \"px\");\n  draggedEl.style.left = \"\".concat(elLeftPx, \"px\");\n  if (positionCenterOnXY) {\n    var center = findCenter(rect);\n    elTopPx -= center.y - positionCenterOnXY.y;\n    elLeftPx -= center.x - positionCenterOnXY.x;\n    window.setTimeout(function () {\n      draggedEl.style.top = \"\".concat(elTopPx, \"px\");\n      draggedEl.style.left = \"\".concat(elLeftPx, \"px\");\n    }, 0);\n  }\n  draggedEl.style.margin = \"0\";\n  // we can't have relative or automatic height and width or it will break the illusion\n  draggedEl.style.boxSizing = \"border-box\";\n  draggedEl.style.height = \"\".concat(rect.height, \"px\");\n  draggedEl.style.width = \"\".concat(rect.width, \"px\");\n  draggedEl.style.transition = \"\".concat(trs(\"top\"), \", \").concat(trs(\"left\"), \", \").concat(trs(\"background-color\"), \", \").concat(trs(\"opacity\"), \", \").concat(trs(\"color\"), \" \");\n  // this is a workaround for a strange browser bug that causes the right border to disappear when all the transitions are added at the same time\n  window.setTimeout(function () {\n    return draggedEl.style.transition += \", \".concat(trs(\"width\"), \", \").concat(trs(\"height\"));\n  }, 0);\n  draggedEl.style.zIndex = \"9999\";\n  draggedEl.style.cursor = \"grabbing\";\n  return draggedEl;\n}\n\n/**\n * styles the dragged element to a 'dropped' state\n * @param {HTMLElement} draggedEl\n */\nfunction moveDraggedElementToWasDroppedState(draggedEl) {\n  draggedEl.style.cursor = \"grab\";\n}\n\n/**\n * Morphs the dragged element style, maintains the mouse pointer within the element\n * @param {HTMLElement} draggedEl\n * @param {HTMLElement} copyFromEl - the element the dragged element should look like, typically the shadow element\n * @param {number} currentMouseX\n * @param {number} currentMouseY\n */\nfunction morphDraggedElementToBeLike(draggedEl, copyFromEl, currentMouseX, currentMouseY) {\n  copyStylesFromTo(copyFromEl, draggedEl);\n  var newRect = copyFromEl.getBoundingClientRect();\n  var draggedElRect = draggedEl.getBoundingClientRect();\n  var widthChange = newRect.width - draggedElRect.width;\n  var heightChange = newRect.height - draggedElRect.height;\n  if (widthChange || heightChange) {\n    var relativeDistanceOfMousePointerFromDraggedSides = {\n      left: (currentMouseX - draggedElRect.left) / draggedElRect.width,\n      top: (currentMouseY - draggedElRect.top) / draggedElRect.height\n    };\n    if (!getFeatureFlag(FEATURE_FLAG_NAMES.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT)) {\n      draggedEl.style.height = \"\".concat(newRect.height, \"px\");\n      draggedEl.style.width = \"\".concat(newRect.width, \"px\");\n    }\n    draggedEl.style.left = \"\".concat(parseFloat(draggedEl.style.left) - relativeDistanceOfMousePointerFromDraggedSides.left * widthChange, \"px\");\n    draggedEl.style.top = \"\".concat(parseFloat(draggedEl.style.top) - relativeDistanceOfMousePointerFromDraggedSides.top * heightChange, \"px\");\n  }\n}\n\n/**\n * @param {HTMLElement} copyFromEl\n * @param {HTMLElement} copyToEl\n */\nfunction copyStylesFromTo(copyFromEl, copyToEl) {\n  var computedStyle = window.getComputedStyle(copyFromEl);\n  Array.from(computedStyle).filter(function (s) {\n    return s.startsWith(\"background\") || s.startsWith(\"padding\") || s.startsWith(\"font\") || s.startsWith(\"text\") || s.startsWith(\"align\") || s.startsWith(\"justify\") || s.startsWith(\"display\") || s.startsWith(\"flex\") || s.startsWith(\"border\") || s === \"opacity\" || s === \"color\" || s === \"list-style-type\" ||\n    // copying with and height to make up for rect update timing issues in some browsers\n    getFeatureFlag(FEATURE_FLAG_NAMES.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT) && (s === \"width\" || s === \"height\");\n  }).forEach(function (s) {\n    return copyToEl.style.setProperty(s, computedStyle.getPropertyValue(s), computedStyle.getPropertyPriority(s));\n  });\n}\n\n/**\n * makes the element compatible with being draggable\n * @param {HTMLElement} draggableEl\n * @param {boolean} dragDisabled\n */\nfunction styleDraggable(draggableEl, dragDisabled) {\n  draggableEl.draggable = false;\n  draggableEl.ondragstart = function () {\n    return false;\n  };\n  if (!dragDisabled) {\n    draggableEl.style.userSelect = \"none\";\n    draggableEl.style.WebkitUserSelect = \"none\";\n    draggableEl.style.cursor = \"grab\";\n  } else {\n    draggableEl.style.userSelect = \"\";\n    draggableEl.style.WebkitUserSelect = \"\";\n    draggableEl.style.cursor = \"\";\n  }\n}\n\n/**\n * Hides the provided element so that it can stay in the dom without interrupting\n * @param {HTMLElement} dragTarget\n */\nfunction hideElement(dragTarget) {\n  dragTarget.style.display = \"none\";\n  dragTarget.style.position = \"fixed\";\n  dragTarget.style.zIndex = \"-5\";\n}\n\n/**\n * styles the shadow element\n * @param {HTMLElement} shadowEl\n */\nfunction decorateShadowEl(shadowEl) {\n  shadowEl.style.visibility = \"hidden\";\n  shadowEl.setAttribute(SHADOW_ELEMENT_ATTRIBUTE_NAME, \"true\");\n}\n\n/**\n * undo the styles the shadow element\n * @param {HTMLElement} shadowEl\n */\nfunction unDecorateShadowElement(shadowEl) {\n  shadowEl.style.visibility = \"\";\n  shadowEl.removeAttribute(SHADOW_ELEMENT_ATTRIBUTE_NAME);\n}\n\n/**\n * will mark the given dropzones as visually active\n * @param {Array<HTMLElement>} dropZones\n * @param {Function} getStyles - maps a dropzone to a styles object (so the styles can be removed)\n * @param {Function} getClasses - maps a dropzone to a classList\n */\nfunction styleActiveDropZones(dropZones) {\n  var getStyles = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function () {};\n  var getClasses = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return [];\n  };\n  dropZones.forEach(function (dz) {\n    var styles = getStyles(dz);\n    Object.keys(styles).forEach(function (style) {\n      dz.style[style] = styles[style];\n    });\n    getClasses(dz).forEach(function (c) {\n      return dz.classList.add(c);\n    });\n  });\n}\n\n/**\n * will remove the 'active' styling from given dropzones\n * @param {Array<HTMLElement>} dropZones\n * @param {Function} getStyles - maps a dropzone to a styles object\n * @param {Function} getClasses - maps a dropzone to a classList\n */\nfunction styleInactiveDropZones(dropZones) {\n  var getStyles = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function () {};\n  var getClasses = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return [];\n  };\n  dropZones.forEach(function (dz) {\n    var styles = getStyles(dz);\n    Object.keys(styles).forEach(function (style) {\n      dz.style[style] = \"\";\n    });\n    getClasses(dz).forEach(function (c) {\n      return dz.classList.contains(c) && dz.classList.remove(c);\n    });\n  });\n}\n\n/**\n * will prevent the provided element from shrinking by setting its minWidth and minHeight to the current width and height values\n * @param {HTMLElement} el\n * @return {function(): void} - run this function to undo the operation and restore the original values\n */\nfunction preventShrinking(el) {\n  var originalMinHeight = el.style.minHeight;\n  el.style.minHeight = window.getComputedStyle(el).getPropertyValue(\"height\");\n  var originalMinWidth = el.style.minWidth;\n  el.style.minWidth = window.getComputedStyle(el).getPropertyValue(\"width\");\n  return function undo() {\n    el.style.minHeight = originalMinHeight;\n    el.style.minWidth = originalMinWidth;\n  };\n}\n\nvar DEFAULT_DROP_ZONE_TYPE$1 = \"--any--\";\nvar MIN_OBSERVATION_INTERVAL_MS = 100;\nvar DISABLED_OBSERVATION_INTERVAL_MS = 20;\nvar MIN_MOVEMENT_BEFORE_DRAG_START_PX = 3;\nvar DEFAULT_DROP_TARGET_STYLE$1 = {\n  outline: \"rgba(255, 255, 102, 0.7) solid 2px\"\n};\nvar ORIGINAL_DRAGGED_ITEM_MARKER_ATTRIBUTE = \"data-is-dnd-original-dragged-item\";\nvar originalDragTarget;\nvar draggedEl;\nvar draggedElData;\nvar draggedElType;\nvar originDropZone;\nvar originIndex;\nvar shadowElData;\nvar shadowElDropZone;\nvar dragStartMousePosition;\nvar currentMousePosition;\nvar isWorkingOnPreviousDrag = false;\nvar finalizingPreviousDrag = false;\nvar unlockOriginDzMinDimensions;\nvar isDraggedOutsideOfAnyDz = false;\nvar scheduledForRemovalAfterDrop = [];\nvar multiScroller;\n\n// a map from type to a set of drop-zones\nvar typeToDropZones$1 = new Map();\n// important - this is needed because otherwise the config that would be used for everyone is the config of the element that created the event listeners\nvar dzToConfig$1 = new Map();\n// this is needed in order to be able to cleanup old listeners and avoid stale closures issues (as the listener is defined within each zone)\nvar elToMouseDownListener = new WeakMap();\n\n/* drop-zones registration management */\nfunction registerDropZone$1(dropZoneEl, type) {\n  printDebug(function () {\n    return \"registering drop-zone if absent\";\n  });\n  if (!typeToDropZones$1.has(type)) {\n    typeToDropZones$1.set(type, new Set());\n  }\n  if (!typeToDropZones$1.get(type).has(dropZoneEl)) {\n    typeToDropZones$1.get(type).add(dropZoneEl);\n    incrementActiveDropZoneCount();\n  }\n}\nfunction unregisterDropZone$1(dropZoneEl, type) {\n  typeToDropZones$1.get(type)[\"delete\"](dropZoneEl);\n  decrementActiveDropZoneCount();\n  if (typeToDropZones$1.get(type).size === 0) {\n    typeToDropZones$1[\"delete\"](type);\n  }\n}\n\n/* functions to manage observing the dragged element and trigger custom drag-events */\nfunction watchDraggedElement() {\n  printDebug(function () {\n    return \"watching dragged element\";\n  });\n  var dropZones = typeToDropZones$1.get(draggedElType);\n  var _iterator = _createForOfIteratorHelper(dropZones),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var dz = _step.value;\n      dz.addEventListener(DRAGGED_ENTERED_EVENT_NAME, handleDraggedEntered);\n      dz.addEventListener(DRAGGED_LEFT_EVENT_NAME, handleDraggedLeft);\n      dz.addEventListener(DRAGGED_OVER_INDEX_EVENT_NAME, handleDraggedIsOverIndex);\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  window.addEventListener(DRAGGED_LEFT_DOCUMENT_EVENT_NAME, handleDrop$1);\n\n  // it is important that we don't have an interval that is faster than the flip duration because it can cause elements to jump bach and forth\n  var setIntervalMs = Math.max.apply(Math, _toConsumableArray(Array.from(dropZones.keys()).map(function (dz) {\n    return dzToConfig$1.get(dz).dropAnimationDurationMs;\n  })));\n  var observationIntervalMs = setIntervalMs === 0 ? DISABLED_OBSERVATION_INTERVAL_MS : Math.max(setIntervalMs, MIN_OBSERVATION_INTERVAL_MS); // if setIntervalMs is 0 it goes to 20, otherwise it is max between it and min observation.\n  multiScroller = createMultiScroller(dropZones, function () {\n    return currentMousePosition;\n  });\n  observe(draggedEl, dropZones, observationIntervalMs * 1.07, multiScroller);\n}\nfunction unWatchDraggedElement() {\n  printDebug(function () {\n    return \"unwatching dragged element\";\n  });\n  var dropZones = typeToDropZones$1.get(draggedElType);\n  var _iterator2 = _createForOfIteratorHelper(dropZones),\n    _step2;\n  try {\n    for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n      var dz = _step2.value;\n      dz.removeEventListener(DRAGGED_ENTERED_EVENT_NAME, handleDraggedEntered);\n      dz.removeEventListener(DRAGGED_LEFT_EVENT_NAME, handleDraggedLeft);\n      dz.removeEventListener(DRAGGED_OVER_INDEX_EVENT_NAME, handleDraggedIsOverIndex);\n    }\n  } catch (err) {\n    _iterator2.e(err);\n  } finally {\n    _iterator2.f();\n  }\n  window.removeEventListener(DRAGGED_LEFT_DOCUMENT_EVENT_NAME, handleDrop$1);\n  // ensuring multiScroller is not already destroyed before destroying\n  if (multiScroller) {\n    multiScroller.destroy();\n    multiScroller = undefined;\n  }\n  unobserve();\n}\nfunction findShadowElementIdx(items) {\n  return items.findIndex(function (item) {\n    return !!item[SHADOW_ITEM_MARKER_PROPERTY_NAME];\n  });\n}\nfunction createShadowElData(draggedElData) {\n  var _objectSpread2$1;\n  return _objectSpread2(_objectSpread2({}, draggedElData), {}, (_objectSpread2$1 = {}, _defineProperty(_objectSpread2$1, SHADOW_ITEM_MARKER_PROPERTY_NAME, true), _defineProperty(_objectSpread2$1, ITEM_ID_KEY, SHADOW_PLACEHOLDER_ITEM_ID), _objectSpread2$1));\n}\n\n/* custom drag-events handlers */\nfunction handleDraggedEntered(e) {\n  printDebug(function () {\n    return [\"dragged entered\", e.currentTarget, e.detail];\n  });\n  var _dzToConfig$get = dzToConfig$1.get(e.currentTarget),\n    items = _dzToConfig$get.items,\n    dropFromOthersDisabled = _dzToConfig$get.dropFromOthersDisabled;\n  if (dropFromOthersDisabled && e.currentTarget !== originDropZone) {\n    printDebug(function () {\n      return \"ignoring dragged entered because drop is currently disabled\";\n    });\n    return;\n  }\n  isDraggedOutsideOfAnyDz = false;\n  // this deals with another race condition. in rare occasions (super rapid operations) the list hasn't updated yet\n  items = items.filter(function (item) {\n    return item[ITEM_ID_KEY] !== shadowElData[ITEM_ID_KEY];\n  });\n  printDebug(function () {\n    return \"dragged entered items \".concat(toString(items));\n  });\n  if (originDropZone !== e.currentTarget) {\n    var originZoneItems = dzToConfig$1.get(originDropZone).items;\n    var newOriginZoneItems = originZoneItems.filter(function (item) {\n      return !item[SHADOW_ITEM_MARKER_PROPERTY_NAME];\n    });\n    dispatchConsiderEvent(originDropZone, newOriginZoneItems, {\n      trigger: TRIGGERS.DRAGGED_ENTERED_ANOTHER,\n      id: draggedElData[ITEM_ID_KEY],\n      source: SOURCES.POINTER\n    });\n  }\n  var _e$detail$indexObj = e.detail.indexObj,\n    index = _e$detail$indexObj.index,\n    isProximityBased = _e$detail$indexObj.isProximityBased;\n  var shadowElIdx = isProximityBased && index === e.currentTarget.children.length - 1 ? index + 1 : index;\n  shadowElDropZone = e.currentTarget;\n  items.splice(shadowElIdx, 0, shadowElData);\n  dispatchConsiderEvent(e.currentTarget, items, {\n    trigger: TRIGGERS.DRAGGED_ENTERED,\n    id: draggedElData[ITEM_ID_KEY],\n    source: SOURCES.POINTER\n  });\n}\nfunction handleDraggedLeft(e) {\n  // dealing with a rare race condition on extremely rapid clicking and dropping\n  if (!isWorkingOnPreviousDrag) return;\n  printDebug(function () {\n    return [\"dragged left\", e.currentTarget, e.detail];\n  });\n  var _dzToConfig$get2 = dzToConfig$1.get(e.currentTarget),\n    originalItems = _dzToConfig$get2.items,\n    dropFromOthersDisabled = _dzToConfig$get2.dropFromOthersDisabled;\n  if (dropFromOthersDisabled && e.currentTarget !== originDropZone && e.currentTarget !== shadowElDropZone) {\n    printDebug(function () {\n      return \"drop is currently disabled\";\n    });\n    return;\n  }\n  var items = _toConsumableArray(originalItems);\n  var shadowElIdx = findShadowElementIdx(items);\n  if (shadowElIdx !== -1) {\n    items.splice(shadowElIdx, 1);\n  }\n  var origShadowDz = shadowElDropZone;\n  shadowElDropZone = undefined;\n  var _e$detail = e.detail,\n    type = _e$detail.type,\n    theOtherDz = _e$detail.theOtherDz;\n  if (type === DRAGGED_LEFT_TYPES.OUTSIDE_OF_ANY || type === DRAGGED_LEFT_TYPES.LEFT_FOR_ANOTHER && theOtherDz !== originDropZone && dzToConfig$1.get(theOtherDz).dropFromOthersDisabled) {\n    printDebug(function () {\n      return \"dragged left all, putting shadow element back in the origin dz\";\n    });\n    isDraggedOutsideOfAnyDz = true;\n    shadowElDropZone = originDropZone;\n    // if the last zone it left is the origin dz, we will put it back into items (which we just removed it from)\n    var originZoneItems = origShadowDz === originDropZone ? items : _toConsumableArray(dzToConfig$1.get(originDropZone).items);\n    originZoneItems.splice(originIndex, 0, shadowElData);\n    dispatchConsiderEvent(originDropZone, originZoneItems, {\n      trigger: TRIGGERS.DRAGGED_LEFT_ALL,\n      id: draggedElData[ITEM_ID_KEY],\n      source: SOURCES.POINTER\n    });\n  }\n  // for the origin dz, when the dragged is outside of any, this will be fired in addition to the previous. this is for simplicity\n  dispatchConsiderEvent(e.currentTarget, items, {\n    trigger: TRIGGERS.DRAGGED_LEFT,\n    id: draggedElData[ITEM_ID_KEY],\n    source: SOURCES.POINTER\n  });\n}\nfunction handleDraggedIsOverIndex(e) {\n  printDebug(function () {\n    return [\"dragged is over index\", e.currentTarget, e.detail];\n  });\n  var _dzToConfig$get3 = dzToConfig$1.get(e.currentTarget),\n    originalItems = _dzToConfig$get3.items,\n    dropFromOthersDisabled = _dzToConfig$get3.dropFromOthersDisabled;\n  if (dropFromOthersDisabled && e.currentTarget !== originDropZone) {\n    printDebug(function () {\n      return \"drop is currently disabled\";\n    });\n    return;\n  }\n  var items = _toConsumableArray(originalItems);\n  isDraggedOutsideOfAnyDz = false;\n  var index = e.detail.indexObj.index;\n  var shadowElIdx = findShadowElementIdx(items);\n  if (shadowElIdx !== -1) {\n    items.splice(shadowElIdx, 1);\n  }\n  items.splice(index, 0, shadowElData);\n  dispatchConsiderEvent(e.currentTarget, items, {\n    trigger: TRIGGERS.DRAGGED_OVER_INDEX,\n    id: draggedElData[ITEM_ID_KEY],\n    source: SOURCES.POINTER\n  });\n}\n\n// Global mouse/touch-events handlers\nfunction handleMouseMove(e) {\n  e.preventDefault();\n  var c = e.touches ? e.touches[0] : e;\n  currentMousePosition = {\n    x: c.clientX,\n    y: c.clientY\n  };\n  draggedEl.style.transform = \"translate3d(\".concat(currentMousePosition.x - dragStartMousePosition.x, \"px, \").concat(currentMousePosition.y - dragStartMousePosition.y, \"px, 0)\");\n}\nfunction handleDrop$1() {\n  printDebug(function () {\n    return \"dropped\";\n  });\n  finalizingPreviousDrag = true;\n  // cleanup\n  window.removeEventListener(\"mousemove\", handleMouseMove);\n  window.removeEventListener(\"touchmove\", handleMouseMove);\n  window.removeEventListener(\"mouseup\", handleDrop$1);\n  window.removeEventListener(\"touchend\", handleDrop$1);\n  unWatchDraggedElement();\n  moveDraggedElementToWasDroppedState(draggedEl);\n  if (!shadowElDropZone) {\n    printDebug(function () {\n      return \"element was dropped right after it left origin but before entering somewhere else\";\n    });\n    shadowElDropZone = originDropZone;\n  }\n  printDebug(function () {\n    return [\"dropped in dz\", shadowElDropZone];\n  });\n  var _dzToConfig$get4 = dzToConfig$1.get(shadowElDropZone),\n    items = _dzToConfig$get4.items,\n    type = _dzToConfig$get4.type;\n  styleInactiveDropZones(typeToDropZones$1.get(type), function (dz) {\n    return dzToConfig$1.get(dz).dropTargetStyle;\n  }, function (dz) {\n    return dzToConfig$1.get(dz).dropTargetClasses;\n  });\n  var shadowElIdx = findShadowElementIdx(items);\n  // the handler might remove the shadow element, ex: dragula like copy on drag\n  if (shadowElIdx === -1) {\n    if (shadowElDropZone === originDropZone) {\n      shadowElIdx = originIndex;\n    }\n  }\n  items = items.map(function (item) {\n    return item[SHADOW_ITEM_MARKER_PROPERTY_NAME] ? draggedElData : item;\n  });\n  function finalizeWithinZone() {\n    unlockOriginDzMinDimensions();\n    dispatchFinalizeEvent(shadowElDropZone, items, {\n      trigger: isDraggedOutsideOfAnyDz ? TRIGGERS.DROPPED_OUTSIDE_OF_ANY : TRIGGERS.DROPPED_INTO_ZONE,\n      id: draggedElData[ITEM_ID_KEY],\n      source: SOURCES.POINTER\n    });\n    if (shadowElDropZone !== originDropZone) {\n      // letting the origin drop zone know the element was permanently taken away\n      dispatchFinalizeEvent(originDropZone, dzToConfig$1.get(originDropZone).items, {\n        trigger: TRIGGERS.DROPPED_INTO_ANOTHER,\n        id: draggedElData[ITEM_ID_KEY],\n        source: SOURCES.POINTER\n      });\n    }\n    // In edge cases the dom might have not been updated yet so we can't rely on data list index\n    var domShadowEl = Array.from(shadowElDropZone.children).find(function (c) {\n      return c.getAttribute(SHADOW_ELEMENT_ATTRIBUTE_NAME);\n    });\n    if (domShadowEl) unDecorateShadowElement(domShadowEl);\n    cleanupPostDrop();\n  }\n  if (dzToConfig$1.get(shadowElDropZone).dropAnimationDisabled) {\n    finalizeWithinZone();\n  } else {\n    animateDraggedToFinalPosition(shadowElIdx, finalizeWithinZone);\n  }\n}\n\n// helper function for handleDrop\nfunction animateDraggedToFinalPosition(shadowElIdx, callback) {\n  var shadowElRect = shadowElIdx > -1 ? getBoundingRectNoTransforms(shadowElDropZone.children[shadowElIdx], false) : getBoundingRectNoTransforms(shadowElDropZone, false);\n  var newTransform = {\n    x: shadowElRect.left - parseFloat(draggedEl.style.left),\n    y: shadowElRect.top - parseFloat(draggedEl.style.top)\n  };\n  var _dzToConfig$get5 = dzToConfig$1.get(shadowElDropZone),\n    dropAnimationDurationMs = _dzToConfig$get5.dropAnimationDurationMs;\n  var transition = \"transform \".concat(dropAnimationDurationMs, \"ms ease\");\n  draggedEl.style.transition = draggedEl.style.transition ? draggedEl.style.transition + \",\" + transition : transition;\n  draggedEl.style.transform = \"translate3d(\".concat(newTransform.x, \"px, \").concat(newTransform.y, \"px, 0)\");\n  window.setTimeout(callback, dropAnimationDurationMs);\n}\nfunction scheduleDZForRemovalAfterDrop(dz, destroy) {\n  scheduledForRemovalAfterDrop.push({\n    dz: dz,\n    destroy: destroy\n  });\n  window.requestAnimationFrame(function () {\n    hideElement(dz);\n    document.body.appendChild(dz);\n  });\n}\n/* cleanup */\nfunction cleanupPostDrop() {\n  draggedEl.remove();\n  originalDragTarget.remove();\n  if (scheduledForRemovalAfterDrop.length) {\n    printDebug(function () {\n      return [\"will destroy zones that were removed during drag\", scheduledForRemovalAfterDrop];\n    });\n    scheduledForRemovalAfterDrop.forEach(function (_ref) {\n      var dz = _ref.dz,\n        destroy = _ref.destroy;\n      destroy();\n      dz.remove();\n    });\n    scheduledForRemovalAfterDrop = [];\n  }\n  draggedEl = undefined;\n  originalDragTarget = undefined;\n  draggedElData = undefined;\n  draggedElType = undefined;\n  originDropZone = undefined;\n  originIndex = undefined;\n  shadowElData = undefined;\n  shadowElDropZone = undefined;\n  dragStartMousePosition = undefined;\n  currentMousePosition = undefined;\n  isWorkingOnPreviousDrag = false;\n  finalizingPreviousDrag = false;\n  unlockOriginDzMinDimensions = undefined;\n  isDraggedOutsideOfAnyDz = false;\n}\nfunction dndzone$2(node, options) {\n  var initialized = false;\n  var config = {\n    items: undefined,\n    type: undefined,\n    flipDurationMs: 0,\n    dragDisabled: false,\n    morphDisabled: false,\n    dropFromOthersDisabled: false,\n    dropTargetStyle: DEFAULT_DROP_TARGET_STYLE$1,\n    dropTargetClasses: [],\n    transformDraggedElement: function transformDraggedElement() {},\n    centreDraggedOnCursor: false,\n    dropAnimationDisabled: false\n  };\n  printDebug(function () {\n    return [\"dndzone good to go options: \".concat(toString(options), \", config: \").concat(toString(config)), {\n      node: node\n    }];\n  });\n  var elToIdx = new Map();\n  function addMaybeListeners() {\n    window.addEventListener(\"mousemove\", handleMouseMoveMaybeDragStart, {\n      passive: false\n    });\n    window.addEventListener(\"touchmove\", handleMouseMoveMaybeDragStart, {\n      passive: false,\n      capture: false\n    });\n    window.addEventListener(\"mouseup\", handleFalseAlarm, {\n      passive: false\n    });\n    window.addEventListener(\"touchend\", handleFalseAlarm, {\n      passive: false\n    });\n  }\n  function removeMaybeListeners() {\n    window.removeEventListener(\"mousemove\", handleMouseMoveMaybeDragStart);\n    window.removeEventListener(\"touchmove\", handleMouseMoveMaybeDragStart);\n    window.removeEventListener(\"mouseup\", handleFalseAlarm);\n    window.removeEventListener(\"touchend\", handleFalseAlarm);\n  }\n  function handleFalseAlarm(e) {\n    removeMaybeListeners();\n    originalDragTarget = undefined;\n    dragStartMousePosition = undefined;\n    currentMousePosition = undefined;\n\n    // dragging initiated by touch events prevents onclick from initially firing\n    if (e.type === \"touchend\") {\n      var clickEvent = new Event(\"click\", {\n        bubbles: true,\n        cancelable: true\n      });\n      // doing it this way instead of calling .click() because that doesn't work for SVG elements\n      e.target.dispatchEvent(clickEvent);\n    }\n  }\n  function handleMouseMoveMaybeDragStart(e) {\n    e.preventDefault();\n    var c = e.touches ? e.touches[0] : e;\n    currentMousePosition = {\n      x: c.clientX,\n      y: c.clientY\n    };\n    if (Math.abs(currentMousePosition.x - dragStartMousePosition.x) >= MIN_MOVEMENT_BEFORE_DRAG_START_PX || Math.abs(currentMousePosition.y - dragStartMousePosition.y) >= MIN_MOVEMENT_BEFORE_DRAG_START_PX) {\n      removeMaybeListeners();\n      handleDragStart();\n    }\n  }\n  function handleMouseDown(e) {\n    // on safari clicking on a select element doesn't fire mouseup at the end of the click and in general this makes more sense\n    if (e.target !== e.currentTarget && (e.target.value !== undefined || e.target.isContentEditable)) {\n      printDebug(function () {\n        return \"won't initiate drag on a nested input element\";\n      });\n      return;\n    }\n    // prevents responding to any button but left click which equals 0 (which is falsy)\n    if (e.button) {\n      printDebug(function () {\n        return \"ignoring none left click button: \".concat(e.button);\n      });\n      return;\n    }\n    if (isWorkingOnPreviousDrag) {\n      printDebug(function () {\n        return \"cannot start a new drag before finalizing previous one\";\n      });\n      return;\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    var c = e.touches ? e.touches[0] : e;\n    dragStartMousePosition = {\n      x: c.clientX,\n      y: c.clientY\n    };\n    currentMousePosition = _objectSpread2({}, dragStartMousePosition);\n    originalDragTarget = e.currentTarget;\n    addMaybeListeners();\n  }\n  function handleDragStart() {\n    printDebug(function () {\n      return [\"drag start config: \".concat(toString(config)), originalDragTarget];\n    });\n    isWorkingOnPreviousDrag = true;\n\n    // initialising globals\n    var currentIdx = elToIdx.get(originalDragTarget);\n    originIndex = currentIdx;\n    originDropZone = originalDragTarget.parentElement;\n    /** @type {ShadowRoot | HTMLDocument | Element } */\n    var rootNode = originDropZone.closest(\"dialog\") || originDropZone.closest(\"[popover]\") || originDropZone.getRootNode();\n    var originDropZoneRoot = rootNode.body || rootNode;\n    var originalItems = config.items,\n      type = config.type,\n      centreDraggedOnCursor = config.centreDraggedOnCursor;\n    var items = _toConsumableArray(originalItems);\n    draggedElData = items[currentIdx];\n    draggedElType = type;\n    shadowElData = createShadowElData(draggedElData);\n\n    // creating the draggable element\n    draggedEl = createDraggedElementFrom(originalDragTarget, centreDraggedOnCursor && currentMousePosition);\n    originDropZoneRoot.appendChild(draggedEl);\n    // We will keep the original dom node in the dom because touch events keep firing on it, we want to re-add it after the framework removes it\n    function keepOriginalElementInDom() {\n      if (!originalDragTarget.parentElement) {\n        originalDragTarget.setAttribute(ORIGINAL_DRAGGED_ITEM_MARKER_ATTRIBUTE, true);\n        originDropZoneRoot.appendChild(originalDragTarget);\n        // have to watch before we hide, otherwise Svelte 5 $state gets confused\n        watchDraggedElement();\n        hideElement(originalDragTarget);\n        // after the removal of the original element we can give the shadow element the original item id so that the host zone can find it and render it correctly if it does lookups by id\n        shadowElData[ITEM_ID_KEY] = draggedElData[ITEM_ID_KEY];\n        // to prevent the outline from disappearing\n        draggedEl.focus();\n      } else {\n        window.requestAnimationFrame(keepOriginalElementInDom);\n      }\n    }\n    window.requestAnimationFrame(keepOriginalElementInDom);\n    styleActiveDropZones(Array.from(typeToDropZones$1.get(config.type)).filter(function (dz) {\n      return dz === originDropZone || !dzToConfig$1.get(dz).dropFromOthersDisabled;\n    }), function (dz) {\n      return dzToConfig$1.get(dz).dropTargetStyle;\n    }, function (dz) {\n      return dzToConfig$1.get(dz).dropTargetClasses;\n    });\n\n    // removing the original element by removing its data entry\n    items.splice(currentIdx, 1, shadowElData);\n    unlockOriginDzMinDimensions = preventShrinking(originDropZone);\n    dispatchConsiderEvent(originDropZone, items, {\n      trigger: TRIGGERS.DRAG_STARTED,\n      id: draggedElData[ITEM_ID_KEY],\n      source: SOURCES.POINTER\n    });\n\n    // handing over to global handlers - starting to watch the element\n    window.addEventListener(\"mousemove\", handleMouseMove, {\n      passive: false\n    });\n    window.addEventListener(\"touchmove\", handleMouseMove, {\n      passive: false,\n      capture: false\n    });\n    window.addEventListener(\"mouseup\", handleDrop$1, {\n      passive: false\n    });\n    window.addEventListener(\"touchend\", handleDrop$1, {\n      passive: false\n    });\n  }\n  function configure(_ref2) {\n    var _ref2$items = _ref2.items,\n      items = _ref2$items === void 0 ? undefined : _ref2$items,\n      _ref2$flipDurationMs = _ref2.flipDurationMs,\n      dropAnimationDurationMs = _ref2$flipDurationMs === void 0 ? 0 : _ref2$flipDurationMs,\n      _ref2$type = _ref2.type,\n      newType = _ref2$type === void 0 ? DEFAULT_DROP_ZONE_TYPE$1 : _ref2$type,\n      _ref2$dragDisabled = _ref2.dragDisabled,\n      dragDisabled = _ref2$dragDisabled === void 0 ? false : _ref2$dragDisabled,\n      _ref2$morphDisabled = _ref2.morphDisabled,\n      morphDisabled = _ref2$morphDisabled === void 0 ? false : _ref2$morphDisabled,\n      _ref2$dropFromOthersD = _ref2.dropFromOthersDisabled,\n      dropFromOthersDisabled = _ref2$dropFromOthersD === void 0 ? false : _ref2$dropFromOthersD,\n      _ref2$dropTargetStyle = _ref2.dropTargetStyle,\n      dropTargetStyle = _ref2$dropTargetStyle === void 0 ? DEFAULT_DROP_TARGET_STYLE$1 : _ref2$dropTargetStyle,\n      _ref2$dropTargetClass = _ref2.dropTargetClasses,\n      dropTargetClasses = _ref2$dropTargetClass === void 0 ? [] : _ref2$dropTargetClass,\n      _ref2$transformDragge = _ref2.transformDraggedElement,\n      transformDraggedElement = _ref2$transformDragge === void 0 ? function () {} : _ref2$transformDragge,\n      _ref2$centreDraggedOn = _ref2.centreDraggedOnCursor,\n      centreDraggedOnCursor = _ref2$centreDraggedOn === void 0 ? false : _ref2$centreDraggedOn,\n      _ref2$dropAnimationDi = _ref2.dropAnimationDisabled,\n      dropAnimationDisabled = _ref2$dropAnimationDi === void 0 ? false : _ref2$dropAnimationDi;\n    config.dropAnimationDurationMs = dropAnimationDurationMs;\n    if (config.type && newType !== config.type) {\n      unregisterDropZone$1(node, config.type);\n    }\n    config.type = newType;\n    config.items = _toConsumableArray(items);\n    config.dragDisabled = dragDisabled;\n    config.morphDisabled = morphDisabled;\n    config.transformDraggedElement = transformDraggedElement;\n    config.centreDraggedOnCursor = centreDraggedOnCursor;\n    config.dropAnimationDisabled = dropAnimationDisabled;\n\n    // realtime update for dropTargetStyle\n    if (initialized && isWorkingOnPreviousDrag && !finalizingPreviousDrag && (!areObjectsShallowEqual(dropTargetStyle, config.dropTargetStyle) || !areArraysShallowEqualSameOrder(dropTargetClasses, config.dropTargetClasses))) {\n      styleInactiveDropZones([node], function () {\n        return config.dropTargetStyle;\n      }, function () {\n        return dropTargetClasses;\n      });\n      styleActiveDropZones([node], function () {\n        return dropTargetStyle;\n      }, function () {\n        return dropTargetClasses;\n      });\n    }\n    config.dropTargetStyle = dropTargetStyle;\n    config.dropTargetClasses = _toConsumableArray(dropTargetClasses);\n\n    // realtime update for dropFromOthersDisabled\n    function getConfigProp(dz, propName) {\n      return dzToConfig$1.get(dz) ? dzToConfig$1.get(dz)[propName] : config[propName];\n    }\n    if (initialized && isWorkingOnPreviousDrag && config.dropFromOthersDisabled !== dropFromOthersDisabled) {\n      if (dropFromOthersDisabled) {\n        styleInactiveDropZones([node], function (dz) {\n          return getConfigProp(dz, \"dropTargetStyle\");\n        }, function (dz) {\n          return getConfigProp(dz, \"dropTargetClasses\");\n        });\n      } else {\n        styleActiveDropZones([node], function (dz) {\n          return getConfigProp(dz, \"dropTargetStyle\");\n        }, function (dz) {\n          return getConfigProp(dz, \"dropTargetClasses\");\n        });\n      }\n    }\n    config.dropFromOthersDisabled = dropFromOthersDisabled;\n    dzToConfig$1.set(node, config);\n    registerDropZone$1(node, newType);\n    var shadowElIdx = isWorkingOnPreviousDrag ? findShadowElementIdx(config.items) : -1;\n    for (var idx = 0; idx < node.children.length; idx++) {\n      var draggableEl = node.children[idx];\n      styleDraggable(draggableEl, dragDisabled);\n      if (idx === shadowElIdx) {\n        if (!morphDisabled) {\n          morphDraggedElementToBeLike(draggedEl, draggableEl, currentMousePosition.x, currentMousePosition.y);\n        }\n        config.transformDraggedElement(draggedEl, draggedElData, idx);\n        decorateShadowEl(draggableEl);\n        continue;\n      }\n      draggableEl.removeEventListener(\"mousedown\", elToMouseDownListener.get(draggableEl));\n      draggableEl.removeEventListener(\"touchstart\", elToMouseDownListener.get(draggableEl));\n      if (!dragDisabled) {\n        draggableEl.addEventListener(\"mousedown\", handleMouseDown);\n        draggableEl.addEventListener(\"touchstart\", handleMouseDown);\n        elToMouseDownListener.set(draggableEl, handleMouseDown);\n      }\n      // updating the idx\n      elToIdx.set(draggableEl, idx);\n      if (!initialized) {\n        initialized = true;\n      }\n    }\n  }\n  configure(options);\n  return {\n    update: function update(newOptions) {\n      printDebug(function () {\n        return \"pointer dndzone will update newOptions: \".concat(toString(newOptions));\n      });\n      configure(newOptions);\n    },\n    destroy: function destroy() {\n      function destroyDz() {\n        printDebug(function () {\n          return \"pointer dndzone will destroy\";\n        });\n        unregisterDropZone$1(node, dzToConfig$1.get(node).type);\n        dzToConfig$1[\"delete\"](node);\n      }\n      if (isWorkingOnPreviousDrag && !node.closest(\"[\".concat(ORIGINAL_DRAGGED_ITEM_MARKER_ATTRIBUTE, \"]\"))) {\n        printDebug(function () {\n          return \"pointer dndzone will be scheduled for destruction\";\n        });\n        scheduleDZForRemovalAfterDrop(node, destroyDz);\n      } else {\n        destroyDz();\n      }\n    }\n  };\n}\n\nvar _ID_TO_INSTRUCTION;\nvar INSTRUCTION_IDs$1 = {\n  DND_ZONE_ACTIVE: \"dnd-zone-active\",\n  DND_ZONE_DRAG_DISABLED: \"dnd-zone-drag-disabled\"\n};\nvar ID_TO_INSTRUCTION = (_ID_TO_INSTRUCTION = {}, _defineProperty(_ID_TO_INSTRUCTION, INSTRUCTION_IDs$1.DND_ZONE_ACTIVE, \"Tab to one the items and press space-bar or enter to start dragging it\"), _defineProperty(_ID_TO_INSTRUCTION, INSTRUCTION_IDs$1.DND_ZONE_DRAG_DISABLED, \"This is a disabled drag and drop list\"), _ID_TO_INSTRUCTION);\nvar ALERT_DIV_ID = \"dnd-action-aria-alert\";\nvar alertsDiv;\nfunction initAriaOnBrowser() {\n  if (alertsDiv) {\n    // it is already initialized\n    return;\n  }\n  // setting the dynamic alerts\n  alertsDiv = document.createElement(\"div\");\n  (function initAlertsDiv() {\n    alertsDiv.id = ALERT_DIV_ID;\n    // tab index -1 makes the alert be read twice on chrome for some reason\n    //alertsDiv.tabIndex = -1;\n    alertsDiv.style.position = \"fixed\";\n    alertsDiv.style.bottom = \"0\";\n    alertsDiv.style.left = \"0\";\n    alertsDiv.style.zIndex = \"-5\";\n    alertsDiv.style.opacity = \"0\";\n    alertsDiv.style.height = \"0\";\n    alertsDiv.style.width = \"0\";\n    alertsDiv.setAttribute(\"role\", \"alert\");\n  })();\n  document.body.prepend(alertsDiv);\n\n  // setting the instructions\n  Object.entries(ID_TO_INSTRUCTION).forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      id = _ref2[0],\n      txt = _ref2[1];\n    return document.body.prepend(instructionToHiddenDiv(id, txt));\n  });\n}\n\n/**\n * Initializes the static aria instructions so they can be attached to zones\n * @return {{DND_ZONE_ACTIVE: string, DND_ZONE_DRAG_DISABLED: string} | null} - the IDs for static aria instruction (to be used via aria-describedby) or null on the server\n */\nfunction initAria() {\n  if (isOnServer) return null;\n  if (document.readyState === \"complete\") {\n    initAriaOnBrowser();\n  } else {\n    window.addEventListener(\"DOMContentLoaded\", initAriaOnBrowser);\n  }\n  return _objectSpread2({}, INSTRUCTION_IDs$1);\n}\n\n/**\n * Removes all the artifacts (dom elements) added by this module\n */\nfunction destroyAria() {\n  if (isOnServer || !alertsDiv) return;\n  Object.keys(ID_TO_INSTRUCTION).forEach(function (id) {\n    var _document$getElementB;\n    return (_document$getElementB = document.getElementById(id)) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.remove();\n  });\n  alertsDiv.remove();\n  alertsDiv = undefined;\n}\nfunction instructionToHiddenDiv(id, txt) {\n  var div = document.createElement(\"div\");\n  div.id = id;\n  div.innerHTML = \"<p>\".concat(txt, \"</p>\");\n  div.style.display = \"none\";\n  div.style.position = \"fixed\";\n  div.style.zIndex = \"-5\";\n  return div;\n}\n\n/**\n * Will make the screen reader alert the provided text to the user\n * @param {string} txt\n */\nfunction alertToScreenReader(txt) {\n  if (isOnServer) return;\n  if (!alertsDiv) {\n    initAriaOnBrowser();\n  }\n  alertsDiv.innerHTML = \"\";\n  var alertText = document.createTextNode(txt);\n  alertsDiv.appendChild(alertText);\n  // this is needed for Safari\n  alertsDiv.style.display = \"none\";\n  alertsDiv.style.display = \"inline\";\n}\n\nvar DEFAULT_DROP_ZONE_TYPE = \"--any--\";\nvar DEFAULT_DROP_TARGET_STYLE = {\n  outline: \"rgba(255, 255, 102, 0.7) solid 2px\"\n};\nvar isDragging = false;\nvar draggedItemType;\nvar focusedDz;\nvar focusedDzLabel = \"\";\nvar focusedItem;\nvar focusedItemId;\nvar focusedItemLabel = \"\";\nvar allDragTargets = new WeakSet();\nvar elToKeyDownListeners = new WeakMap();\nvar elToFocusListeners = new WeakMap();\nvar dzToHandles = new Map();\nvar dzToConfig = new Map();\nvar typeToDropZones = new Map();\n\n/* TODO (potentially)\n * what's the deal with the black border of voice-reader not following focus?\n * maybe keep focus on the last dragged item upon drop?\n */\n\nvar INSTRUCTION_IDs;\n\n/* drop-zones registration management */\nfunction registerDropZone(dropZoneEl, type) {\n  printDebug(function () {\n    return \"registering drop-zone if absent\";\n  });\n  if (typeToDropZones.size === 0) {\n    printDebug(function () {\n      return \"adding global keydown and click handlers\";\n    });\n    INSTRUCTION_IDs = initAria();\n    window.addEventListener(\"keydown\", globalKeyDownHandler);\n    window.addEventListener(\"click\", globalClickHandler);\n  }\n  if (!typeToDropZones.has(type)) {\n    typeToDropZones.set(type, new Set());\n  }\n  if (!typeToDropZones.get(type).has(dropZoneEl)) {\n    typeToDropZones.get(type).add(dropZoneEl);\n    incrementActiveDropZoneCount();\n  }\n}\nfunction unregisterDropZone(dropZoneEl, type) {\n  printDebug(function () {\n    return \"unregistering drop-zone\";\n  });\n  if (focusedDz === dropZoneEl) {\n    handleDrop();\n  }\n  typeToDropZones.get(type)[\"delete\"](dropZoneEl);\n  decrementActiveDropZoneCount();\n  if (typeToDropZones.get(type).size === 0) {\n    typeToDropZones[\"delete\"](type);\n  }\n  if (typeToDropZones.size === 0) {\n    printDebug(function () {\n      return \"removing global keydown and click handlers\";\n    });\n    window.removeEventListener(\"keydown\", globalKeyDownHandler);\n    window.removeEventListener(\"click\", globalClickHandler);\n    INSTRUCTION_IDs = undefined;\n    destroyAria();\n  }\n}\nfunction globalKeyDownHandler(e) {\n  if (!isDragging) return;\n  switch (e.key) {\n    case \"Escape\":\n      {\n        handleDrop();\n        break;\n      }\n  }\n}\nfunction globalClickHandler() {\n  if (!isDragging) return;\n  if (!allDragTargets.has(document.activeElement)) {\n    printDebug(function () {\n      return \"clicked outside of any draggable\";\n    });\n    handleDrop();\n  }\n}\nfunction handleZoneFocus(e) {\n  printDebug(function () {\n    return \"zone focus\";\n  });\n  if (!isDragging) return;\n  var newlyFocusedDz = e.currentTarget;\n  if (newlyFocusedDz === focusedDz) return;\n  focusedDzLabel = newlyFocusedDz.getAttribute(\"aria-label\") || \"\";\n  var _dzToConfig$get = dzToConfig.get(focusedDz),\n    originItems = _dzToConfig$get.items;\n  var originItem = originItems.find(function (item) {\n    return item[ITEM_ID_KEY] === focusedItemId;\n  });\n  var originIdx = originItems.indexOf(originItem);\n  var itemToMove = originItems.splice(originIdx, 1)[0];\n  var _dzToConfig$get2 = dzToConfig.get(newlyFocusedDz),\n    targetItems = _dzToConfig$get2.items,\n    autoAriaDisabled = _dzToConfig$get2.autoAriaDisabled;\n  if (newlyFocusedDz.getBoundingClientRect().top < focusedDz.getBoundingClientRect().top || newlyFocusedDz.getBoundingClientRect().left < focusedDz.getBoundingClientRect().left) {\n    targetItems.push(itemToMove);\n    if (!autoAriaDisabled) {\n      alertToScreenReader(\"Moved item \".concat(focusedItemLabel, \" to the end of the list \").concat(focusedDzLabel));\n    }\n  } else {\n    targetItems.unshift(itemToMove);\n    if (!autoAriaDisabled) {\n      alertToScreenReader(\"Moved item \".concat(focusedItemLabel, \" to the beginning of the list \").concat(focusedDzLabel));\n    }\n  }\n  var dzFrom = focusedDz;\n  dispatchFinalizeEvent(dzFrom, originItems, {\n    trigger: TRIGGERS.DROPPED_INTO_ANOTHER,\n    id: focusedItemId,\n    source: SOURCES.KEYBOARD\n  });\n  dispatchFinalizeEvent(newlyFocusedDz, targetItems, {\n    trigger: TRIGGERS.DROPPED_INTO_ZONE,\n    id: focusedItemId,\n    source: SOURCES.KEYBOARD\n  });\n  focusedDz = newlyFocusedDz;\n}\nfunction triggerAllDzsUpdate() {\n  dzToHandles.forEach(function (_ref, dz) {\n    var update = _ref.update;\n    return update(dzToConfig.get(dz));\n  });\n}\nfunction handleDrop() {\n  var dispatchConsider = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  printDebug(function () {\n    return \"drop\";\n  });\n  if (!dzToConfig.get(focusedDz).autoAriaDisabled) {\n    alertToScreenReader(\"Stopped dragging item \".concat(focusedItemLabel));\n  }\n  if (allDragTargets.has(document.activeElement)) {\n    document.activeElement.blur();\n  }\n  if (dispatchConsider) {\n    dispatchConsiderEvent(focusedDz, dzToConfig.get(focusedDz).items, {\n      trigger: TRIGGERS.DRAG_STOPPED,\n      id: focusedItemId,\n      source: SOURCES.KEYBOARD\n    });\n  }\n  styleInactiveDropZones(typeToDropZones.get(draggedItemType), function (dz) {\n    return dzToConfig.get(dz).dropTargetStyle;\n  }, function (dz) {\n    return dzToConfig.get(dz).dropTargetClasses;\n  });\n  focusedItem = null;\n  focusedItemId = null;\n  focusedItemLabel = \"\";\n  draggedItemType = null;\n  focusedDz = null;\n  focusedDzLabel = \"\";\n  isDragging = false;\n  triggerAllDzsUpdate();\n}\n//////\nfunction dndzone$1(node, options) {\n  var config = {\n    items: undefined,\n    type: undefined,\n    dragDisabled: false,\n    zoneTabIndex: 0,\n    zoneItemTabIndex: 0,\n    dropFromOthersDisabled: false,\n    dropTargetStyle: DEFAULT_DROP_TARGET_STYLE,\n    dropTargetClasses: [],\n    autoAriaDisabled: false\n  };\n  function swap(arr, i, j) {\n    if (arr.length <= 1) return;\n    arr.splice(j, 1, arr.splice(i, 1, arr[j])[0]);\n  }\n  function handleKeyDown(e) {\n    printDebug(function () {\n      return [\"handling key down\", e.key];\n    });\n    switch (e.key) {\n      case \"Enter\":\n      case \" \":\n        {\n          // we don't want to affect nested input elements or clickable elements\n          if ((e.target.disabled !== undefined || e.target.href || e.target.isContentEditable) && !allDragTargets.has(e.target)) {\n            return;\n          }\n          e.preventDefault(); // preventing scrolling on spacebar\n          e.stopPropagation();\n          if (isDragging) {\n            // TODO - should this trigger a drop? only here or in general (as in when hitting space or enter outside of any zone)?\n            handleDrop();\n          } else {\n            // drag start\n            handleDragStart(e);\n          }\n          break;\n        }\n      case \"ArrowDown\":\n      case \"ArrowRight\":\n        {\n          if (!isDragging) return;\n          e.preventDefault(); // prevent scrolling\n          e.stopPropagation();\n          var _dzToConfig$get3 = dzToConfig.get(node),\n            items = _dzToConfig$get3.items;\n          var children = Array.from(node.children);\n          var idx = children.indexOf(e.currentTarget);\n          printDebug(function () {\n            return [\"arrow down\", idx];\n          });\n          if (idx < children.length - 1) {\n            if (!config.autoAriaDisabled) {\n              alertToScreenReader(\"Moved item \".concat(focusedItemLabel, \" to position \").concat(idx + 2, \" in the list \").concat(focusedDzLabel));\n            }\n            swap(items, idx, idx + 1);\n            dispatchFinalizeEvent(node, items, {\n              trigger: TRIGGERS.DROPPED_INTO_ZONE,\n              id: focusedItemId,\n              source: SOURCES.KEYBOARD\n            });\n          }\n          break;\n        }\n      case \"ArrowUp\":\n      case \"ArrowLeft\":\n        {\n          if (!isDragging) return;\n          e.preventDefault(); // prevent scrolling\n          e.stopPropagation();\n          var _dzToConfig$get4 = dzToConfig.get(node),\n            _items = _dzToConfig$get4.items;\n          var _children = Array.from(node.children);\n          var _idx = _children.indexOf(e.currentTarget);\n          printDebug(function () {\n            return [\"arrow up\", _idx];\n          });\n          if (_idx > 0) {\n            if (!config.autoAriaDisabled) {\n              alertToScreenReader(\"Moved item \".concat(focusedItemLabel, \" to position \").concat(_idx, \" in the list \").concat(focusedDzLabel));\n            }\n            swap(_items, _idx, _idx - 1);\n            dispatchFinalizeEvent(node, _items, {\n              trigger: TRIGGERS.DROPPED_INTO_ZONE,\n              id: focusedItemId,\n              source: SOURCES.KEYBOARD\n            });\n          }\n          break;\n        }\n    }\n  }\n  function handleDragStart(e) {\n    printDebug(function () {\n      return \"drag start\";\n    });\n    setCurrentFocusedItem(e.currentTarget);\n    focusedDz = node;\n    draggedItemType = config.type;\n    isDragging = true;\n    var dropTargets = Array.from(typeToDropZones.get(config.type)).filter(function (dz) {\n      return dz === focusedDz || !dzToConfig.get(dz).dropFromOthersDisabled;\n    });\n    styleActiveDropZones(dropTargets, function (dz) {\n      return dzToConfig.get(dz).dropTargetStyle;\n    }, function (dz) {\n      return dzToConfig.get(dz).dropTargetClasses;\n    });\n    if (!config.autoAriaDisabled) {\n      var msg = \"Started dragging item \".concat(focusedItemLabel, \". Use the arrow keys to move it within its list \").concat(focusedDzLabel);\n      if (dropTargets.length > 1) {\n        msg += \", or tab to another list in order to move the item into it\";\n      }\n      alertToScreenReader(msg);\n    }\n    dispatchConsiderEvent(node, dzToConfig.get(node).items, {\n      trigger: TRIGGERS.DRAG_STARTED,\n      id: focusedItemId,\n      source: SOURCES.KEYBOARD\n    });\n    triggerAllDzsUpdate();\n  }\n  function handleClick(e) {\n    if (!isDragging) return;\n    if (e.currentTarget === focusedItem) return;\n    e.stopPropagation();\n    handleDrop(false);\n    handleDragStart(e);\n  }\n  function setCurrentFocusedItem(draggableEl) {\n    var _dzToConfig$get5 = dzToConfig.get(node),\n      items = _dzToConfig$get5.items;\n    var children = Array.from(node.children);\n    var focusedItemIdx = children.indexOf(draggableEl);\n    focusedItem = draggableEl;\n    focusedItem.tabIndex = config.zoneItemTabIndex;\n    focusedItemId = items[focusedItemIdx][ITEM_ID_KEY];\n    focusedItemLabel = children[focusedItemIdx].getAttribute(\"aria-label\") || \"\";\n  }\n  function configure(_ref2) {\n    var _ref2$items = _ref2.items,\n      items = _ref2$items === void 0 ? [] : _ref2$items,\n      _ref2$type = _ref2.type,\n      newType = _ref2$type === void 0 ? DEFAULT_DROP_ZONE_TYPE : _ref2$type,\n      _ref2$dragDisabled = _ref2.dragDisabled,\n      dragDisabled = _ref2$dragDisabled === void 0 ? false : _ref2$dragDisabled,\n      _ref2$zoneTabIndex = _ref2.zoneTabIndex,\n      zoneTabIndex = _ref2$zoneTabIndex === void 0 ? 0 : _ref2$zoneTabIndex,\n      _ref2$zoneItemTabInde = _ref2.zoneItemTabIndex,\n      zoneItemTabIndex = _ref2$zoneItemTabInde === void 0 ? 0 : _ref2$zoneItemTabInde,\n      _ref2$dropFromOthersD = _ref2.dropFromOthersDisabled,\n      dropFromOthersDisabled = _ref2$dropFromOthersD === void 0 ? false : _ref2$dropFromOthersD,\n      _ref2$dropTargetStyle = _ref2.dropTargetStyle,\n      dropTargetStyle = _ref2$dropTargetStyle === void 0 ? DEFAULT_DROP_TARGET_STYLE : _ref2$dropTargetStyle,\n      _ref2$dropTargetClass = _ref2.dropTargetClasses,\n      dropTargetClasses = _ref2$dropTargetClass === void 0 ? [] : _ref2$dropTargetClass,\n      _ref2$autoAriaDisable = _ref2.autoAriaDisabled,\n      autoAriaDisabled = _ref2$autoAriaDisable === void 0 ? false : _ref2$autoAriaDisable;\n    config.items = _toConsumableArray(items);\n    config.dragDisabled = dragDisabled;\n    config.dropFromOthersDisabled = dropFromOthersDisabled;\n    config.zoneTabIndex = zoneTabIndex;\n    config.zoneItemTabIndex = zoneItemTabIndex;\n    config.dropTargetStyle = dropTargetStyle;\n    config.dropTargetClasses = dropTargetClasses;\n    config.autoAriaDisabled = autoAriaDisabled;\n    if (config.type && newType !== config.type) {\n      unregisterDropZone(node, config.type);\n    }\n    config.type = newType;\n    registerDropZone(node, newType);\n    if (!autoAriaDisabled) {\n      node.setAttribute(\"aria-disabled\", dragDisabled);\n      node.setAttribute(\"role\", \"list\");\n      node.setAttribute(\"aria-describedby\", dragDisabled ? INSTRUCTION_IDs.DND_ZONE_DRAG_DISABLED : INSTRUCTION_IDs.DND_ZONE_ACTIVE);\n    }\n    dzToConfig.set(node, config);\n    if (isDragging) {\n      node.tabIndex = node === focusedDz || focusedItem.contains(node) || config.dropFromOthersDisabled || focusedDz && config.type !== dzToConfig.get(focusedDz).type ? -1 : 0;\n    } else {\n      node.tabIndex = config.zoneTabIndex;\n    }\n    node.addEventListener(\"focus\", handleZoneFocus);\n    var _loop = function _loop(i) {\n      var draggableEl = node.children[i];\n      allDragTargets.add(draggableEl);\n      draggableEl.tabIndex = isDragging ? -1 : config.zoneItemTabIndex;\n      if (!autoAriaDisabled) {\n        draggableEl.setAttribute(\"role\", \"listitem\");\n      }\n      draggableEl.removeEventListener(\"keydown\", elToKeyDownListeners.get(draggableEl));\n      draggableEl.removeEventListener(\"click\", elToFocusListeners.get(draggableEl));\n      if (!dragDisabled) {\n        draggableEl.addEventListener(\"keydown\", handleKeyDown);\n        elToKeyDownListeners.set(draggableEl, handleKeyDown);\n        draggableEl.addEventListener(\"click\", handleClick);\n        elToFocusListeners.set(draggableEl, handleClick);\n      }\n      if (isDragging && config.items[i][ITEM_ID_KEY] === focusedItemId) {\n        printDebug(function () {\n          return [\"focusing on\", {\n            i: i,\n            focusedItemId: focusedItemId\n          }];\n        });\n        // if it is a nested dropzone, it was re-rendered and we need to refresh our pointer\n        focusedItem = draggableEl;\n        focusedItem.tabIndex = config.zoneItemTabIndex;\n        // without this the element loses focus if it moves backwards in the list\n        draggableEl.focus();\n      }\n    };\n    for (var i = 0; i < node.children.length; i++) {\n      _loop(i);\n    }\n  }\n  configure(options);\n  var handles = {\n    update: function update(newOptions) {\n      printDebug(function () {\n        return \"keyboard dndzone will update newOptions: \".concat(toString(newOptions));\n      });\n      configure(newOptions);\n    },\n    destroy: function destroy() {\n      printDebug(function () {\n        return \"keyboard dndzone will destroy\";\n      });\n      unregisterDropZone(node, config.type);\n      dzToConfig[\"delete\"](node);\n      dzToHandles[\"delete\"](node);\n    }\n  };\n  dzToHandles.set(node, handles);\n  return handles;\n}\n\nvar _excluded = [\"items\", \"flipDurationMs\", \"type\", \"dragDisabled\", \"morphDisabled\", \"dropFromOthersDisabled\", \"zoneTabIndex\", \"zoneItemTabIndex\", \"dropTargetStyle\", \"dropTargetClasses\", \"transformDraggedElement\", \"autoAriaDisabled\", \"centreDraggedOnCursor\", \"dropAnimationDisabled\"];\n\n/**\n * A custom action to turn any container to a dnd zone and all of its direct children to draggables\n * Supports mouse, touch and keyboard interactions.\n * Dispatches two events that the container is expected to react to by modifying its list of items,\n * which will then feed back in to this action via the update function\n *\n * @typedef {object} Options\n * @property {array} items - the list of items that was used to generate the children of the given node (the list used in the #each block\n * @property {string} [type] - the type of the dnd zone. children dragged from here can only be dropped in other zones of the same type, default to a base type\n * @property {number} [flipDurationMs] - if the list animated using flip (recommended), specifies the flip duration such that everything syncs with it without conflict, defaults to zero\n * @property {boolean} [dragDisabled]\n * @property {boolean} [morphDisabled] - whether dragged element should morph to zone dimensions\n * @property {boolean} [dropFromOthersDisabled]\n * @property {number} [zoneTabIndex] - set the tabindex of the list container when not dragging\n * @property {number} [zoneItemTabIndex] - set the tabindex of the list container items when not dragging\n * @property {object} [dropTargetStyle]\n * @property {string[]} [dropTargetClasses]\n * @property {boolean} [dropAnimationDisabled] - cancels the drop animation to place\n * @property {function} [transformDraggedElement]\n * @param {HTMLElement} node - the element to enhance\n * @param {Options} options\n * @return {{update: function, destroy: function}}\n */\nfunction dndzone(node, options) {\n  if (shouldIgnoreZone(node)) {\n    return {\n      update: function update() {},\n      destroy: function destroy() {}\n    };\n  }\n  validateOptions(options);\n  var pointerZone = dndzone$2(node, options);\n  var keyboardZone = dndzone$1(node, options);\n  return {\n    update: function update(newOptions) {\n      validateOptions(newOptions);\n      pointerZone.update(newOptions);\n      keyboardZone.update(newOptions);\n    },\n    destroy: function destroy() {\n      pointerZone.destroy();\n      keyboardZone.destroy();\n    }\n  };\n}\n\n/**\n * If the user marked something in the ancestry of our node as shadow element, we can ignore it\n * We need the user to mark it for us because svelte updates the action from deep to shallow (but renders top down)\n * @param {HTMLElement} node\n * @return {boolean}\n */\nfunction shouldIgnoreZone(node) {\n  return !!node.closest(\"[\".concat(SHADOW_ELEMENT_HINT_ATTRIBUTE_NAME, \"=\\\"true\\\"]\"));\n}\nfunction validateOptions(options) {\n  /*eslint-disable*/\n  var items = options.items;\n    options.flipDurationMs;\n    options.type;\n    options.dragDisabled;\n    options.morphDisabled;\n    options.dropFromOthersDisabled;\n    var zoneTabIndex = options.zoneTabIndex,\n    zoneItemTabIndex = options.zoneItemTabIndex;\n    options.dropTargetStyle;\n    var dropTargetClasses = options.dropTargetClasses;\n    options.transformDraggedElement;\n    options.autoAriaDisabled;\n    options.centreDraggedOnCursor;\n    options.dropAnimationDisabled;\n    var rest = _objectWithoutProperties(options, _excluded);\n  /*eslint-enable*/\n  if (Object.keys(rest).length > 0) {\n    console.warn(\"dndzone will ignore unknown options\", rest);\n  }\n  if (!items) {\n    throw new Error(\"no 'items' key provided to dndzone\");\n  }\n  var itemWithMissingId = items.find(function (item) {\n    return !{}.hasOwnProperty.call(item, ITEM_ID_KEY);\n  });\n  if (itemWithMissingId) {\n    throw new Error(\"missing '\".concat(ITEM_ID_KEY, \"' property for item \").concat(toString(itemWithMissingId)));\n  }\n  if (dropTargetClasses && !Array.isArray(dropTargetClasses)) {\n    throw new Error(\"dropTargetClasses should be an array but instead it is a \".concat(_typeof(dropTargetClasses), \", \").concat(toString(dropTargetClasses)));\n  }\n  if (zoneTabIndex && !isInt(zoneTabIndex)) {\n    throw new Error(\"zoneTabIndex should be a number but instead it is a \".concat(_typeof(zoneTabIndex), \", \").concat(toString(zoneTabIndex)));\n  }\n  if (zoneItemTabIndex && !isInt(zoneItemTabIndex)) {\n    throw new Error(\"zoneItemTabIndex should be a number but instead it is a \".concat(_typeof(zoneItemTabIndex), \", \").concat(toString(zoneItemTabIndex)));\n  }\n}\nfunction isInt(value) {\n  return !isNaN(value) && function (x) {\n    return (x | 0) === x;\n  }(parseFloat(value));\n}\n\nfunction createStore(initialValue) {\n  var _val = initialValue;\n  var subs = new Set();\n  return {\n    get: function get() {\n      return _val;\n    },\n    set: function set(newVal) {\n      _val = newVal;\n      Array.from(subs).forEach(function (cb) {\n        return cb(_val);\n      });\n    },\n    subscribe: function subscribe(cb) {\n      subs.add(cb);\n      cb(_val);\n    },\n    unsubscribe: function unsubscribe(cb) {\n      subs[\"delete\"](cb);\n    }\n  };\n}\n\nvar isItemsDragDisabled = createStore(true);\nfunction getAddedOptions() {\n  var isItemsDragDisabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  return {\n    dragDisabled: isItemsDragDisabled,\n    zoneItemTabIndex: -1\n  };\n}\n\n/**\n * This is an action that wraps around the dndzone action to make it easy to work with drag handles\n * When using this you must also use the 'dragHandle' action (see below) on an element inside each item within the zone\n * Credit for the idea and initial implementation goes to @gleuch (Greg Leuch) and @geovie (Georg Vienna)\n *\n * @param {HTMLElement} node\n * @param options - will be passed down to the dndzone\n * @return {{update: (newOptions: Object) => {}, destroy: () => {}}}\n */\nfunction dragHandleZone(node, options) {\n  var currentOptions = options;\n  var zone = dndzone(node, _objectSpread2(_objectSpread2({}, currentOptions), getAddedOptions()));\n  function isItemDisabledCB(isItemsDragDisabled) {\n    zone.update(_objectSpread2(_objectSpread2({}, currentOptions), getAddedOptions(isItemsDragDisabled)));\n  }\n  isItemsDragDisabled.subscribe(isItemDisabledCB);\n  function consider(e) {\n    var _e$detail$info = e.detail.info,\n      source = _e$detail$info.source,\n      trigger = _e$detail$info.trigger;\n    // Ensure dragging is stopped on drag finish via keyboard\n    if (source === SOURCES.KEYBOARD && trigger === TRIGGERS.DRAG_STOPPED) {\n      isItemsDragDisabled.set(true);\n    }\n  }\n  function finalize(e) {\n    var source = e.detail.info.source;\n    // Ensure dragging is stopped on drag finish via pointer (mouse, touch)\n    if (source === SOURCES.POINTER) {\n      isItemsDragDisabled.set(true);\n    }\n  }\n  node.addEventListener(\"consider\", consider);\n  node.addEventListener(\"finalize\", finalize);\n  return {\n    update: function update(newOptions) {\n      currentOptions = newOptions;\n      zone.update(_objectSpread2(_objectSpread2({}, currentOptions), getAddedOptions(isItemsDragDisabled.get())));\n    },\n    destroy: function destroy() {\n      node.removeEventListener(\"consider\", consider);\n      node.removeEventListener(\"finalize\", finalize);\n      isItemsDragDisabled.unsubscribe(isItemDisabledCB);\n    }\n  };\n}\n\n/**\n * This should be used to mark drag handles inside items that belong to a 'dragHandleZone' (see above)\n * @param {HTMLElement} handle\n * @return {{update: *, destroy: *}}\n */\nfunction dragHandle(handle) {\n  handle.setAttribute(\"role\", \"button\");\n  function startDrag(e) {\n    // preventing default to prevent lag on touch devices (because of the browser checking for screen scrolling)\n    e.preventDefault();\n    isItemsDragDisabled.set(false);\n  }\n  function handleKeyDown(e) {\n    if (e.key === \"Enter\" || e.key === \" \") isItemsDragDisabled.set(false);\n  }\n  isItemsDragDisabled.subscribe(function (disabled) {\n    handle.tabIndex = disabled ? 0 : -1;\n    handle.style.cursor = disabled ? \"grab\" : \"grabbing\";\n  });\n  handle.addEventListener(\"mousedown\", startDrag);\n  handle.addEventListener(\"touchstart\", startDrag);\n  handle.addEventListener(\"keydown\", handleKeyDown);\n  return {\n    update: function update() {},\n    destroy: function destroy() {\n      handle.removeEventListener(\"mousedown\", startDrag);\n      handle.removeEventListener(\"touchstart\", startDrag);\n      handle.removeEventListener(\"keydown\", handleKeyDown);\n    }\n  };\n}\n\nexport { DRAGGED_ELEMENT_ID, FEATURE_FLAG_NAMES, SHADOW_ITEM_MARKER_PROPERTY_NAME, SHADOW_PLACEHOLDER_ITEM_ID, SOURCES, TRIGGERS, alertToScreenReader, dndzone, dragHandle, dragHandleZone, overrideItemIdKeyNameBeforeInitialisingDndZones, setDebugMode, setFeatureFlag };\n"], "mappings": ";;;AAAA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,QAAQ,KAAK;AACpB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,MAAK;AAClG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,MAAK;AACjB,WAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,EAC1H,GAAG,QAAQ,GAAG;AAChB;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AACT,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAC3D,MAAI,KAAK;AACT,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAC1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AACA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAC1H;AACA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AACvG,MAAI,MAAM,KAAM;AAChB,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI;AACF,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,WAAK,KAAK,GAAG,KAAK;AAClB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AACA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAC/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AACpE,SAAO;AACT;AACA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AACA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AACA,SAAS,2BAA2B,GAAG,gBAAgB;AACrD,MAAI,KAAK,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAC9E,MAAI,CAAC,IAAI;AACP,QAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,4BAA4B,CAAC,MAAM,kBAAkB,KAAK,OAAO,EAAE,WAAW,UAAU;AACpH,UAAI,GAAI,KAAI;AACZ,UAAI,IAAI;AACR,UAAI,IAAI,WAAY;AAAA,MAAC;AACrB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG,WAAY;AACb,cAAI,KAAK,EAAE,OAAQ,QAAO;AAAA,YACxB,MAAM;AAAA,UACR;AACA,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,EAAE,GAAG;AAAA,UACd;AAAA,QACF;AAAA,QACA,GAAG,SAAU,GAAG;AACd,gBAAM;AAAA,QACR;AAAA,QACA,GAAG;AAAA,MACL;AAAA,IACF;AACA,UAAM,IAAI,UAAU,uIAAuI;AAAA,EAC7J;AACA,MAAI,mBAAmB,MACrB,SAAS,OACT;AACF,SAAO;AAAA,IACL,GAAG,WAAY;AACb,WAAK,GAAG,KAAK,CAAC;AAAA,IAChB;AAAA,IACA,GAAG,WAAY;AACb,UAAI,OAAO,GAAG,KAAK;AACnB,yBAAmB,KAAK;AACxB,aAAO;AAAA,IACT;AAAA,IACA,GAAG,SAAU,GAAG;AACd,eAAS;AACT,YAAM;AAAA,IACR;AAAA,IACA,GAAG,WAAY;AACb,UAAI;AACF,YAAI,CAAC,oBAAoB,GAAG,UAAU,KAAM,IAAG,OAAO;AAAA,MACxD,UAAE;AACA,YAAI,OAAQ,OAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAW1B,SAAS,sBAAsB,IAAI,OAAO,MAAM;AAC9C,KAAG,cAAc,IAAI,YAAY,qBAAqB;AAAA,IACpD,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AAQA,SAAS,sBAAsB,IAAI,OAAO,MAAM;AAC9C,KAAG,cAAc,IAAI,YAAY,qBAAqB;AAAA,IACpD,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AAGA,IAAI,6BAA6B;AACjC,IAAI,0BAA0B;AAC9B,IAAI,gCAAgC;AACpC,IAAI,mCAAmC;AACvC,IAAI,qBAAqB;AAAA,EACvB,kBAAkB;AAAA,EAClB,gBAAgB;AAClB;AACA,SAAS,uCAAuC,aAAa,UAAUC,YAAW;AAChF,cAAY,cAAc,IAAI,YAAY,4BAA4B;AAAA,IACpE,QAAQ;AAAA,MACN;AAAA,MACA,WAAWA;AAAA,IACb;AAAA,EACF,CAAC,CAAC;AACJ;AAOA,SAAS,8CAA8C,aAAaA,YAAW,YAAY;AACzF,cAAY,cAAc,IAAI,YAAY,yBAAyB;AAAA,IACjE,QAAQ;AAAA,MACN,WAAWA;AAAA,MACX,MAAM,mBAAmB;AAAA,MACzB;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AACA,SAAS,2CAA2C,aAAaA,YAAW;AAC1E,cAAY,cAAc,IAAI,YAAY,yBAAyB;AAAA,IACjE,QAAQ;AAAA,MACN,WAAWA;AAAA,MACX,MAAM,mBAAmB;AAAA,IAC3B;AAAA,EACF,CAAC,CAAC;AACJ;AACA,SAAS,kCAAkC,aAAa,UAAUA,YAAW;AAC3E,cAAY,cAAc,IAAI,YAAY,+BAA+B;AAAA,IACvE,QAAQ;AAAA,MACN;AAAA,MACA,WAAWA;AAAA,IACb;AAAA,EACF,CAAC,CAAC;AACJ;AACA,SAAS,4BAA4BA,YAAW;AAC9C,SAAO,cAAc,IAAI,YAAY,kCAAkC;AAAA,IACrE,QAAQ;AAAA,MACN,WAAWA;AAAA,IACb;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,cAAc;AAChB;AACA,IAAI,UAAU;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AACZ;AACA,IAAI,mCAAmC;AACvC,IAAI,gCAAgC;AACpC,IAAI,qCAAqC;AACzC,IAAI,6BAA6B;AACjC,IAAI,qBAAqB;AACzB,IAAI,cAAc;AAClB,IAAI,qBAAqB;AACzB,SAAS,+BAA+B;AACtC;AACF;AACA,SAAS,+BAA+B;AACtC,MAAI,uBAAuB,GAAG;AAC5B,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACxE;AACA;AACF;AAQA,SAAS,gDAAgD,YAAY;AACnE,MAAI,qBAAqB,GAAG;AAC1B,UAAM,IAAI,MAAM,8DAA8D;AAAA,EAChF;AACA,MAAI,OAAO,eAAe,UAAU;AAClC,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACA,aAAW,WAAY;AACrB,WAAO,CAAC,+BAA+B,UAAU;AAAA,EACnD,CAAC;AACD,gBAAc;AAChB;AACA,IAAI,aAAa,OAAO,WAAW;AACnC,IAAI,aAAa,SAASC,cAAa;AAAC;AAMxC,SAAS,aAAa,SAAS;AAC7B,MAAI,SAAS;AACX,iBAAa,SAASA,YAAW,iBAAiB;AAChD,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,QAAQ;AAC9F,UAAI,UAAU,gBAAgB;AAC9B,UAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,oBAAY,MAAM,QAAQ,mBAAmB,OAAO,CAAC;AAAA,MACvD,OAAO;AACL,oBAAY,OAAO;AAAA,MACrB;AAAA,IACF;AAAA,EACF,OAAO;AACL,iBAAa,SAASA,cAAa;AAAA,IAAC;AAAA,EACtC;AACF;AAUA,SAAS,4BAA4B,IAAI;AACvC,MAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACtF,MAAI;AACJ,MAAI,OAAO,cAAc,wBAAwB,EAAE,IAAI,GAAG,sBAAsB;AAChF,MAAI,QAAQ,iBAAiB,EAAE;AAC/B,MAAI,KAAK,MAAM;AACf,MAAI,IAAI;AACN,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,GAAG,WAAW,WAAW,GAAG;AAC9B,WAAK,GAAG,MAAM,GAAG,EAAE,EAAE,MAAM,IAAI;AAC/B,WAAK,CAAC,GAAG,CAAC;AACV,WAAK,CAAC,GAAG,CAAC;AACV,WAAK,CAAC,GAAG,EAAE;AACX,WAAK,CAAC,GAAG,EAAE;AAAA,IACb,WAAW,GAAG,WAAW,SAAS,GAAG;AACnC,WAAK,GAAG,MAAM,GAAG,EAAE,EAAE,MAAM,IAAI;AAC/B,WAAK,CAAC,GAAG,CAAC;AACV,WAAK,CAAC,GAAG,CAAC;AACV,WAAK,CAAC,GAAG,CAAC;AACV,WAAK,CAAC,GAAG,CAAC;AAAA,IACZ,OAAO;AACL,aAAO;AAAA,IACT;AACA,QAAI,KAAK,MAAM;AACf,QAAI,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,WAAW,EAAE;AAC9C,QAAI,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;AACzE,QAAI,IAAI,KAAK,KAAK,QAAQ,KAAK,GAAG;AAClC,QAAI,IAAI,KAAK,KAAK,SAAS,KAAK,GAAG;AACnC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,OAAO,IAAI;AAAA,MACX,QAAQ,IAAI;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAOA,SAAS,4BAA4B,IAAI;AACvC,MAAI,OAAO,4BAA4B,EAAE;AACzC,SAAO;AAAA,IACL,KAAK,KAAK,MAAM,OAAO;AAAA,IACvB,QAAQ,KAAK,SAAS,OAAO;AAAA,IAC7B,MAAM,KAAK,OAAO,OAAO;AAAA,IACzB,OAAO,KAAK,QAAQ,OAAO;AAAA,EAC7B;AACF;AAOA,SAAS,gBAAgB,IAAI;AAC3B,MAAI,OAAO,GAAG,sBAAsB;AACpC,SAAO;AAAA,IACL,KAAK,KAAK,MAAM,OAAO;AAAA,IACvB,QAAQ,KAAK,SAAS,OAAO;AAAA,IAC7B,MAAM,KAAK,OAAO,OAAO;AAAA,IACzB,OAAO,KAAK,QAAQ,OAAO;AAAA,EAC7B;AACF;AAYA,SAAS,WAAW,MAAM;AACxB,SAAO;AAAA,IACL,IAAI,KAAK,OAAO,KAAK,SAAS;AAAA,IAC9B,IAAI,KAAK,MAAM,KAAK,UAAU;AAAA,EAChC;AACF;AAUA,SAAS,aAAa,QAAQ,QAAQ;AACpC,SAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,CAAC;AACtF;AAOA,SAAS,kBAAkB,OAAO,MAAM;AACtC,SAAO,MAAM,KAAK,KAAK,UAAU,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK;AAClG;AAOA,SAAS,oBAAoB,IAAI;AAC/B,SAAO,WAAW,gBAAgB,EAAE,CAAC;AACvC;AAOA,SAAS,mBAAmB,KAAK,KAAK;AACpC,MAAI,YAAY,oBAAoB,GAAG;AACvC,MAAI,UAAU,4BAA4B,GAAG;AAC7C,SAAO,kBAAkB,WAAW,OAAO;AAC7C;AAOA,SAAS,2BAA2B,KAAK,KAAK;AAC5C,MAAI,YAAY,oBAAoB,GAAG;AACvC,MAAI,YAAY,oBAAoB,GAAG;AACvC,SAAO,aAAa,WAAW,SAAS;AAC1C;AAMA,SAAS,qBAAqB,IAAI;AAChC,MAAI,OAAO,gBAAgB,EAAE;AAC7B,SAAO,KAAK,QAAQ,KAAK,KAAK,OAAO,SAAS,gBAAgB,eAAe,KAAK,SAAS,KAAK,KAAK,MAAM,SAAS,gBAAgB;AACtI;AACA,SAAS,wBAAwB,SAAS;AACxC,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,cAAc;AAAA,IAChB,KAAK,KAAK;AAAA,IACV,QAAQ,KAAK;AAAA,IACb,MAAM,KAAK;AAAA,IACX,OAAO,KAAK;AAAA,EACd;AAGA,MAAI,SAAS,QAAQ;AACrB,SAAO,UAAU,WAAW,SAAS,MAAM;AACzC,QAAI,aAAa,OAAO,sBAAsB;AAG9C,QAAI,YAAY,OAAO,iBAAiB,MAAM,EAAE;AAChD,QAAI,YAAY,OAAO,iBAAiB,MAAM,EAAE;AAChD,QAAI,gBAAgB,cAAc,YAAY,cAAc;AAC5D,QAAI,gBAAgB,cAAc,YAAY,cAAc;AAG5D,QAAI,eAAe;AACjB,kBAAY,MAAM,KAAK,IAAI,YAAY,KAAK,WAAW,GAAG;AAC1D,kBAAY,SAAS,KAAK,IAAI,YAAY,QAAQ,WAAW,MAAM;AAAA,IACrE;AACA,QAAI,eAAe;AACjB,kBAAY,OAAO,KAAK,IAAI,YAAY,MAAM,WAAW,IAAI;AAC7D,kBAAY,QAAQ,KAAK,IAAI,YAAY,OAAO,WAAW,KAAK;AAAA,IAClE;AACA,aAAS,OAAO;AAAA,EAClB;AAGA,cAAY,MAAM,KAAK,IAAI,YAAY,KAAK,CAAC;AAC7C,cAAY,SAAS,KAAK,IAAI,YAAY,QAAQ,OAAO,WAAW;AACpE,cAAY,OAAO,KAAK,IAAI,YAAY,MAAM,CAAC;AAC/C,cAAY,QAAQ,KAAK,IAAI,YAAY,OAAO,OAAO,UAAU;AAGjE,SAAO;AAAA,IACL,KAAK,YAAY;AAAA,IACjB,QAAQ,YAAY;AAAA,IACpB,MAAM,YAAY;AAAA,IAClB,OAAO,YAAY;AAAA,IACnB,OAAO,KAAK,IAAI,GAAG,YAAY,QAAQ,YAAY,IAAI;AAAA,IACvD,QAAQ,KAAK,IAAI,GAAG,YAAY,SAAS,YAAY,GAAG;AAAA,EAC1D;AACF;AAEA,IAAI;AAKJ,SAAS,oBAAoB;AAC3B,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,0BAAwB,oBAAI,IAAI;AAClC;AACA,kBAAkB;AAQlB,SAAS,gBAAgB,IAAI;AAC3B,MAAI,gBAAgB,MAAM,KAAK,GAAG,QAAQ,EAAE,UAAU,SAAU,OAAO;AACrE,WAAO,MAAM,aAAa,6BAA6B;AAAA,EACzD,CAAC;AACD,MAAI,iBAAiB,GAAG;AACtB,QAAI,CAAC,sBAAsB,IAAI,EAAE,GAAG;AAClC,4BAAsB,IAAI,IAAI,oBAAI,IAAI,CAAC;AAAA,IACzC;AACA,0BAAsB,IAAI,EAAE,EAAE,IAAI,eAAe,4BAA4B,GAAG,SAAS,aAAa,CAAC,CAAC;AACxG,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAaA,SAAS,iBAAiB,iBAAiB,mBAAmB;AAC5D,MAAI,CAAC,mBAAmB,iBAAiB,iBAAiB,GAAG;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,WAAW,kBAAkB;AAEjC,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,kBAAkB;AAAA,IACpB;AAAA,EACF;AACA,MAAI,gBAAgB,gBAAgB,iBAAiB;AAIrD,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,mBAAmB,iBAAiB,SAAS,CAAC,CAAC,GAAG;AACpD,UAAI,mBAAmB,sBAAsB,IAAI,iBAAiB,KAAK,sBAAsB,IAAI,iBAAiB,EAAE,IAAI,CAAC;AACzH,UAAI,kBAAkB;AACpB,YAAI,CAAC,kBAAkB,oBAAoB,eAAe,GAAG,gBAAgB,GAAG;AAC9E,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,kBAAkB;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO;AAAA,QACP,kBAAkB;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,mBAAmB,OAAO;AAC9B,MAAI,aAAa;AAEjB,WAAS,KAAK,GAAG,KAAK,SAAS,QAAQ,MAAM;AAC3C,QAAI,WAAW,2BAA2B,iBAAiB,SAAS,EAAE,CAAC;AACvE,QAAI,WAAW,kBAAkB;AAC/B,yBAAmB;AACnB,mBAAa;AAAA,IACf;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,kBAAkB;AAAA,EACpB;AACF;AAMA,SAAS,SAAS,QAAQ;AACxB,SAAO,KAAK,UAAU,QAAQ,MAAM,CAAC;AACvC;AAOA,SAAS,SAAS,MAAM;AACtB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACpD;AACA,SAAO,UAAU,MAAM,CAAC;AAC1B;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACrF,MAAI,CAAC,KAAK,eAAe;AACvB,WAAO,aAAa;AAAA,EACtB;AACA,SAAO,UAAU,KAAK,eAAe,aAAa,CAAC;AACrD;AAQA,SAAS,uBAAuB,MAAM,MAAM;AAC1C,MAAI,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACzD,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,MAAM;AACrB,QAAI,CAAC,CAAC,EAAE,eAAe,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,GAAG;AACpE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,+BAA+B,MAAM,MAAM;AAClD,MAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI;AASJ,SAAS,QAAQD,YAAW,WAAW;AACrC,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACrF,MAAIE,iBAAgB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAE1D,MAAI;AACJ,MAAI;AACJ,MAAI,2BAA2B;AAC/B,MAAI;AAEJ,MAAI,6BAA6B,MAAM,KAAK,SAAS,EAAE,KAAK,SAAU,KAAK,KAAK;AAC9E,WAAO,SAAS,GAAG,IAAI,SAAS,GAAG;AAAA,EACrC,CAAC;AAKD,WAAS,SAAS;AAChB,QAAI,yBAAyB,oBAAoBF,UAAS;AAC1D,QAAI,WAAWE,eAAc,oBAAoB;AAEjD,QAAI,CAAC,YAAY,+BAA+B,KAAK,IAAI,4BAA4B,IAAI,uBAAuB,CAAC,IAAI,gBAAgB,KAAK,IAAI,4BAA4B,IAAI,uBAAuB,CAAC,IAAI,cAAc;AACtN,aAAO,OAAO,WAAW,QAAQ,UAAU;AAC3C;AAAA,IACF;AACA,QAAI,qBAAqBF,UAAS,GAAG;AACnC,iBAAW,WAAY;AACrB,eAAO;AAAA,MACT,CAAC;AACD,kCAA4BA,UAAS;AACrC;AAAA,IACF;AACA,kCAA8B;AAE9B,QAAI,uBAAuB;AAC3B,QAAI,YAAY,2BAA2B,0BAA0B,GACnE;AACF,QAAI;AACF,WAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,YAAI,KAAK,MAAM;AACf,YAAI,SAAU,mBAAkB;AAChC,YAAI,WAAW,iBAAiBA,YAAW,EAAE;AAC7C,YAAI,aAAa,MAAM;AAErB;AAAA,QACF;AACA,YAAI,QAAQ,SAAS;AACrB,+BAAuB;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,+BAAqB,8CAA8C,mBAAmBA,YAAW,EAAE;AACnG,iDAAuC,IAAI,UAAUA,UAAS;AAC9D,8BAAoB;AAAA,QACtB,WAAW,UAAU,gBAAgB;AACnC,4CAAkC,IAAI,UAAUA,UAAS;AACzD,2BAAiB;AAAA,QACnB;AAEA;AAAA,MACF;AAAA,IAEF,SAAS,KAAK;AACZ,gBAAU,EAAE,GAAG;AAAA,IACjB,UAAE;AACA,gBAAU,EAAE;AAAA,IACd;AACA,QAAI,CAAC,wBAAwB,4BAA4B,mBAAmB;AAC1E,iDAA2C,mBAAmBA,UAAS;AACvE,0BAAoB;AACpB,uBAAiB;AACjB,iCAA2B;AAAA,IAC7B,OAAO;AACL,iCAA2B;AAAA,IAC7B;AACA,WAAO,OAAO,WAAW,QAAQ,UAAU;AAAA,EAC7C;AACA,SAAO;AACT;AAGA,SAAS,YAAY;AACnB,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,eAAa,IAAI;AACjB,oBAAkB;AACpB;AAEA,IAAI,iBAAiB;AAMrB,SAAS,eAAe;AACtB,MAAI;AACJ,WAAS,iBAAiB;AACxB,oBAAgB;AAAA,MACd,cAAc;AAAA,MACd,QAAQ;AAAA,IACV;AAAA,EACF;AACA,iBAAe;AAEf,WAAS,gBAAgB,aAAa;AACpC,QAAI,iBAAiB,eACnB,eAAe,eAAe,cAC9B,SAAS,eAAe;AAC1B,QAAI,cAAc;AAChB,kBAAY,SAAS,aAAa,IAAI,QAAQ,aAAa,IAAI,MAAM;AACrE,aAAO,sBAAsB,WAAY;AACvC,eAAO,gBAAgB,WAAW;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,EACF;AACA,WAAS,iBAAiB,YAAY;AACpC,WAAO,iBAAiB;AAAA,EAC1B;AASA,WAAS,eAAe,SAAS,iBAAiB;AAChD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,QAAI,YAAY,gDAAgD,SAAS,eAAe;AACxF,QAAI,qBAAqB,CAAC,CAAC,cAAc;AACzC,QAAI,cAAc,MAAM;AACtB,UAAI,mBAAoB,gBAAe;AACvC,aAAO;AAAA,IACT;AACA,QAAI,sBAAsB,OACxB,wBAAwB;AAE1B,QAAI,gBAAgB,eAAe,gBAAgB,cAAc;AAC/D,UAAI,UAAU,SAAS,gBAAgB;AACrC,8BAAsB;AACtB,sBAAc,eAAe;AAAA,UAC3B,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,sBAAc,SAAS,iBAAiB,UAAU,MAAM;AAAA,MAC1D,WAAW,UAAU,MAAM,gBAAgB;AACzC,8BAAsB;AACtB,sBAAc,eAAe;AAAA,UAC3B,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,sBAAc,SAAS,iBAAiB,UAAU,GAAG;AAAA,MACvD;AACA,UAAI,CAAC,sBAAsB,qBAAqB;AAC9C,wBAAgB,eAAe;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,gBAAgB,cAAc,gBAAgB,aAAa;AAC7D,UAAI,UAAU,QAAQ,gBAAgB;AACpC,gCAAwB;AACxB,sBAAc,eAAe;AAAA,UAC3B,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,sBAAc,SAAS,iBAAiB,UAAU,KAAK;AAAA,MACzD,WAAW,UAAU,OAAO,gBAAgB;AAC1C,gCAAwB;AACxB,sBAAc,eAAe;AAAA,UAC3B,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,sBAAc,SAAS,iBAAiB,UAAU,IAAI;AAAA,MACxD;AACA,UAAI,CAAC,sBAAsB,uBAAuB;AAChD,wBAAgB,eAAe;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AACA,mBAAe;AACf,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAQA,SAAS,gDAAgD,OAAO,IAAI;AAElE,MAAI,OAAO,OAAO,SAAS,mBAAmB;AAAA,IAC5C,KAAK;AAAA,IACL,QAAQ,OAAO;AAAA,IACf,MAAM;AAAA,IACN,OAAO,OAAO;AAAA,EAChB,IAAI,GAAG,sBAAsB;AAC7B,MAAI,CAAC,kBAAkB,OAAO,IAAI,GAAG;AACnC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,KAAK,MAAM,IAAI,KAAK;AAAA,IACpB,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,MAAM,MAAM,IAAI,KAAK;AAAA,IACrB,OAAO,KAAK,QAAQ,MAAM;AAAA,EAC5B;AACF;AAWA,SAAS,sBAAsB;AAC7B,MAAI,2BAA2B,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACpG,MAAI,qBAAqB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAC/D,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,yBAAyB,6BAA6B,wBAAwB;AAClF,MAAI,mCAAmC,MAAM,KAAK,sBAAsB,EAAE,KAAK,SAAU,KAAK,KAAK;AACjG,WAAO,SAAS,GAAG,IAAI,SAAS,GAAG;AAAA,EACrC,CAAC;AACD,MAAI,gBAAgB,aAAa,GAC/B,iBAAiB,cAAc,gBAC/B,iBAAiB,cAAc;AAKjC,WAAS,OAAO;AACd,QAAI,gBAAgB,mBAAmB;AACvC,QAAI,CAAC,iBAAiB,CAAC,kCAAkC;AACvD,aAAO;AAAA,IACT;AACA,QAAI,8BAA8B,iCAAiC,OAAO,SAAU,IAAI;AACtF,aAAO,kBAAkB,eAAe,GAAG,sBAAsB,CAAC,KAAK,OAAO,SAAS;AAAA,IACzF,CAAC;AACD,aAAS,IAAI,GAAG,IAAI,4BAA4B,QAAQ,KAAK;AAC3D,UAAI,WAAW,eAAe,eAAe,4BAA4B,CAAC,CAAC;AAC3E,UAAI,UAAU;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,qBAAqB,uBAAuB,OAAO,IAAI,OAAO,WAAY;AACxE,aAAO;AAAA,IACT;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AACF;AAGA,SAAS,sBAAsB,SAAS;AACtC,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AACA,MAAI,uBAAuB,CAAC;AAC5B,MAAI,SAAS;AACb,SAAO,QAAQ;AACb,QAAI,wBAAwB,OAAO,iBAAiB,MAAM,GACxD,WAAW,sBAAsB;AACnC,QAAI,SAAS,MAAM,GAAG,EAAE,KAAK,SAAU,GAAG;AACxC,aAAO,EAAE,SAAS,MAAM,KAAK,EAAE,SAAS,QAAQ;AAAA,IAClD,CAAC,GAAG;AACF,2BAAqB,KAAK,MAAM;AAAA,IAClC;AACA,aAAS,OAAO;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,6BAA6B,WAAW;AAC/C,MAAI,sBAAsB,oBAAI,IAAI;AAClC,MAAI,YAAY,2BAA2B,SAAS,GAClD;AACF,MAAI;AACF,SAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,UAAI,KAAK,MAAM;AACf,4BAAsB,EAAE,EAAE,QAAQ,SAAU,WAAW;AACrD,eAAO,oBAAoB,IAAI,SAAS;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EAEF,SAAS,KAAK;AACZ,cAAU,EAAE,GAAG;AAAA,EACjB,UAAE;AACA,cAAU,EAAE;AAAA,EACd;AACA,MAAI,SAAS,iBAAiB,eAAe,SAAS,iBAAiB,gBAAgB,SAAS,iBAAiB,cAAc,SAAS,iBAAiB,cAAc;AACrK,wBAAoB,IAAI,SAAS,gBAAgB;AAAA,EACnD;AACA,SAAO;AACT;AAWA,SAAS,gBAAgB,IAAI;AAC3B,MAAI,SAAS,GAAG,UAAU,IAAI;AAC9B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,GAAG,YAAY;AAChC,MAAI,UAAU,aAAa,CAAC,EAAE,IAAI,mBAAmB,GAAG,iBAAiB,QAAQ,CAAC;AAClF,MAAI,YAAY,2BAA2B,OAAO,GAChD;AACF,MAAI;AACF,SAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,UAAI,UAAU,MAAM;AACpB,aAAO,KAAK,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACF,SAAS,KAAK;AACZ,cAAU,EAAE,GAAG;AAAA,EACjB,UAAE;AACA,cAAU,EAAE;AAAA,EACd;AACA,MAAI,QAAQ,SAAS,GAAG;AACtB,QAAI,gBAAgB,aAAa,CAAC,MAAM,IAAI,mBAAmB,OAAO,iBAAiB,QAAQ,CAAC;AAChG,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,UAAI,SAAS,cAAc,CAAC;AAC5B,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,WAAW,OAAO,cAAc,iBAAkB,OAAO,OAAO,GAAI,CAAC;AACzE,UAAI,UAAU;AACZ,iBAAS,aAAa,YAAY,IAAI;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,GAAG,YAAY;AAChC,MAAI,WAAW,aAAa,CAAC,EAAE,IAAI,mBAAmB,GAAG,iBAAiB,QAAQ,CAAC;AACnF,MAAI,SAAS,SAAS,GAAG;AACvB,QAAI,iBAAiB,aAAa,CAAC,MAAM,IAAI,mBAAmB,OAAO,iBAAiB,QAAQ,CAAC;AACjG,aAAS,KAAK,GAAG,KAAK,eAAe,QAAQ,MAAM;AACjD,UAAI,SAAS,SAAS,EAAE;AACxB,UAAI,eAAe,eAAe,EAAE;AACpC,mBAAa,QAAQ,OAAO;AAC5B,mBAAa,SAAS,OAAO;AAC7B,UAAI,OAAO,QAAQ,KAAK,OAAO,SAAS,GAAG;AACzC,qBAAa,WAAW,IAAI,EAAE,UAAU,QAAQ,GAAG,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAKA,IAAI,qBAAqB,OAAO,OAAO;AAAA;AAAA,EAErC,6CAA6C;AAC/C,CAAC;AACD,IAAI,kBAAkB,gBAAgB,CAAC,GAAG,mBAAmB,6CAA6C,KAAK;AAM/G,SAAS,eAAe,UAAU,WAAW;AAC3C,MAAI,CAAC,mBAAmB,QAAQ,EAAG,OAAM,IAAI,MAAM,uCAAuC,OAAO,UAAU,qBAAqB,EAAE,OAAO,OAAO,KAAK,kBAAkB,CAAC,CAAC;AACzK,kBAAgB,QAAQ,IAAI,CAAC,CAAC;AAChC;AAOA,SAAS,eAAe,UAAU;AAChC,MAAI,CAAC,mBAAmB,QAAQ,EAAG,OAAM,IAAI,MAAM,uCAAuC,OAAO,UAAU,qBAAqB,EAAE,OAAO,OAAO,KAAK,kBAAkB,CAAC,CAAC;AACzK,SAAO,gBAAgB,QAAQ;AACjC;AAEA,IAAI,8BAA8B;AAOlC,SAAS,IAAI,UAAU;AACrB,SAAO,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,6BAA6B,QAAQ;AAC9E;AAOA,SAAS,yBAAyB,iBAAiB,oBAAoB;AACrE,MAAI,OAAO,gBAAgB,sBAAsB;AACjD,MAAIA,aAAY,gBAAgB,eAAe;AAC/C,mBAAiB,iBAAiBA,UAAS;AAC3C,EAAAA,WAAU,KAAK;AACf,EAAAA,WAAU,MAAM,WAAW;AAC3B,MAAI,UAAU,KAAK;AACnB,MAAI,WAAW,KAAK;AACpB,EAAAA,WAAU,MAAM,MAAM,GAAG,OAAO,SAAS,IAAI;AAC7C,EAAAA,WAAU,MAAM,OAAO,GAAG,OAAO,UAAU,IAAI;AAC/C,MAAI,oBAAoB;AACtB,QAAI,SAAS,WAAW,IAAI;AAC5B,eAAW,OAAO,IAAI,mBAAmB;AACzC,gBAAY,OAAO,IAAI,mBAAmB;AAC1C,WAAO,WAAW,WAAY;AAC5B,MAAAA,WAAU,MAAM,MAAM,GAAG,OAAO,SAAS,IAAI;AAC7C,MAAAA,WAAU,MAAM,OAAO,GAAG,OAAO,UAAU,IAAI;AAAA,IACjD,GAAG,CAAC;AAAA,EACN;AACA,EAAAA,WAAU,MAAM,SAAS;AAEzB,EAAAA,WAAU,MAAM,YAAY;AAC5B,EAAAA,WAAU,MAAM,SAAS,GAAG,OAAO,KAAK,QAAQ,IAAI;AACpD,EAAAA,WAAU,MAAM,QAAQ,GAAG,OAAO,KAAK,OAAO,IAAI;AAClD,EAAAA,WAAU,MAAM,aAAa,GAAG,OAAO,IAAI,KAAK,GAAG,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,EAAE,OAAO,IAAI,kBAAkB,GAAG,IAAI,EAAE,OAAO,IAAI,SAAS,GAAG,IAAI,EAAE,OAAO,IAAI,OAAO,GAAG,GAAG;AAE9K,SAAO,WAAW,WAAY;AAC5B,WAAOA,WAAU,MAAM,cAAc,KAAK,OAAO,IAAI,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC;AAAA,EAC3F,GAAG,CAAC;AACJ,EAAAA,WAAU,MAAM,SAAS;AACzB,EAAAA,WAAU,MAAM,SAAS;AACzB,SAAOA;AACT;AAMA,SAAS,oCAAoCA,YAAW;AACtD,EAAAA,WAAU,MAAM,SAAS;AAC3B;AASA,SAAS,4BAA4BA,YAAW,YAAY,eAAe,eAAe;AACxF,mBAAiB,YAAYA,UAAS;AACtC,MAAI,UAAU,WAAW,sBAAsB;AAC/C,MAAI,gBAAgBA,WAAU,sBAAsB;AACpD,MAAI,cAAc,QAAQ,QAAQ,cAAc;AAChD,MAAI,eAAe,QAAQ,SAAS,cAAc;AAClD,MAAI,eAAe,cAAc;AAC/B,QAAI,iDAAiD;AAAA,MACnD,OAAO,gBAAgB,cAAc,QAAQ,cAAc;AAAA,MAC3D,MAAM,gBAAgB,cAAc,OAAO,cAAc;AAAA,IAC3D;AACA,QAAI,CAAC,eAAe,mBAAmB,2CAA2C,GAAG;AACnF,MAAAA,WAAU,MAAM,SAAS,GAAG,OAAO,QAAQ,QAAQ,IAAI;AACvD,MAAAA,WAAU,MAAM,QAAQ,GAAG,OAAO,QAAQ,OAAO,IAAI;AAAA,IACvD;AACA,IAAAA,WAAU,MAAM,OAAO,GAAG,OAAO,WAAWA,WAAU,MAAM,IAAI,IAAI,+CAA+C,OAAO,aAAa,IAAI;AAC3I,IAAAA,WAAU,MAAM,MAAM,GAAG,OAAO,WAAWA,WAAU,MAAM,GAAG,IAAI,+CAA+C,MAAM,cAAc,IAAI;AAAA,EAC3I;AACF;AAMA,SAAS,iBAAiB,YAAY,UAAU;AAC9C,MAAI,gBAAgB,OAAO,iBAAiB,UAAU;AACtD,QAAM,KAAK,aAAa,EAAE,OAAO,SAAU,GAAG;AAC5C,WAAO,EAAE,WAAW,YAAY,KAAK,EAAE,WAAW,SAAS,KAAK,EAAE,WAAW,MAAM,KAAK,EAAE,WAAW,MAAM,KAAK,EAAE,WAAW,OAAO,KAAK,EAAE,WAAW,SAAS,KAAK,EAAE,WAAW,SAAS,KAAK,EAAE,WAAW,MAAM,KAAK,EAAE,WAAW,QAAQ,KAAK,MAAM,aAAa,MAAM,WAAW,MAAM;AAAA,IAE3R,eAAe,mBAAmB,2CAA2C,MAAM,MAAM,WAAW,MAAM;AAAA,EAC5G,CAAC,EAAE,QAAQ,SAAU,GAAG;AACtB,WAAO,SAAS,MAAM,YAAY,GAAG,cAAc,iBAAiB,CAAC,GAAG,cAAc,oBAAoB,CAAC,CAAC;AAAA,EAC9G,CAAC;AACH;AAOA,SAAS,eAAe,aAAa,cAAc;AACjD,cAAY,YAAY;AACxB,cAAY,cAAc,WAAY;AACpC,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc;AACjB,gBAAY,MAAM,aAAa;AAC/B,gBAAY,MAAM,mBAAmB;AACrC,gBAAY,MAAM,SAAS;AAAA,EAC7B,OAAO;AACL,gBAAY,MAAM,aAAa;AAC/B,gBAAY,MAAM,mBAAmB;AACrC,gBAAY,MAAM,SAAS;AAAA,EAC7B;AACF;AAMA,SAAS,YAAY,YAAY;AAC/B,aAAW,MAAM,UAAU;AAC3B,aAAW,MAAM,WAAW;AAC5B,aAAW,MAAM,SAAS;AAC5B;AAMA,SAAS,iBAAiB,UAAU;AAClC,WAAS,MAAM,aAAa;AAC5B,WAAS,aAAa,+BAA+B,MAAM;AAC7D;AAMA,SAAS,wBAAwB,UAAU;AACzC,WAAS,MAAM,aAAa;AAC5B,WAAS,gBAAgB,6BAA6B;AACxD;AAQA,SAAS,qBAAqB,WAAW;AACvC,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,WAAY;AAAA,EAAC;AACjG,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,WAAY;AAC/F,WAAO,CAAC;AAAA,EACV;AACA,YAAU,QAAQ,SAAU,IAAI;AAC9B,QAAI,SAAS,UAAU,EAAE;AACzB,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,OAAO;AAC3C,SAAG,MAAM,KAAK,IAAI,OAAO,KAAK;AAAA,IAChC,CAAC;AACD,eAAW,EAAE,EAAE,QAAQ,SAAU,GAAG;AAClC,aAAO,GAAG,UAAU,IAAI,CAAC;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH;AAQA,SAAS,uBAAuB,WAAW;AACzC,MAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,WAAY;AAAA,EAAC;AACjG,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,WAAY;AAC/F,WAAO,CAAC;AAAA,EACV;AACA,YAAU,QAAQ,SAAU,IAAI;AAC9B,QAAI,SAAS,UAAU,EAAE;AACzB,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,OAAO;AAC3C,SAAG,MAAM,KAAK,IAAI;AAAA,IACpB,CAAC;AACD,eAAW,EAAE,EAAE,QAAQ,SAAU,GAAG;AAClC,aAAO,GAAG,UAAU,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,CAAC;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC;AACH;AAOA,SAAS,iBAAiB,IAAI;AAC5B,MAAI,oBAAoB,GAAG,MAAM;AACjC,KAAG,MAAM,YAAY,OAAO,iBAAiB,EAAE,EAAE,iBAAiB,QAAQ;AAC1E,MAAI,mBAAmB,GAAG,MAAM;AAChC,KAAG,MAAM,WAAW,OAAO,iBAAiB,EAAE,EAAE,iBAAiB,OAAO;AACxE,SAAO,SAAS,OAAO;AACrB,OAAG,MAAM,YAAY;AACrB,OAAG,MAAM,WAAW;AAAA,EACtB;AACF;AAEA,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC,IAAI,mCAAmC;AACvC,IAAI,oCAAoC;AACxC,IAAI,8BAA8B;AAAA,EAChC,SAAS;AACX;AACA,IAAI,yCAAyC;AAC7C,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,0BAA0B;AAC9B,IAAI,yBAAyB;AAC7B,IAAI;AACJ,IAAI,0BAA0B;AAC9B,IAAI,+BAA+B,CAAC;AACpC,IAAI;AAGJ,IAAI,oBAAoB,oBAAI,IAAI;AAEhC,IAAI,eAAe,oBAAI,IAAI;AAE3B,IAAI,wBAAwB,oBAAI,QAAQ;AAGxC,SAAS,mBAAmB,YAAY,MAAM;AAC5C,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG;AAChC,sBAAkB,IAAI,MAAM,oBAAI,IAAI,CAAC;AAAA,EACvC;AACA,MAAI,CAAC,kBAAkB,IAAI,IAAI,EAAE,IAAI,UAAU,GAAG;AAChD,sBAAkB,IAAI,IAAI,EAAE,IAAI,UAAU;AAC1C,iCAA6B;AAAA,EAC/B;AACF;AACA,SAAS,qBAAqB,YAAY,MAAM;AAC9C,oBAAkB,IAAI,IAAI,EAAE,QAAQ,EAAE,UAAU;AAChD,+BAA6B;AAC7B,MAAI,kBAAkB,IAAI,IAAI,EAAE,SAAS,GAAG;AAC1C,sBAAkB,QAAQ,EAAE,IAAI;AAAA,EAClC;AACF;AAGA,SAAS,sBAAsB;AAC7B,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,YAAY,kBAAkB,IAAI,aAAa;AACnD,MAAI,YAAY,2BAA2B,SAAS,GAClD;AACF,MAAI;AACF,SAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,UAAI,KAAK,MAAM;AACf,SAAG,iBAAiB,4BAA4B,oBAAoB;AACpE,SAAG,iBAAiB,yBAAyB,iBAAiB;AAC9D,SAAG,iBAAiB,+BAA+B,wBAAwB;AAAA,IAC7E;AAAA,EACF,SAAS,KAAK;AACZ,cAAU,EAAE,GAAG;AAAA,EACjB,UAAE;AACA,cAAU,EAAE;AAAA,EACd;AACA,SAAO,iBAAiB,kCAAkC,YAAY;AAGtE,MAAI,gBAAgB,KAAK,IAAI,MAAM,MAAM,mBAAmB,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE,IAAI,SAAUG,KAAI;AACzG,WAAO,aAAa,IAAIA,GAAE,EAAE;AAAA,EAC9B,CAAC,CAAC,CAAC;AACH,MAAI,wBAAwB,kBAAkB,IAAI,mCAAmC,KAAK,IAAI,eAAe,2BAA2B;AACxI,kBAAgB,oBAAoB,WAAW,WAAY;AACzD,WAAO;AAAA,EACT,CAAC;AACD,UAAQ,WAAW,WAAW,wBAAwB,MAAM,aAAa;AAC3E;AACA,SAAS,wBAAwB;AAC/B,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,YAAY,kBAAkB,IAAI,aAAa;AACnD,MAAI,aAAa,2BAA2B,SAAS,GACnD;AACF,MAAI;AACF,SAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,UAAI,KAAK,OAAO;AAChB,SAAG,oBAAoB,4BAA4B,oBAAoB;AACvE,SAAG,oBAAoB,yBAAyB,iBAAiB;AACjE,SAAG,oBAAoB,+BAA+B,wBAAwB;AAAA,IAChF;AAAA,EACF,SAAS,KAAK;AACZ,eAAW,EAAE,GAAG;AAAA,EAClB,UAAE;AACA,eAAW,EAAE;AAAA,EACf;AACA,SAAO,oBAAoB,kCAAkC,YAAY;AAEzE,MAAI,eAAe;AACjB,kBAAc,QAAQ;AACtB,oBAAgB;AAAA,EAClB;AACA,YAAU;AACZ;AACA,SAAS,qBAAqB,OAAO;AACnC,SAAO,MAAM,UAAU,SAAU,MAAM;AACrC,WAAO,CAAC,CAAC,KAAK,gCAAgC;AAAA,EAChD,CAAC;AACH;AACA,SAAS,mBAAmBC,gBAAe;AACzC,MAAI;AACJ,SAAO,eAAe,eAAe,CAAC,GAAGA,cAAa,GAAG,CAAC,IAAI,mBAAmB,CAAC,GAAG,gBAAgB,kBAAkB,kCAAkC,IAAI,GAAG,gBAAgB,kBAAkB,aAAa,0BAA0B,GAAG,iBAAiB;AAC/P;AAGA,SAAS,qBAAqB,GAAG;AAC/B,aAAW,WAAY;AACrB,WAAO,CAAC,mBAAmB,EAAE,eAAe,EAAE,MAAM;AAAA,EACtD,CAAC;AACD,MAAI,kBAAkB,aAAa,IAAI,EAAE,aAAa,GACpD,QAAQ,gBAAgB,OACxB,yBAAyB,gBAAgB;AAC3C,MAAI,0BAA0B,EAAE,kBAAkB,gBAAgB;AAChE,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD;AAAA,EACF;AACA,4BAA0B;AAE1B,UAAQ,MAAM,OAAO,SAAU,MAAM;AACnC,WAAO,KAAK,WAAW,MAAM,aAAa,WAAW;AAAA,EACvD,CAAC;AACD,aAAW,WAAY;AACrB,WAAO,yBAAyB,OAAO,SAAS,KAAK,CAAC;AAAA,EACxD,CAAC;AACD,MAAI,mBAAmB,EAAE,eAAe;AACtC,QAAI,kBAAkB,aAAa,IAAI,cAAc,EAAE;AACvD,QAAI,qBAAqB,gBAAgB,OAAO,SAAU,MAAM;AAC9D,aAAO,CAAC,KAAK,gCAAgC;AAAA,IAC/C,CAAC;AACD,0BAAsB,gBAAgB,oBAAoB;AAAA,MACxD,SAAS,SAAS;AAAA,MAClB,IAAI,cAAc,WAAW;AAAA,MAC7B,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,qBAAqB,EAAE,OAAO,UAChC,QAAQ,mBAAmB,OAC3B,mBAAmB,mBAAmB;AACxC,MAAI,cAAc,oBAAoB,UAAU,EAAE,cAAc,SAAS,SAAS,IAAI,QAAQ,IAAI;AAClG,qBAAmB,EAAE;AACrB,QAAM,OAAO,aAAa,GAAG,YAAY;AACzC,wBAAsB,EAAE,eAAe,OAAO;AAAA,IAC5C,SAAS,SAAS;AAAA,IAClB,IAAI,cAAc,WAAW;AAAA,IAC7B,QAAQ,QAAQ;AAAA,EAClB,CAAC;AACH;AACA,SAAS,kBAAkB,GAAG;AAE5B,MAAI,CAAC,wBAAyB;AAC9B,aAAW,WAAY;AACrB,WAAO,CAAC,gBAAgB,EAAE,eAAe,EAAE,MAAM;AAAA,EACnD,CAAC;AACD,MAAI,mBAAmB,aAAa,IAAI,EAAE,aAAa,GACrD,gBAAgB,iBAAiB,OACjC,yBAAyB,iBAAiB;AAC5C,MAAI,0BAA0B,EAAE,kBAAkB,kBAAkB,EAAE,kBAAkB,kBAAkB;AACxG,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,mBAAmB,aAAa;AAC5C,MAAI,cAAc,qBAAqB,KAAK;AAC5C,MAAI,gBAAgB,IAAI;AACtB,UAAM,OAAO,aAAa,CAAC;AAAA,EAC7B;AACA,MAAI,eAAe;AACnB,qBAAmB;AACnB,MAAI,YAAY,EAAE,QAChB,OAAO,UAAU,MACjB,aAAa,UAAU;AACzB,MAAI,SAAS,mBAAmB,kBAAkB,SAAS,mBAAmB,oBAAoB,eAAe,kBAAkB,aAAa,IAAI,UAAU,EAAE,wBAAwB;AACtL,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD,8BAA0B;AAC1B,uBAAmB;AAEnB,QAAI,kBAAkB,iBAAiB,iBAAiB,QAAQ,mBAAmB,aAAa,IAAI,cAAc,EAAE,KAAK;AACzH,oBAAgB,OAAO,aAAa,GAAG,YAAY;AACnD,0BAAsB,gBAAgB,iBAAiB;AAAA,MACrD,SAAS,SAAS;AAAA,MAClB,IAAI,cAAc,WAAW;AAAA,MAC7B,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,wBAAsB,EAAE,eAAe,OAAO;AAAA,IAC5C,SAAS,SAAS;AAAA,IAClB,IAAI,cAAc,WAAW;AAAA,IAC7B,QAAQ,QAAQ;AAAA,EAClB,CAAC;AACH;AACA,SAAS,yBAAyB,GAAG;AACnC,aAAW,WAAY;AACrB,WAAO,CAAC,yBAAyB,EAAE,eAAe,EAAE,MAAM;AAAA,EAC5D,CAAC;AACD,MAAI,mBAAmB,aAAa,IAAI,EAAE,aAAa,GACrD,gBAAgB,iBAAiB,OACjC,yBAAyB,iBAAiB;AAC5C,MAAI,0BAA0B,EAAE,kBAAkB,gBAAgB;AAChE,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,mBAAmB,aAAa;AAC5C,4BAA0B;AAC1B,MAAI,QAAQ,EAAE,OAAO,SAAS;AAC9B,MAAI,cAAc,qBAAqB,KAAK;AAC5C,MAAI,gBAAgB,IAAI;AACtB,UAAM,OAAO,aAAa,CAAC;AAAA,EAC7B;AACA,QAAM,OAAO,OAAO,GAAG,YAAY;AACnC,wBAAsB,EAAE,eAAe,OAAO;AAAA,IAC5C,SAAS,SAAS;AAAA,IAClB,IAAI,cAAc,WAAW;AAAA,IAC7B,QAAQ,QAAQ;AAAA,EAClB,CAAC;AACH;AAGA,SAAS,gBAAgB,GAAG;AAC1B,IAAE,eAAe;AACjB,MAAI,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACnC,yBAAuB;AAAA,IACrB,GAAG,EAAE;AAAA,IACL,GAAG,EAAE;AAAA,EACP;AACA,YAAU,MAAM,YAAY,eAAe,OAAO,qBAAqB,IAAI,uBAAuB,GAAG,MAAM,EAAE,OAAO,qBAAqB,IAAI,uBAAuB,GAAG,QAAQ;AACjL;AACA,SAAS,eAAe;AACtB,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,2BAAyB;AAEzB,SAAO,oBAAoB,aAAa,eAAe;AACvD,SAAO,oBAAoB,aAAa,eAAe;AACvD,SAAO,oBAAoB,WAAW,YAAY;AAClD,SAAO,oBAAoB,YAAY,YAAY;AACnD,wBAAsB;AACtB,sCAAoC,SAAS;AAC7C,MAAI,CAAC,kBAAkB;AACrB,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD,uBAAmB;AAAA,EACrB;AACA,aAAW,WAAY;AACrB,WAAO,CAAC,iBAAiB,gBAAgB;AAAA,EAC3C,CAAC;AACD,MAAI,mBAAmB,aAAa,IAAI,gBAAgB,GACtD,QAAQ,iBAAiB,OACzB,OAAO,iBAAiB;AAC1B,yBAAuB,kBAAkB,IAAI,IAAI,GAAG,SAAU,IAAI;AAChE,WAAO,aAAa,IAAI,EAAE,EAAE;AAAA,EAC9B,GAAG,SAAU,IAAI;AACf,WAAO,aAAa,IAAI,EAAE,EAAE;AAAA,EAC9B,CAAC;AACD,MAAI,cAAc,qBAAqB,KAAK;AAE5C,MAAI,gBAAgB,IAAI;AACtB,QAAI,qBAAqB,gBAAgB;AACvC,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,UAAQ,MAAM,IAAI,SAAU,MAAM;AAChC,WAAO,KAAK,gCAAgC,IAAI,gBAAgB;AAAA,EAClE,CAAC;AACD,WAAS,qBAAqB;AAC5B,gCAA4B;AAC5B,0BAAsB,kBAAkB,OAAO;AAAA,MAC7C,SAAS,0BAA0B,SAAS,yBAAyB,SAAS;AAAA,MAC9E,IAAI,cAAc,WAAW;AAAA,MAC7B,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,QAAI,qBAAqB,gBAAgB;AAEvC,4BAAsB,gBAAgB,aAAa,IAAI,cAAc,EAAE,OAAO;AAAA,QAC5E,SAAS,SAAS;AAAA,QAClB,IAAI,cAAc,WAAW;AAAA,QAC7B,QAAQ,QAAQ;AAAA,MAClB,CAAC;AAAA,IACH;AAEA,QAAI,cAAc,MAAM,KAAK,iBAAiB,QAAQ,EAAE,KAAK,SAAU,GAAG;AACxE,aAAO,EAAE,aAAa,6BAA6B;AAAA,IACrD,CAAC;AACD,QAAI,YAAa,yBAAwB,WAAW;AACpD,oBAAgB;AAAA,EAClB;AACA,MAAI,aAAa,IAAI,gBAAgB,EAAE,uBAAuB;AAC5D,uBAAmB;AAAA,EACrB,OAAO;AACL,kCAA8B,aAAa,kBAAkB;AAAA,EAC/D;AACF;AAGA,SAAS,8BAA8B,aAAa,UAAU;AAC5D,MAAI,eAAe,cAAc,KAAK,4BAA4B,iBAAiB,SAAS,WAAW,GAAG,KAAK,IAAI,4BAA4B,kBAAkB,KAAK;AACtK,MAAI,eAAe;AAAA,IACjB,GAAG,aAAa,OAAO,WAAW,UAAU,MAAM,IAAI;AAAA,IACtD,GAAG,aAAa,MAAM,WAAW,UAAU,MAAM,GAAG;AAAA,EACtD;AACA,MAAI,mBAAmB,aAAa,IAAI,gBAAgB,GACtD,0BAA0B,iBAAiB;AAC7C,MAAI,aAAa,aAAa,OAAO,yBAAyB,SAAS;AACvE,YAAU,MAAM,aAAa,UAAU,MAAM,aAAa,UAAU,MAAM,aAAa,MAAM,aAAa;AAC1G,YAAU,MAAM,YAAY,eAAe,OAAO,aAAa,GAAG,MAAM,EAAE,OAAO,aAAa,GAAG,QAAQ;AACzG,SAAO,WAAW,UAAU,uBAAuB;AACrD;AACA,SAAS,8BAA8B,IAAI,SAAS;AAClD,+BAA6B,KAAK;AAAA,IAChC;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,sBAAsB,WAAY;AACvC,gBAAY,EAAE;AACd,aAAS,KAAK,YAAY,EAAE;AAAA,EAC9B,CAAC;AACH;AAEA,SAAS,kBAAkB;AACzB,YAAU,OAAO;AACjB,qBAAmB,OAAO;AAC1B,MAAI,6BAA6B,QAAQ;AACvC,eAAW,WAAY;AACrB,aAAO,CAAC,oDAAoD,4BAA4B;AAAA,IAC1F,CAAC;AACD,iCAA6B,QAAQ,SAAU,MAAM;AACnD,UAAI,KAAK,KAAK,IACZ,UAAU,KAAK;AACjB,cAAQ;AACR,SAAG,OAAO;AAAA,IACZ,CAAC;AACD,mCAA+B,CAAC;AAAA,EAClC;AACA,cAAY;AACZ,uBAAqB;AACrB,kBAAgB;AAChB,kBAAgB;AAChB,mBAAiB;AACjB,gBAAc;AACd,iBAAe;AACf,qBAAmB;AACnB,2BAAyB;AACzB,yBAAuB;AACvB,4BAA0B;AAC1B,2BAAyB;AACzB,gCAA8B;AAC9B,4BAA0B;AAC5B;AACA,SAAS,UAAU,MAAM,SAAS;AAChC,MAAI,cAAc;AAClB,MAAI,SAAS;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,wBAAwB;AAAA,IACxB,iBAAiB;AAAA,IACjB,mBAAmB,CAAC;AAAA,IACpB,yBAAyB,SAAS,0BAA0B;AAAA,IAAC;AAAA,IAC7D,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACzB;AACA,aAAW,WAAY;AACrB,WAAO,CAAC,+BAA+B,OAAO,SAAS,OAAO,GAAG,YAAY,EAAE,OAAO,SAAS,MAAM,CAAC,GAAG;AAAA,MACvG;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,UAAU,oBAAI,IAAI;AACtB,WAAS,oBAAoB;AAC3B,WAAO,iBAAiB,aAAa,+BAA+B;AAAA,MAClE,SAAS;AAAA,IACX,CAAC;AACD,WAAO,iBAAiB,aAAa,+BAA+B;AAAA,MAClE,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,WAAO,iBAAiB,WAAW,kBAAkB;AAAA,MACnD,SAAS;AAAA,IACX,CAAC;AACD,WAAO,iBAAiB,YAAY,kBAAkB;AAAA,MACpD,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,WAAS,uBAAuB;AAC9B,WAAO,oBAAoB,aAAa,6BAA6B;AACrE,WAAO,oBAAoB,aAAa,6BAA6B;AACrE,WAAO,oBAAoB,WAAW,gBAAgB;AACtD,WAAO,oBAAoB,YAAY,gBAAgB;AAAA,EACzD;AACA,WAAS,iBAAiB,GAAG;AAC3B,yBAAqB;AACrB,yBAAqB;AACrB,6BAAyB;AACzB,2BAAuB;AAGvB,QAAI,EAAE,SAAS,YAAY;AACzB,UAAI,aAAa,IAAI,MAAM,SAAS;AAAA,QAClC,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAED,QAAE,OAAO,cAAc,UAAU;AAAA,IACnC;AAAA,EACF;AACA,WAAS,8BAA8B,GAAG;AACxC,MAAE,eAAe;AACjB,QAAI,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACnC,2BAAuB;AAAA,MACrB,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACP;AACA,QAAI,KAAK,IAAI,qBAAqB,IAAI,uBAAuB,CAAC,KAAK,qCAAqC,KAAK,IAAI,qBAAqB,IAAI,uBAAuB,CAAC,KAAK,mCAAmC;AACxM,2BAAqB;AACrB,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,WAAS,gBAAgB,GAAG;AAE1B,QAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,OAAO,UAAU,UAAa,EAAE,OAAO,oBAAoB;AAChG,iBAAW,WAAY;AACrB,eAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF;AAEA,QAAI,EAAE,QAAQ;AACZ,iBAAW,WAAY;AACrB,eAAO,oCAAoC,OAAO,EAAE,MAAM;AAAA,MAC5D,CAAC;AACD;AAAA,IACF;AACA,QAAI,yBAAyB;AAC3B,iBAAW,WAAY;AACrB,eAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF;AACA,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,QAAI,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACnC,6BAAyB;AAAA,MACvB,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACP;AACA,2BAAuB,eAAe,CAAC,GAAG,sBAAsB;AAChE,yBAAqB,EAAE;AACvB,sBAAkB;AAAA,EACpB;AACA,WAAS,kBAAkB;AACzB,eAAW,WAAY;AACrB,aAAO,CAAC,sBAAsB,OAAO,SAAS,MAAM,CAAC,GAAG,kBAAkB;AAAA,IAC5E,CAAC;AACD,8BAA0B;AAG1B,QAAI,aAAa,QAAQ,IAAI,kBAAkB;AAC/C,kBAAc;AACd,qBAAiB,mBAAmB;AAEpC,QAAI,WAAW,eAAe,QAAQ,QAAQ,KAAK,eAAe,QAAQ,WAAW,KAAK,eAAe,YAAY;AACrH,QAAI,qBAAqB,SAAS,QAAQ;AAC1C,QAAI,gBAAgB,OAAO,OACzB,OAAO,OAAO,MACd,wBAAwB,OAAO;AACjC,QAAI,QAAQ,mBAAmB,aAAa;AAC5C,oBAAgB,MAAM,UAAU;AAChC,oBAAgB;AAChB,mBAAe,mBAAmB,aAAa;AAG/C,gBAAY,yBAAyB,oBAAoB,yBAAyB,oBAAoB;AACtG,uBAAmB,YAAY,SAAS;AAExC,aAAS,2BAA2B;AAClC,UAAI,CAAC,mBAAmB,eAAe;AACrC,2BAAmB,aAAa,wCAAwC,IAAI;AAC5E,2BAAmB,YAAY,kBAAkB;AAEjD,4BAAoB;AACpB,oBAAY,kBAAkB;AAE9B,qBAAa,WAAW,IAAI,cAAc,WAAW;AAErD,kBAAU,MAAM;AAAA,MAClB,OAAO;AACL,eAAO,sBAAsB,wBAAwB;AAAA,MACvD;AAAA,IACF;AACA,WAAO,sBAAsB,wBAAwB;AACrD,yBAAqB,MAAM,KAAK,kBAAkB,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,SAAU,IAAI;AACvF,aAAO,OAAO,kBAAkB,CAAC,aAAa,IAAI,EAAE,EAAE;AAAA,IACxD,CAAC,GAAG,SAAU,IAAI;AAChB,aAAO,aAAa,IAAI,EAAE,EAAE;AAAA,IAC9B,GAAG,SAAU,IAAI;AACf,aAAO,aAAa,IAAI,EAAE,EAAE;AAAA,IAC9B,CAAC;AAGD,UAAM,OAAO,YAAY,GAAG,YAAY;AACxC,kCAA8B,iBAAiB,cAAc;AAC7D,0BAAsB,gBAAgB,OAAO;AAAA,MAC3C,SAAS,SAAS;AAAA,MAClB,IAAI,cAAc,WAAW;AAAA,MAC7B,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAGD,WAAO,iBAAiB,aAAa,iBAAiB;AAAA,MACpD,SAAS;AAAA,IACX,CAAC;AACD,WAAO,iBAAiB,aAAa,iBAAiB;AAAA,MACpD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,WAAO,iBAAiB,WAAW,cAAc;AAAA,MAC/C,SAAS;AAAA,IACX,CAAC;AACD,WAAO,iBAAiB,YAAY,cAAc;AAAA,MAChD,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,WAAS,UAAU,OAAO;AACxB,QAAI,cAAc,MAAM,OACtB,QAAQ,gBAAgB,SAAS,SAAY,aAC7C,uBAAuB,MAAM,gBAC7B,0BAA0B,yBAAyB,SAAS,IAAI,sBAChE,aAAa,MAAM,MACnB,UAAU,eAAe,SAAS,2BAA2B,YAC7D,qBAAqB,MAAM,cAC3B,eAAe,uBAAuB,SAAS,QAAQ,oBACvD,sBAAsB,MAAM,eAC5B,gBAAgB,wBAAwB,SAAS,QAAQ,qBACzD,wBAAwB,MAAM,wBAC9B,yBAAyB,0BAA0B,SAAS,QAAQ,uBACpE,wBAAwB,MAAM,iBAC9B,kBAAkB,0BAA0B,SAAS,8BAA8B,uBACnF,wBAAwB,MAAM,mBAC9B,oBAAoB,0BAA0B,SAAS,CAAC,IAAI,uBAC5D,wBAAwB,MAAM,yBAC9B,0BAA0B,0BAA0B,SAAS,WAAY;AAAA,IAAC,IAAI,uBAC9E,wBAAwB,MAAM,uBAC9B,wBAAwB,0BAA0B,SAAS,QAAQ,uBACnE,wBAAwB,MAAM,uBAC9B,wBAAwB,0BAA0B,SAAS,QAAQ;AACrE,WAAO,0BAA0B;AACjC,QAAI,OAAO,QAAQ,YAAY,OAAO,MAAM;AAC1C,2BAAqB,MAAM,OAAO,IAAI;AAAA,IACxC;AACA,WAAO,OAAO;AACd,WAAO,QAAQ,mBAAmB,KAAK;AACvC,WAAO,eAAe;AACtB,WAAO,gBAAgB;AACvB,WAAO,0BAA0B;AACjC,WAAO,wBAAwB;AAC/B,WAAO,wBAAwB;AAG/B,QAAI,eAAe,2BAA2B,CAAC,2BAA2B,CAAC,uBAAuB,iBAAiB,OAAO,eAAe,KAAK,CAAC,+BAA+B,mBAAmB,OAAO,iBAAiB,IAAI;AAC3N,6BAAuB,CAAC,IAAI,GAAG,WAAY;AACzC,eAAO,OAAO;AAAA,MAChB,GAAG,WAAY;AACb,eAAO;AAAA,MACT,CAAC;AACD,2BAAqB,CAAC,IAAI,GAAG,WAAY;AACvC,eAAO;AAAA,MACT,GAAG,WAAY;AACb,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO,kBAAkB;AACzB,WAAO,oBAAoB,mBAAmB,iBAAiB;AAG/D,aAAS,cAAc,IAAI,UAAU;AACnC,aAAO,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,EAAE,EAAE,QAAQ,IAAI,OAAO,QAAQ;AAAA,IAChF;AACA,QAAI,eAAe,2BAA2B,OAAO,2BAA2B,wBAAwB;AACtG,UAAI,wBAAwB;AAC1B,+BAAuB,CAAC,IAAI,GAAG,SAAU,IAAI;AAC3C,iBAAO,cAAc,IAAI,iBAAiB;AAAA,QAC5C,GAAG,SAAU,IAAI;AACf,iBAAO,cAAc,IAAI,mBAAmB;AAAA,QAC9C,CAAC;AAAA,MACH,OAAO;AACL,6BAAqB,CAAC,IAAI,GAAG,SAAU,IAAI;AACzC,iBAAO,cAAc,IAAI,iBAAiB;AAAA,QAC5C,GAAG,SAAU,IAAI;AACf,iBAAO,cAAc,IAAI,mBAAmB;AAAA,QAC9C,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,yBAAyB;AAChC,iBAAa,IAAI,MAAM,MAAM;AAC7B,uBAAmB,MAAM,OAAO;AAChC,QAAI,cAAc,0BAA0B,qBAAqB,OAAO,KAAK,IAAI;AACjF,aAAS,MAAM,GAAG,MAAM,KAAK,SAAS,QAAQ,OAAO;AACnD,UAAI,cAAc,KAAK,SAAS,GAAG;AACnC,qBAAe,aAAa,YAAY;AACxC,UAAI,QAAQ,aAAa;AACvB,YAAI,CAAC,eAAe;AAClB,sCAA4B,WAAW,aAAa,qBAAqB,GAAG,qBAAqB,CAAC;AAAA,QACpG;AACA,eAAO,wBAAwB,WAAW,eAAe,GAAG;AAC5D,yBAAiB,WAAW;AAC5B;AAAA,MACF;AACA,kBAAY,oBAAoB,aAAa,sBAAsB,IAAI,WAAW,CAAC;AACnF,kBAAY,oBAAoB,cAAc,sBAAsB,IAAI,WAAW,CAAC;AACpF,UAAI,CAAC,cAAc;AACjB,oBAAY,iBAAiB,aAAa,eAAe;AACzD,oBAAY,iBAAiB,cAAc,eAAe;AAC1D,8BAAsB,IAAI,aAAa,eAAe;AAAA,MACxD;AAEA,cAAQ,IAAI,aAAa,GAAG;AAC5B,UAAI,CAAC,aAAa;AAChB,sBAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,YAAU,OAAO;AACjB,SAAO;AAAA,IACL,QAAQ,SAAS,OAAO,YAAY;AAClC,iBAAW,WAAY;AACrB,eAAO,2CAA2C,OAAO,SAAS,UAAU,CAAC;AAAA,MAC/E,CAAC;AACD,gBAAU,UAAU;AAAA,IACtB;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,eAAS,YAAY;AACnB,mBAAW,WAAY;AACrB,iBAAO;AAAA,QACT,CAAC;AACD,6BAAqB,MAAM,aAAa,IAAI,IAAI,EAAE,IAAI;AACtD,qBAAa,QAAQ,EAAE,IAAI;AAAA,MAC7B;AACA,UAAI,2BAA2B,CAAC,KAAK,QAAQ,IAAI,OAAO,wCAAwC,GAAG,CAAC,GAAG;AACrG,mBAAW,WAAY;AACrB,iBAAO;AAAA,QACT,CAAC;AACD,sCAA8B,MAAM,SAAS;AAAA,MAC/C,OAAO;AACL,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI;AACJ,IAAI,oBAAoB;AAAA,EACtB,iBAAiB;AAAA,EACjB,wBAAwB;AAC1B;AACA,IAAI,qBAAqB,qBAAqB,CAAC,GAAG,gBAAgB,oBAAoB,kBAAkB,iBAAiB,wEAAwE,GAAG,gBAAgB,oBAAoB,kBAAkB,wBAAwB,uCAAuC,GAAG;AAC5T,IAAI,eAAe;AACnB,IAAI;AACJ,SAAS,oBAAoB;AAC3B,MAAI,WAAW;AAEb;AAAA,EACF;AAEA,cAAY,SAAS,cAAc,KAAK;AACxC,GAAC,SAAS,gBAAgB;AACxB,cAAU,KAAK;AAGf,cAAU,MAAM,WAAW;AAC3B,cAAU,MAAM,SAAS;AACzB,cAAU,MAAM,OAAO;AACvB,cAAU,MAAM,SAAS;AACzB,cAAU,MAAM,UAAU;AAC1B,cAAU,MAAM,SAAS;AACzB,cAAU,MAAM,QAAQ;AACxB,cAAU,aAAa,QAAQ,OAAO;AAAA,EACxC,GAAG;AACH,WAAS,KAAK,QAAQ,SAAS;AAG/B,SAAO,QAAQ,iBAAiB,EAAE,QAAQ,SAAU,MAAM;AACxD,QAAI,QAAQ,eAAe,MAAM,CAAC,GAChC,KAAK,MAAM,CAAC,GACZ,MAAM,MAAM,CAAC;AACf,WAAO,SAAS,KAAK,QAAQ,uBAAuB,IAAI,GAAG,CAAC;AAAA,EAC9D,CAAC;AACH;AAMA,SAAS,WAAW;AAClB,MAAI,WAAY,QAAO;AACvB,MAAI,SAAS,eAAe,YAAY;AACtC,sBAAkB;AAAA,EACpB,OAAO;AACL,WAAO,iBAAiB,oBAAoB,iBAAiB;AAAA,EAC/D;AACA,SAAO,eAAe,CAAC,GAAG,iBAAiB;AAC7C;AAKA,SAAS,cAAc;AACrB,MAAI,cAAc,CAAC,UAAW;AAC9B,SAAO,KAAK,iBAAiB,EAAE,QAAQ,SAAU,IAAI;AACnD,QAAI;AACJ,YAAQ,wBAAwB,SAAS,eAAe,EAAE,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,OAAO;AAAA,EACpJ,CAAC;AACD,YAAU,OAAO;AACjB,cAAY;AACd;AACA,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,MAAM,SAAS,cAAc,KAAK;AACtC,MAAI,KAAK;AACT,MAAI,YAAY,MAAM,OAAO,KAAK,MAAM;AACxC,MAAI,MAAM,UAAU;AACpB,MAAI,MAAM,WAAW;AACrB,MAAI,MAAM,SAAS;AACnB,SAAO;AACT;AAMA,SAAS,oBAAoB,KAAK;AAChC,MAAI,WAAY;AAChB,MAAI,CAAC,WAAW;AACd,sBAAkB;AAAA,EACpB;AACA,YAAU,YAAY;AACtB,MAAI,YAAY,SAAS,eAAe,GAAG;AAC3C,YAAU,YAAY,SAAS;AAE/B,YAAU,MAAM,UAAU;AAC1B,YAAU,MAAM,UAAU;AAC5B;AAEA,IAAI,yBAAyB;AAC7B,IAAI,4BAA4B;AAAA,EAC9B,SAAS;AACX;AACA,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI,iBAAiB;AACrB,IAAI;AACJ,IAAI;AACJ,IAAI,mBAAmB;AACvB,IAAI,iBAAiB,oBAAI,QAAQ;AACjC,IAAI,uBAAuB,oBAAI,QAAQ;AACvC,IAAI,qBAAqB,oBAAI,QAAQ;AACrC,IAAI,cAAc,oBAAI,IAAI;AAC1B,IAAI,aAAa,oBAAI,IAAI;AACzB,IAAI,kBAAkB,oBAAI,IAAI;AAO9B,IAAI;AAGJ,SAAS,iBAAiB,YAAY,MAAM;AAC1C,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,gBAAgB,SAAS,GAAG;AAC9B,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD,sBAAkB,SAAS;AAC3B,WAAO,iBAAiB,WAAW,oBAAoB;AACvD,WAAO,iBAAiB,SAAS,kBAAkB;AAAA,EACrD;AACA,MAAI,CAAC,gBAAgB,IAAI,IAAI,GAAG;AAC9B,oBAAgB,IAAI,MAAM,oBAAI,IAAI,CAAC;AAAA,EACrC;AACA,MAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE,IAAI,UAAU,GAAG;AAC9C,oBAAgB,IAAI,IAAI,EAAE,IAAI,UAAU;AACxC,iCAA6B;AAAA,EAC/B;AACF;AACA,SAAS,mBAAmB,YAAY,MAAM;AAC5C,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,cAAc,YAAY;AAC5B,eAAW;AAAA,EACb;AACA,kBAAgB,IAAI,IAAI,EAAE,QAAQ,EAAE,UAAU;AAC9C,+BAA6B;AAC7B,MAAI,gBAAgB,IAAI,IAAI,EAAE,SAAS,GAAG;AACxC,oBAAgB,QAAQ,EAAE,IAAI;AAAA,EAChC;AACA,MAAI,gBAAgB,SAAS,GAAG;AAC9B,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD,WAAO,oBAAoB,WAAW,oBAAoB;AAC1D,WAAO,oBAAoB,SAAS,kBAAkB;AACtD,sBAAkB;AAClB,gBAAY;AAAA,EACd;AACF;AACA,SAAS,qBAAqB,GAAG;AAC/B,MAAI,CAAC,WAAY;AACjB,UAAQ,EAAE,KAAK;AAAA,IACb,KAAK,UACH;AACE,iBAAW;AACX;AAAA,IACF;AAAA,EACJ;AACF;AACA,SAAS,qBAAqB;AAC5B,MAAI,CAAC,WAAY;AACjB,MAAI,CAAC,eAAe,IAAI,SAAS,aAAa,GAAG;AAC/C,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD,eAAW;AAAA,EACb;AACF;AACA,SAAS,gBAAgB,GAAG;AAC1B,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,CAAC,WAAY;AACjB,MAAI,iBAAiB,EAAE;AACvB,MAAI,mBAAmB,UAAW;AAClC,mBAAiB,eAAe,aAAa,YAAY,KAAK;AAC9D,MAAI,kBAAkB,WAAW,IAAI,SAAS,GAC5C,cAAc,gBAAgB;AAChC,MAAI,aAAa,YAAY,KAAK,SAAU,MAAM;AAChD,WAAO,KAAK,WAAW,MAAM;AAAA,EAC/B,CAAC;AACD,MAAI,YAAY,YAAY,QAAQ,UAAU;AAC9C,MAAI,aAAa,YAAY,OAAO,WAAW,CAAC,EAAE,CAAC;AACnD,MAAI,mBAAmB,WAAW,IAAI,cAAc,GAClD,cAAc,iBAAiB,OAC/B,mBAAmB,iBAAiB;AACtC,MAAI,eAAe,sBAAsB,EAAE,MAAM,UAAU,sBAAsB,EAAE,OAAO,eAAe,sBAAsB,EAAE,OAAO,UAAU,sBAAsB,EAAE,MAAM;AAC9K,gBAAY,KAAK,UAAU;AAC3B,QAAI,CAAC,kBAAkB;AACrB,0BAAoB,cAAc,OAAO,kBAAkB,0BAA0B,EAAE,OAAO,cAAc,CAAC;AAAA,IAC/G;AAAA,EACF,OAAO;AACL,gBAAY,QAAQ,UAAU;AAC9B,QAAI,CAAC,kBAAkB;AACrB,0BAAoB,cAAc,OAAO,kBAAkB,gCAAgC,EAAE,OAAO,cAAc,CAAC;AAAA,IACrH;AAAA,EACF;AACA,MAAI,SAAS;AACb,wBAAsB,QAAQ,aAAa;AAAA,IACzC,SAAS,SAAS;AAAA,IAClB,IAAI;AAAA,IACJ,QAAQ,QAAQ;AAAA,EAClB,CAAC;AACD,wBAAsB,gBAAgB,aAAa;AAAA,IACjD,SAAS,SAAS;AAAA,IAClB,IAAI;AAAA,IACJ,QAAQ,QAAQ;AAAA,EAClB,CAAC;AACD,cAAY;AACd;AACA,SAAS,sBAAsB;AAC7B,cAAY,QAAQ,SAAU,MAAM,IAAI;AACtC,QAAI,SAAS,KAAK;AAClB,WAAO,OAAO,WAAW,IAAI,EAAE,CAAC;AAAA,EAClC,CAAC;AACH;AACA,SAAS,aAAa;AACpB,MAAI,mBAAmB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC3F,aAAW,WAAY;AACrB,WAAO;AAAA,EACT,CAAC;AACD,MAAI,CAAC,WAAW,IAAI,SAAS,EAAE,kBAAkB;AAC/C,wBAAoB,yBAAyB,OAAO,gBAAgB,CAAC;AAAA,EACvE;AACA,MAAI,eAAe,IAAI,SAAS,aAAa,GAAG;AAC9C,aAAS,cAAc,KAAK;AAAA,EAC9B;AACA,MAAI,kBAAkB;AACpB,0BAAsB,WAAW,WAAW,IAAI,SAAS,EAAE,OAAO;AAAA,MAChE,SAAS,SAAS;AAAA,MAClB,IAAI;AAAA,MACJ,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AACA,yBAAuB,gBAAgB,IAAI,eAAe,GAAG,SAAU,IAAI;AACzE,WAAO,WAAW,IAAI,EAAE,EAAE;AAAA,EAC5B,GAAG,SAAU,IAAI;AACf,WAAO,WAAW,IAAI,EAAE,EAAE;AAAA,EAC5B,CAAC;AACD,gBAAc;AACd,kBAAgB;AAChB,qBAAmB;AACnB,oBAAkB;AAClB,cAAY;AACZ,mBAAiB;AACjB,eAAa;AACb,sBAAoB;AACtB;AAEA,SAAS,UAAU,MAAM,SAAS;AAChC,MAAI,SAAS;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,IACxB,iBAAiB;AAAA,IACjB,mBAAmB,CAAC;AAAA,IACpB,kBAAkB;AAAA,EACpB;AACA,WAAS,KAAK,KAAK,GAAG,GAAG;AACvB,QAAI,IAAI,UAAU,EAAG;AACrB,QAAI,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAAA,EAC9C;AACA,WAAS,cAAc,GAAG;AACxB,eAAW,WAAY;AACrB,aAAO,CAAC,qBAAqB,EAAE,GAAG;AAAA,IACpC,CAAC;AACD,YAAQ,EAAE,KAAK;AAAA,MACb,KAAK;AAAA,MACL,KAAK,KACH;AAEE,aAAK,EAAE,OAAO,aAAa,UAAa,EAAE,OAAO,QAAQ,EAAE,OAAO,sBAAsB,CAAC,eAAe,IAAI,EAAE,MAAM,GAAG;AACrH;AAAA,QACF;AACA,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAClB,YAAI,YAAY;AAEd,qBAAW;AAAA,QACb,OAAO;AAEL,0BAAgB,CAAC;AAAA,QACnB;AACA;AAAA,MACF;AAAA,MACF,KAAK;AAAA,MACL,KAAK,cACH;AACE,YAAI,CAAC,WAAY;AACjB,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAClB,YAAI,mBAAmB,WAAW,IAAI,IAAI,GACxC,QAAQ,iBAAiB;AAC3B,YAAI,WAAW,MAAM,KAAK,KAAK,QAAQ;AACvC,YAAI,MAAM,SAAS,QAAQ,EAAE,aAAa;AAC1C,mBAAW,WAAY;AACrB,iBAAO,CAAC,cAAc,GAAG;AAAA,QAC3B,CAAC;AACD,YAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,cAAI,CAAC,OAAO,kBAAkB;AAC5B,gCAAoB,cAAc,OAAO,kBAAkB,eAAe,EAAE,OAAO,MAAM,GAAG,eAAe,EAAE,OAAO,cAAc,CAAC;AAAA,UACrI;AACA,eAAK,OAAO,KAAK,MAAM,CAAC;AACxB,gCAAsB,MAAM,OAAO;AAAA,YACjC,SAAS,SAAS;AAAA,YAClB,IAAI;AAAA,YACJ,QAAQ,QAAQ;AAAA,UAClB,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MACF,KAAK;AAAA,MACL,KAAK,aACH;AACE,YAAI,CAAC,WAAY;AACjB,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAClB,YAAI,mBAAmB,WAAW,IAAI,IAAI,GACxC,SAAS,iBAAiB;AAC5B,YAAI,YAAY,MAAM,KAAK,KAAK,QAAQ;AACxC,YAAI,OAAO,UAAU,QAAQ,EAAE,aAAa;AAC5C,mBAAW,WAAY;AACrB,iBAAO,CAAC,YAAY,IAAI;AAAA,QAC1B,CAAC;AACD,YAAI,OAAO,GAAG;AACZ,cAAI,CAAC,OAAO,kBAAkB;AAC5B,gCAAoB,cAAc,OAAO,kBAAkB,eAAe,EAAE,OAAO,MAAM,eAAe,EAAE,OAAO,cAAc,CAAC;AAAA,UAClI;AACA,eAAK,QAAQ,MAAM,OAAO,CAAC;AAC3B,gCAAsB,MAAM,QAAQ;AAAA,YAClC,SAAS,SAAS;AAAA,YAClB,IAAI;AAAA,YACJ,QAAQ,QAAQ;AAAA,UAClB,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AACA,WAAS,gBAAgB,GAAG;AAC1B,eAAW,WAAY;AACrB,aAAO;AAAA,IACT,CAAC;AACD,0BAAsB,EAAE,aAAa;AACrC,gBAAY;AACZ,sBAAkB,OAAO;AACzB,iBAAa;AACb,QAAI,cAAc,MAAM,KAAK,gBAAgB,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,SAAU,IAAI;AAClF,aAAO,OAAO,aAAa,CAAC,WAAW,IAAI,EAAE,EAAE;AAAA,IACjD,CAAC;AACD,yBAAqB,aAAa,SAAU,IAAI;AAC9C,aAAO,WAAW,IAAI,EAAE,EAAE;AAAA,IAC5B,GAAG,SAAU,IAAI;AACf,aAAO,WAAW,IAAI,EAAE,EAAE;AAAA,IAC5B,CAAC;AACD,QAAI,CAAC,OAAO,kBAAkB;AAC5B,UAAI,MAAM,yBAAyB,OAAO,kBAAkB,kDAAkD,EAAE,OAAO,cAAc;AACrI,UAAI,YAAY,SAAS,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,0BAAoB,GAAG;AAAA,IACzB;AACA,0BAAsB,MAAM,WAAW,IAAI,IAAI,EAAE,OAAO;AAAA,MACtD,SAAS,SAAS;AAAA,MAClB,IAAI;AAAA,MACJ,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,wBAAoB;AAAA,EACtB;AACA,WAAS,YAAY,GAAG;AACtB,QAAI,CAAC,WAAY;AACjB,QAAI,EAAE,kBAAkB,YAAa;AACrC,MAAE,gBAAgB;AAClB,eAAW,KAAK;AAChB,oBAAgB,CAAC;AAAA,EACnB;AACA,WAAS,sBAAsB,aAAa;AAC1C,QAAI,mBAAmB,WAAW,IAAI,IAAI,GACxC,QAAQ,iBAAiB;AAC3B,QAAI,WAAW,MAAM,KAAK,KAAK,QAAQ;AACvC,QAAI,iBAAiB,SAAS,QAAQ,WAAW;AACjD,kBAAc;AACd,gBAAY,WAAW,OAAO;AAC9B,oBAAgB,MAAM,cAAc,EAAE,WAAW;AACjD,uBAAmB,SAAS,cAAc,EAAE,aAAa,YAAY,KAAK;AAAA,EAC5E;AACA,WAAS,UAAU,OAAO;AACxB,QAAI,cAAc,MAAM,OACtB,QAAQ,gBAAgB,SAAS,CAAC,IAAI,aACtC,aAAa,MAAM,MACnB,UAAU,eAAe,SAAS,yBAAyB,YAC3D,qBAAqB,MAAM,cAC3B,eAAe,uBAAuB,SAAS,QAAQ,oBACvD,qBAAqB,MAAM,cAC3B,eAAe,uBAAuB,SAAS,IAAI,oBACnD,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,IAAI,uBAC1D,wBAAwB,MAAM,wBAC9B,yBAAyB,0BAA0B,SAAS,QAAQ,uBACpE,wBAAwB,MAAM,iBAC9B,kBAAkB,0BAA0B,SAAS,4BAA4B,uBACjF,wBAAwB,MAAM,mBAC9B,oBAAoB,0BAA0B,SAAS,CAAC,IAAI,uBAC5D,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,QAAQ;AAChE,WAAO,QAAQ,mBAAmB,KAAK;AACvC,WAAO,eAAe;AACtB,WAAO,yBAAyB;AAChC,WAAO,eAAe;AACtB,WAAO,mBAAmB;AAC1B,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAC3B,WAAO,mBAAmB;AAC1B,QAAI,OAAO,QAAQ,YAAY,OAAO,MAAM;AAC1C,yBAAmB,MAAM,OAAO,IAAI;AAAA,IACtC;AACA,WAAO,OAAO;AACd,qBAAiB,MAAM,OAAO;AAC9B,QAAI,CAAC,kBAAkB;AACrB,WAAK,aAAa,iBAAiB,YAAY;AAC/C,WAAK,aAAa,QAAQ,MAAM;AAChC,WAAK,aAAa,oBAAoB,eAAe,gBAAgB,yBAAyB,gBAAgB,eAAe;AAAA,IAC/H;AACA,eAAW,IAAI,MAAM,MAAM;AAC3B,QAAI,YAAY;AACd,WAAK,WAAW,SAAS,aAAa,YAAY,SAAS,IAAI,KAAK,OAAO,0BAA0B,aAAa,OAAO,SAAS,WAAW,IAAI,SAAS,EAAE,OAAO,KAAK;AAAA,IAC1K,OAAO;AACL,WAAK,WAAW,OAAO;AAAA,IACzB;AACA,SAAK,iBAAiB,SAAS,eAAe;AAC9C,QAAI,QAAQ,SAASC,OAAMC,IAAG;AAC5B,UAAI,cAAc,KAAK,SAASA,EAAC;AACjC,qBAAe,IAAI,WAAW;AAC9B,kBAAY,WAAW,aAAa,KAAK,OAAO;AAChD,UAAI,CAAC,kBAAkB;AACrB,oBAAY,aAAa,QAAQ,UAAU;AAAA,MAC7C;AACA,kBAAY,oBAAoB,WAAW,qBAAqB,IAAI,WAAW,CAAC;AAChF,kBAAY,oBAAoB,SAAS,mBAAmB,IAAI,WAAW,CAAC;AAC5E,UAAI,CAAC,cAAc;AACjB,oBAAY,iBAAiB,WAAW,aAAa;AACrD,6BAAqB,IAAI,aAAa,aAAa;AACnD,oBAAY,iBAAiB,SAAS,WAAW;AACjD,2BAAmB,IAAI,aAAa,WAAW;AAAA,MACjD;AACA,UAAI,cAAc,OAAO,MAAMA,EAAC,EAAE,WAAW,MAAM,eAAe;AAChE,mBAAW,WAAY;AACrB,iBAAO,CAAC,eAAe;AAAA,YACrB,GAAGA;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED,sBAAc;AACd,oBAAY,WAAW,OAAO;AAE9B,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,CAAC;AAAA,IACT;AAAA,EACF;AACA,YAAU,OAAO;AACjB,MAAI,UAAU;AAAA,IACZ,QAAQ,SAAS,OAAO,YAAY;AAClC,iBAAW,WAAY;AACrB,eAAO,4CAA4C,OAAO,SAAS,UAAU,CAAC;AAAA,MAChF,CAAC;AACD,gBAAU,UAAU;AAAA,IACtB;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,iBAAW,WAAY;AACrB,eAAO;AAAA,MACT,CAAC;AACD,yBAAmB,MAAM,OAAO,IAAI;AACpC,iBAAW,QAAQ,EAAE,IAAI;AACzB,kBAAY,QAAQ,EAAE,IAAI;AAAA,IAC5B;AAAA,EACF;AACA,cAAY,IAAI,MAAM,OAAO;AAC7B,SAAO;AACT;AAEA,IAAI,YAAY,CAAC,SAAS,kBAAkB,QAAQ,gBAAgB,iBAAiB,0BAA0B,gBAAgB,oBAAoB,mBAAmB,qBAAqB,2BAA2B,oBAAoB,yBAAyB,uBAAuB;AAyB1R,SAAS,QAAQ,MAAM,SAAS;AAC9B,MAAI,iBAAiB,IAAI,GAAG;AAC1B,WAAO;AAAA,MACL,QAAQ,SAAS,SAAS;AAAA,MAAC;AAAA,MAC3B,SAAS,SAAS,UAAU;AAAA,MAAC;AAAA,IAC/B;AAAA,EACF;AACA,kBAAgB,OAAO;AACvB,MAAI,cAAc,UAAU,MAAM,OAAO;AACzC,MAAI,eAAe,UAAU,MAAM,OAAO;AAC1C,SAAO;AAAA,IACL,QAAQ,SAAS,OAAO,YAAY;AAClC,sBAAgB,UAAU;AAC1B,kBAAY,OAAO,UAAU;AAC7B,mBAAa,OAAO,UAAU;AAAA,IAChC;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,kBAAY,QAAQ;AACpB,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACF;AAQA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,oCAAoC,UAAY,CAAC;AACpF;AACA,SAAS,gBAAgB,SAAS;AAEhC,MAAI,QAAQ,QAAQ;AAClB,UAAQ;AACR,UAAQ;AACR,UAAQ;AACR,UAAQ;AACR,UAAQ;AACR,MAAI,eAAe,QAAQ,cAC3B,mBAAmB,QAAQ;AAC3B,UAAQ;AACR,MAAI,oBAAoB,QAAQ;AAChC,UAAQ;AACR,UAAQ;AACR,UAAQ;AACR,UAAQ;AACR,MAAI,OAAO,yBAAyB,SAAS,SAAS;AAExD,MAAI,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG;AAChC,YAAQ,KAAK,uCAAuC,IAAI;AAAA,EAC1D;AACA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AACA,MAAI,oBAAoB,MAAM,KAAK,SAAU,MAAM;AACjD,WAAO,CAAC,CAAC,EAAE,eAAe,KAAK,MAAM,WAAW;AAAA,EAClD,CAAC;AACD,MAAI,mBAAmB;AACrB,UAAM,IAAI,MAAM,YAAY,OAAO,aAAa,sBAAsB,EAAE,OAAO,SAAS,iBAAiB,CAAC,CAAC;AAAA,EAC7G;AACA,MAAI,qBAAqB,CAAC,MAAM,QAAQ,iBAAiB,GAAG;AAC1D,UAAM,IAAI,MAAM,4DAA4D,OAAO,QAAQ,iBAAiB,GAAG,IAAI,EAAE,OAAO,SAAS,iBAAiB,CAAC,CAAC;AAAA,EAC1J;AACA,MAAI,gBAAgB,CAAC,MAAM,YAAY,GAAG;AACxC,UAAM,IAAI,MAAM,uDAAuD,OAAO,QAAQ,YAAY,GAAG,IAAI,EAAE,OAAO,SAAS,YAAY,CAAC,CAAC;AAAA,EAC3I;AACA,MAAI,oBAAoB,CAAC,MAAM,gBAAgB,GAAG;AAChD,UAAM,IAAI,MAAM,2DAA2D,OAAO,QAAQ,gBAAgB,GAAG,IAAI,EAAE,OAAO,SAAS,gBAAgB,CAAC,CAAC;AAAA,EACvJ;AACF;AACA,SAAS,MAAM,OAAO;AACpB,SAAO,CAAC,MAAM,KAAK,KAAK,SAAU,GAAG;AACnC,YAAQ,IAAI,OAAO;AAAA,EACrB,EAAE,WAAW,KAAK,CAAC;AACrB;AAEA,SAAS,YAAY,cAAc;AACjC,MAAI,OAAO;AACX,MAAI,OAAO,oBAAI,IAAI;AACnB,SAAO;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,SAAS,IAAI,QAAQ;AACxB,aAAO;AACP,YAAM,KAAK,IAAI,EAAE,QAAQ,SAAU,IAAI;AACrC,eAAO,GAAG,IAAI;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,IACA,WAAW,SAAS,UAAU,IAAI;AAChC,WAAK,IAAI,EAAE;AACX,SAAG,IAAI;AAAA,IACT;AAAA,IACA,aAAa,SAAS,YAAY,IAAI;AACpC,WAAK,QAAQ,EAAE,EAAE;AAAA,IACnB;AAAA,EACF;AACF;AAEA,IAAI,sBAAsB,YAAY,IAAI;AAC1C,SAAS,kBAAkB;AACzB,MAAIC,uBAAsB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC9F,SAAO;AAAA,IACL,cAAcA;AAAA,IACd,kBAAkB;AAAA,EACpB;AACF;AAWA,SAAS,eAAe,MAAM,SAAS;AACrC,MAAI,iBAAiB;AACrB,MAAI,OAAO,QAAQ,MAAM,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,gBAAgB,CAAC,CAAC;AAC9F,WAAS,iBAAiBA,sBAAqB;AAC7C,SAAK,OAAO,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,gBAAgBA,oBAAmB,CAAC,CAAC;AAAA,EACtG;AACA,sBAAoB,UAAU,gBAAgB;AAC9C,WAAS,SAAS,GAAG;AACnB,QAAI,iBAAiB,EAAE,OAAO,MAC5B,SAAS,eAAe,QACxB,UAAU,eAAe;AAE3B,QAAI,WAAW,QAAQ,YAAY,YAAY,SAAS,cAAc;AACpE,0BAAoB,IAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AACA,WAAS,SAAS,GAAG;AACnB,QAAI,SAAS,EAAE,OAAO,KAAK;AAE3B,QAAI,WAAW,QAAQ,SAAS;AAC9B,0BAAoB,IAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AACA,OAAK,iBAAiB,YAAY,QAAQ;AAC1C,OAAK,iBAAiB,YAAY,QAAQ;AAC1C,SAAO;AAAA,IACL,QAAQ,SAAS,OAAO,YAAY;AAClC,uBAAiB;AACjB,WAAK,OAAO,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,gBAAgB,oBAAoB,IAAI,CAAC,CAAC,CAAC;AAAA,IAC5G;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,WAAK,oBAAoB,YAAY,QAAQ;AAC7C,WAAK,oBAAoB,YAAY,QAAQ;AAC7C,0BAAoB,YAAY,gBAAgB;AAAA,IAClD;AAAA,EACF;AACF;AAOA,SAAS,WAAW,QAAQ;AAC1B,SAAO,aAAa,QAAQ,QAAQ;AACpC,WAAS,UAAU,GAAG;AAEpB,MAAE,eAAe;AACjB,wBAAoB,IAAI,KAAK;AAAA,EAC/B;AACA,WAAS,cAAc,GAAG;AACxB,QAAI,EAAE,QAAQ,WAAW,EAAE,QAAQ,IAAK,qBAAoB,IAAI,KAAK;AAAA,EACvE;AACA,sBAAoB,UAAU,SAAU,UAAU;AAChD,WAAO,WAAW,WAAW,IAAI;AACjC,WAAO,MAAM,SAAS,WAAW,SAAS;AAAA,EAC5C,CAAC;AACD,SAAO,iBAAiB,aAAa,SAAS;AAC9C,SAAO,iBAAiB,cAAc,SAAS;AAC/C,SAAO,iBAAiB,WAAW,aAAa;AAChD,SAAO;AAAA,IACL,QAAQ,SAAS,SAAS;AAAA,IAAC;AAAA,IAC3B,SAAS,SAAS,UAAU;AAC1B,aAAO,oBAAoB,aAAa,SAAS;AACjD,aAAO,oBAAoB,cAAc,SAAS;AAClD,aAAO,oBAAoB,WAAW,aAAa;AAAA,IACrD;AAAA,EACF;AACF;", "names": ["obj", "draggedEl", "printDebug", "multiScroller", "dz", "draggedElData", "_loop", "i", "isItemsDragDisabled"]}