
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.5.0
 * Query Engine version: 173f8d54f8d52e692c7e27e72a88314ec7aeff60
 */
Prisma.prismaVersion = {
  client: "6.5.0",
  engine: "173f8d54f8d52e692c7e27e72a88314ec7aeff60"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  email: 'email',
  name: 'name',
  session_id: 'session_id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  profilePhoto: 'profilePhoto',
  phone: 'phone',
  department: 'department',
  isActive: 'isActive',
  lastLogin: 'lastLogin'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  domain: 'domain',
  logo: 'logo',
  website: 'website',
  industry: 'industry',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isActive: 'isActive'
};

exports.Prisma.UserOrganizationScalarFieldEnum = {
  id: 'id',
  role: 'role',
  joinedAt: 'joinedAt',
  userId: 'userId',
  organizationId: 'organizationId'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  industry: 'industry',
  website: 'website',
  phone: 'phone',
  street: 'street',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  latitude: 'latitude',
  longitude: 'longitude',
  annualRevenue: 'annualRevenue',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  numberOfEmployees: 'numberOfEmployees',
  accountOwnership: 'accountOwnership',
  tickerSymbol: 'tickerSymbol',
  rating: 'rating',
  sicCode: 'sicCode',
  isActive: 'isActive',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  deletedById: 'deletedById',
  closedAt: 'closedAt',
  closureReason: 'closureReason',
  ownerId: 'ownerId',
  organizationId: 'organizationId'
};

exports.Prisma.ContactScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  title: 'title',
  department: 'department',
  street: 'street',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  latitude: 'latitude',
  longitude: 'longitude',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ownerId: 'ownerId',
  organizationId: 'organizationId'
};

exports.Prisma.LeadScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  company: 'company',
  title: 'title',
  status: 'status',
  leadSource: 'leadSource',
  industry: 'industry',
  rating: 'rating',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ownerId: 'ownerId',
  organizationId: 'organizationId',
  isConverted: 'isConverted',
  convertedAt: 'convertedAt',
  convertedAccountId: 'convertedAccountId',
  convertedContactId: 'convertedContactId',
  convertedOpportunityId: 'convertedOpportunityId',
  contactId: 'contactId'
};

exports.Prisma.OpportunityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  amount: 'amount',
  status: 'status',
  closeDate: 'closeDate',
  probability: 'probability',
  type: 'type',
  nextStep: 'nextStep',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  ownerId: 'ownerId',
  organizationId: 'organizationId',
  stage: 'stage',
  expectedRevenue: 'expectedRevenue',
  forecastCategory: 'forecastCategory',
  leadSource: 'leadSource'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  subject: 'subject',
  status: 'status',
  priority: 'priority',
  dueDate: 'dueDate',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ownerId: 'ownerId',
  createdById: 'createdById',
  accountId: 'accountId',
  contactId: 'contactId',
  leadId: 'leadId',
  opportunityId: 'opportunityId',
  caseId: 'caseId',
  organizationId: 'organizationId'
};

exports.Prisma.EventScalarFieldEnum = {
  id: 'id',
  subject: 'subject',
  location: 'location',
  startDate: 'startDate',
  endDate: 'endDate',
  allDayEvent: 'allDayEvent',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ownerId: 'ownerId',
  createdById: 'createdById',
  accountId: 'accountId',
  contactId: 'contactId',
  leadId: 'leadId',
  opportunityId: 'opportunityId',
  organizationId: 'organizationId'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  description: 'description',
  unitPrice: 'unitPrice',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  organizationId: 'organizationId'
};

exports.Prisma.ProductOpportunityScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  discount: 'discount',
  productId: 'productId',
  opportunityId: 'opportunityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CaseScalarFieldEnum = {
  id: 'id',
  caseNumber: 'caseNumber',
  subject: 'subject',
  status: 'status',
  description: 'description',
  priority: 'priority',
  origin: 'origin',
  type: 'type',
  reason: 'reason',
  dueDate: 'dueDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  closedAt: 'closedAt',
  ownerId: 'ownerId',
  accountId: 'accountId',
  contactId: 'contactId',
  organizationId: 'organizationId'
};

exports.Prisma.SolutionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  status: 'status',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  organizationId: 'organizationId'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  body: 'body',
  isPrivate: 'isPrivate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  authorId: 'authorId',
  organizationId: 'organizationId',
  caseId: 'caseId',
  opportunityId: 'opportunityId',
  leadId: 'leadId',
  accountId: 'accountId',
  contactId: 'contactId',
  taskId: 'taskId'
};

exports.Prisma.QuoteScalarFieldEnum = {
  id: 'id',
  quoteNumber: 'quoteNumber',
  name: 'name',
  status: 'status',
  description: 'description',
  expirationDate: 'expirationDate',
  subtotal: 'subtotal',
  discountAmount: 'discountAmount',
  taxAmount: 'taxAmount',
  grandTotal: 'grandTotal',
  billingStreet: 'billingStreet',
  billingCity: 'billingCity',
  billingState: 'billingState',
  billingPostalCode: 'billingPostalCode',
  billingCountry: 'billingCountry',
  shippingStreet: 'shippingStreet',
  shippingCity: 'shippingCity',
  shippingState: 'shippingState',
  shippingPostalCode: 'shippingPostalCode',
  shippingCountry: 'shippingCountry',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  preparedById: 'preparedById',
  accountId: 'accountId',
  opportunityId: 'opportunityId',
  contactId: 'contactId',
  organizationId: 'organizationId'
};

exports.Prisma.QuoteLineItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  listPrice: 'listPrice',
  unitPrice: 'unitPrice',
  discount: 'discount',
  totalPrice: 'totalPrice',
  description: 'description',
  quoteId: 'quoteId',
  productId: 'productId'
};

exports.Prisma.AccountContactRelationshipScalarFieldEnum = {
  id: 'id',
  role: 'role',
  isPrimary: 'isPrimary',
  startDate: 'startDate',
  endDate: 'endDate',
  description: 'description',
  accountId: 'accountId',
  contactId: 'contactId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  timestamp: 'timestamp',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  description: 'description',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  organizationId: 'organizationId'
};

exports.Prisma.BoardScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  ownerId: 'ownerId',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BoardMemberScalarFieldEnum = {
  id: 'id',
  boardId: 'boardId',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt'
};

exports.Prisma.BoardColumnScalarFieldEnum = {
  id: 'id',
  name: 'name',
  order: 'order',
  boardId: 'boardId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BoardTaskScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  order: 'order',
  columnId: 'columnId',
  assigneeId: 'assigneeId',
  dueDate: 'dueDate',
  completed: 'completed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BoardTaskActivityScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  authorId: 'authorId',
  type: 'type',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.BlogPostScalarFieldEnum = {
  id: 'id',
  title: 'title',
  seoTitle: 'seoTitle',
  seoDescription: 'seoDescription',
  excerpt: 'excerpt',
  slug: 'slug',
  draft: 'draft',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.BlogContentBlockScalarFieldEnum = {
  id: 'id',
  blogId: 'blogId',
  type: 'type',
  content: 'content',
  displayOrder: 'displayOrder',
  draft: 'draft',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NewsletterSubscriberScalarFieldEnum = {
  id: 'id',
  email: 'email',
  isActive: 'isActive',
  subscribedAt: 'subscribedAt',
  unsubscribedAt: 'unsubscribedAt',
  confirmationToken: 'confirmationToken',
  isConfirmed: 'isConfirmed',
  confirmedAt: 'confirmedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.ContactSubmissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  message: 'message',
  reason: 'reason',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  referrer: 'referrer',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER'
};

exports.LeadStatus = exports.$Enums.LeadStatus = {
  NEW: 'NEW',
  PENDING: 'PENDING',
  CONTACTED: 'CONTACTED',
  QUALIFIED: 'QUALIFIED',
  UNQUALIFIED: 'UNQUALIFIED',
  CONVERTED: 'CONVERTED'
};

exports.LeadSource = exports.$Enums.LeadSource = {
  WEB: 'WEB',
  PHONE_INQUIRY: 'PHONE_INQUIRY',
  PARTNER_REFERRAL: 'PARTNER_REFERRAL',
  COLD_CALL: 'COLD_CALL',
  TRADE_SHOW: 'TRADE_SHOW',
  EMPLOYEE_REFERRAL: 'EMPLOYEE_REFERRAL',
  ADVERTISEMENT: 'ADVERTISEMENT',
  OTHER: 'OTHER'
};

exports.OpportunityStatus = exports.$Enums.OpportunityStatus = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  IN_PROGRESS: 'IN_PROGRESS'
};

exports.OpportunityStage = exports.$Enums.OpportunityStage = {
  PROSPECTING: 'PROSPECTING',
  QUALIFICATION: 'QUALIFICATION',
  PROPOSAL: 'PROPOSAL',
  NEGOTIATION: 'NEGOTIATION',
  CLOSED_WON: 'CLOSED_WON',
  CLOSED_LOST: 'CLOSED_LOST'
};

exports.CaseStatus = exports.$Enums.CaseStatus = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  CLOSED: 'CLOSED'
};

exports.QuoteStatus = exports.$Enums.QuoteStatus = {
  DRAFT: 'DRAFT',
  NEEDS_REVIEW: 'NEEDS_REVIEW',
  IN_REVIEW: 'IN_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PRESENTED: 'PRESENTED',
  ACCEPTED: 'ACCEPTED'
};

exports.AuditAction = exports.$Enums.AuditAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  EXPORT: 'EXPORT',
  IMPORT: 'IMPORT',
  VIEW: 'VIEW',
  OTHER: 'OTHER'
};

exports.ContentBlockType = exports.$Enums.ContentBlockType = {
  MARKDOWN: 'MARKDOWN',
  CODE: 'CODE',
  IMAGE: 'IMAGE'
};

exports.Prisma.ModelName = {
  User: 'User',
  Organization: 'Organization',
  UserOrganization: 'UserOrganization',
  Account: 'Account',
  Contact: 'Contact',
  Lead: 'Lead',
  Opportunity: 'Opportunity',
  Task: 'Task',
  Event: 'Event',
  Product: 'Product',
  ProductOpportunity: 'ProductOpportunity',
  Case: 'Case',
  Solution: 'Solution',
  Comment: 'Comment',
  Quote: 'Quote',
  QuoteLineItem: 'QuoteLineItem',
  AccountContactRelationship: 'AccountContactRelationship',
  AuditLog: 'AuditLog',
  Board: 'Board',
  BoardMember: 'BoardMember',
  BoardColumn: 'BoardColumn',
  BoardTask: 'BoardTask',
  BoardTaskActivity: 'BoardTaskActivity',
  BlogPost: 'BlogPost',
  BlogContentBlock: 'BlogContentBlock',
  NewsletterSubscriber: 'NewsletterSubscriber',
  ContactSubmission: 'ContactSubmission'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
