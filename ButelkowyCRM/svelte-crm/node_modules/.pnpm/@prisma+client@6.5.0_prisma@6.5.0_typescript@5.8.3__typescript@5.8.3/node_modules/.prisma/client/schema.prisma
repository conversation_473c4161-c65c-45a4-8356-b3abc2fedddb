generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  USER
}

model User {
  id                String              @id @default(uuid())
  user_id           String              @unique
  email             String              @unique
  name              String?
  session_id        String?             @unique
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  profilePhoto      String?
  phone             String?
  department        String?
  isActive          Boolean             @default(true)
  lastLogin         DateTime?
  accounts          Account[]
  deletedAccounts   Account[]           @relation("DeletedAccounts")
  contacts          Contact[]
  leads             Lead[]
  opportunities     Opportunity[]
  tasks             Task[] // Tasks created by user
  events            Event[] // Events created by user
  ownedTasks        Task[]              @relation("TaskOwner")
  ownedEvents       Event[]             @relation("EventOwner")
  cases             Case[]
  comments          Comment[]
  preparedQuotes    Quote[]             @relation("QuotePreparer")
  organizations     UserOrganization[]
  AuditLog          AuditLog[]
  Board             Board[]
  BoardMember       BoardMember[]
  BoardTask         BoardTask[]
  BoardTaskActivity BoardTaskActivity[]
  BlogPost          BlogPost[]
}

model Organization {
  id          String   @id @default(uuid())
  name        String
  domain      String?
  logo        String?
  website     String?
  industry    String?
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  isActive    Boolean  @default(true)

  // Relationships
  users         UserOrganization[]
  accounts      Account[]
  contacts      Contact[]
  leads         Lead[]
  opportunities Opportunity[]
  tasks         Task[]
  events        Event[]
  cases         Case[]
  comments      Comment[]
  quotes        Quote[]
  products      Product[]
  solutions     Solution[]
  AuditLog      AuditLog[]
  Board         Board[]
}

model UserOrganization {
  id       String   @id @default(uuid())
  role     UserRole @default(USER)
  // isPrimary Boolean  @default(false)
  joinedAt DateTime @default(now())

  // Relationships
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@unique([userId, organizationId])
  @@index([userId])
  @@index([organizationId])
}

model Account {
  id                String                       @id @default(uuid())
  name              String
  type              String? // customer, partner, prospect
  industry          String?
  website           String?
  phone             String?
  street            String?
  city              String?
  state             String?
  postalCode        String?
  country           String?
  latitude          Float?
  longitude         Float?
  annualRevenue     Float?
  description       String?
  createdAt         DateTime                     @default(now())
  updatedAt         DateTime                     @updatedAt
  numberOfEmployees Int?
  accountOwnership  String? // Public, Private, Subsidiary, etc.
  tickerSymbol      String?
  rating            String? // Hot, Warm, Cold
  sicCode           String? // Standard Industrial Classification code
  isActive          Boolean                      @default(true)
  isDeleted         Boolean                      @default(false)
  deletedAt         DateTime?
  deletedById       String?
  deletedBy         User?                        @relation("DeletedAccounts", fields: [deletedById], references: [id])
  closedAt          DateTime?
  closureReason     String?
  owner             User                         @relation(fields: [ownerId], references: [id])
  ownerId           String
  organization      Organization                 @relation(fields: [organizationId], references: [id])
  organizationId    String
  opportunities     Opportunity[]
  tasks             Task[]
  events            Event[]
  cases             Case[]
  comments          Comment[]
  quotes            Quote[]                      @relation("AccountQuotes")
  relatedContacts   AccountContactRelationship[]
}

model Contact {
  id              String                       @id @default(uuid())
  firstName       String
  lastName        String
  email           String?
  phone           String?
  title           String?
  department      String?
  street          String?
  city            String?
  state           String?
  postalCode      String?
  country         String?
  latitude        Float?
  longitude       Float?
  description     String?
  createdAt       DateTime                     @default(now())
  updatedAt       DateTime                     @updatedAt
  owner           User                         @relation(fields: [ownerId], references: [id])
  ownerId         String
  organization    Organization?                @relation(fields: [organizationId], references: [id])
  organizationId  String?
  tasks           Task[]
  events          Event[]
  opportunities   Opportunity[]
  cases           Case[]
  leads           Lead[] // Keep this relation for historical tracking only
  comments        Comment[]
  quotes          Quote[]                      @relation("ContactQuotes")
  relatedAccounts AccountContactRelationship[]
}

enum LeadSource {
  WEB
  PHONE_INQUIRY
  PARTNER_REFERRAL
  COLD_CALL
  TRADE_SHOW
  EMPLOYEE_REFERRAL
  ADVERTISEMENT
  OTHER
}

model Lead {
  id                     String       @id @default(uuid())
  firstName              String
  lastName               String
  email                  String?
  phone                  String?
  company                String?
  title                  String?
  status                 LeadStatus   @default(PENDING)
  leadSource             LeadSource?
  industry               String?
  rating                 String? // Hot, Warm, Cold
  description            String?
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  owner                  User         @relation(fields: [ownerId], references: [id])
  ownerId                String
  organization           Organization @relation(fields: [organizationId], references: [id])
  organizationId         String
  tasks                  Task[]
  events                 Event[]
  isConverted            Boolean      @default(false)
  convertedAt            DateTime?
  comments               Comment[]
  convertedAccountId     String?
  convertedContactId     String?
  convertedOpportunityId String?
  contact                Contact?     @relation(fields: [contactId], references: [id])
  contactId              String?
}

enum LeadStatus {
  NEW
  PENDING
  CONTACTED
  QUALIFIED
  UNQUALIFIED
  CONVERTED
}

enum OpportunityStatus {
  SUCCESS
  FAILED
  IN_PROGRESS
}

model Opportunity {
  id                   String               @id @default(uuid())
  name                 String
  amount               Float?
  status               OpportunityStatus?
  closeDate            DateTime?
  probability          Float? // Percentage chance to close (0-100)
  type                 String? // New Business, Existing Business, etc.
  nextStep             String?
  description          String?
  createdAt            DateTime             @default(now())
  updatedAt            DateTime             @updatedAt
  account              Account              @relation(fields: [accountId], references: [id])
  accountId            String
  owner                User                 @relation(fields: [ownerId], references: [id])
  ownerId              String
  organization         Organization         @relation(fields: [organizationId], references: [id])
  organizationId       String
  contacts             Contact[]
  tasks                Task[]
  events               Event[]
  comments             Comment[]
  quotes               Quote[]              @relation("OpportunityQuotes")
  stage                OpportunityStage
  expectedRevenue      Float?
  forecastCategory     String? // Pipeline, Best Case, Commit, Closed
  leadSource           String?
  productOpportunities ProductOpportunity[] @relation("OpportunityProductOpportunities")
}

enum OpportunityStage {
  PROSPECTING
  QUALIFICATION
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

model Task {
  id             String       @id @default(uuid())
  subject        String
  status         String       @default("Not Started") // Not Started, In Progress, Completed, etc.
  priority       String       @default("Normal") // High, Normal, Low
  dueDate        DateTime?
  description    String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  owner          User         @relation("TaskOwner", fields: [ownerId], references: [id])
  ownerId        String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String
  account        Account?     @relation(fields: [accountId], references: [id])
  accountId      String?
  contact        Contact?     @relation(fields: [contactId], references: [id])
  contactId      String?
  lead           Lead?        @relation(fields: [leadId], references: [id])
  leadId         String?
  opportunity    Opportunity? @relation(fields: [opportunityId], references: [id])
  opportunityId  String?
  case           Case?        @relation(fields: [caseId], references: [id])
  caseId         String?
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  comments       Comment[]    @relation("TaskComments")
}

model Event {
  id             String       @id @default(uuid())
  subject        String
  location       String?
  startDate      DateTime
  endDate        DateTime
  allDayEvent    Boolean      @default(false)
  description    String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  owner          User         @relation("EventOwner", fields: [ownerId], references: [id])
  ownerId        String
  createdBy      User         @relation(fields: [createdById], references: [id])
  createdById    String
  account        Account?     @relation(fields: [accountId], references: [id])
  accountId      String?
  contact        Contact?     @relation(fields: [contactId], references: [id])
  contactId      String?
  lead           Lead?        @relation(fields: [leadId], references: [id])
  leadId         String?
  opportunity    Opportunity? @relation(fields: [opportunityId], references: [id])
  opportunityId  String?
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
}

model Product {
  id             String               @id @default(uuid())
  name           String
  code           String               @unique
  description    String?
  unitPrice      Float
  active         Boolean              @default(true)
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  opportunities  ProductOpportunity[]
  QuoteLineItem  QuoteLineItem[]      @relation("ProductLineItems")
  organization   Organization         @relation(fields: [organizationId], references: [id])
  organizationId String
}

model ProductOpportunity {
  id            String      @id @default(uuid())
  quantity      Float
  unitPrice     Float
  totalPrice    Float
  discount      Float       @default(0)
  product       Product     @relation(fields: [productId], references: [id])
  productId     String
  opportunity   Opportunity @relation("OpportunityProductOpportunities", fields: [opportunityId], references: [id])
  opportunityId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

model Case {
  id             String       @id @default(uuid())
  caseNumber     String       @unique @default(uuid())
  subject        String
  status         CaseStatus   @default(OPEN)
  description    String?
  priority       String       @default("Medium") // High, Medium, Low
  origin         String? // Email, Web, Phone
  type           String? // Problem, Feature Request, Question
  reason         String? // Example: "User didn't attend training, Complex functionality"
  dueDate        DateTime?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  closedAt       DateTime?
  owner          User         @relation(fields: [ownerId], references: [id])
  ownerId        String
  account        Account      @relation(fields: [accountId], references: [id])
  accountId      String
  contact        Contact?     @relation(fields: [contactId], references: [id])
  contactId      String?
  tasks          Task[]
  comments       Comment[]
  solutions      Solution[]
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
}

enum CaseStatus {
  OPEN
  IN_PROGRESS
  CLOSED
}

model Solution {
  id             String       @id @default(uuid())
  title          String
  description    String
  status         String       @default("Draft") // Draft, Reviewed, Approved
  isPublished    Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  cases          Case[]
}

model Comment {
  id             String       @id @default(uuid())
  body           String
  isPrivate      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  author         User         @relation(fields: [authorId], references: [id])
  authorId       String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  case           Case?        @relation(fields: [caseId], references: [id])
  caseId         String?
  opportunity    Opportunity? @relation(fields: [opportunityId], references: [id])
  opportunityId  String?
  lead           Lead?        @relation(fields: [leadId], references: [id])
  leadId         String?
  account        Account?     @relation(fields: [accountId], references: [id])
  accountId      String?
  contact        Contact?     @relation(fields: [contactId], references: [id])
  contactId      String?
  task           Task?        @relation("TaskComments", fields: [taskId], references: [id])
  taskId         String?
}

model Quote {
  id             String      @id @default(uuid())
  quoteNumber    String      @unique
  name           String
  status         QuoteStatus @default(DRAFT)
  description    String?     @db.Text
  expirationDate DateTime?
  subtotal       Decimal     @default(0.00) @db.Decimal(18, 2)
  discountAmount Decimal     @default(0.00) @db.Decimal(18, 2)
  taxAmount      Decimal     @default(0.00) @db.Decimal(18, 2)
  grandTotal     Decimal     @default(0.00) @db.Decimal(18, 2)

  // Billing address fields
  billingStreet     String?
  billingCity       String?
  billingState      String?
  billingPostalCode String?
  billingCountry    String?

  // Shipping address fields
  shippingStreet     String?
  shippingCity       String?
  shippingState      String?
  shippingPostalCode String?
  shippingCountry    String?

  // Remove billingAddressId, shippingAddressId and address relations
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  preparedById   String
  preparedBy     User            @relation("QuotePreparer", fields: [preparedById], references: [id], onDelete: Restrict)
  accountId      String
  account        Account         @relation("AccountQuotes", fields: [accountId], references: [id], onDelete: Restrict)
  opportunityId  String?
  opportunity    Opportunity?    @relation("OpportunityQuotes", fields: [opportunityId], references: [id], onDelete: SetNull)
  contactId      String?
  contact        Contact?        @relation("ContactQuotes", fields: [contactId], references: [id], onDelete: SetNull)
  lineItems      QuoteLineItem[] @relation("QuoteLineItems")
  organization   Organization    @relation(fields: [organizationId], references: [id])
  organizationId String

  @@index([status])
  @@index([accountId])
  @@index([opportunityId])
  @@index([preparedById])
  @@index([expirationDate])
  @@index([organizationId])
}

model QuoteLineItem {
  id          String  @id @default(uuid())
  quantity    Int     @default(1)
  listPrice   Decimal @db.Decimal(18, 2)
  unitPrice   Decimal @db.Decimal(18, 2)
  discount    Decimal @default(0.00) @db.Decimal(18, 2)
  totalPrice  Decimal @db.Decimal(18, 2)
  description String? @db.Text

  // Relationships
  quoteId   String
  quote     Quote   @relation("QuoteLineItems", fields: [quoteId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation("ProductLineItems", fields: [productId], references: [id], onDelete: Restrict)

  @@index([quoteId])
  @@index([productId])
}

enum QuoteStatus {
  DRAFT
  NEEDS_REVIEW
  IN_REVIEW
  APPROVED
  REJECTED
  PRESENTED
  ACCEPTED
}

model AccountContactRelationship {
  id          String    @id @default(uuid())
  role        String? // Decision Maker, Influencer, etc.
  isPrimary   Boolean   @default(false)
  startDate   DateTime  @default(now())
  endDate     DateTime?
  description String?

  // Relationships
  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId String
  contact   Contact @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([contactId, accountId])
  @@index([contactId])
  @@index([accountId])
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  EXPORT
  IMPORT
  VIEW
  OTHER
}

model AuditLog {
  id          String      @id @default(uuid())
  timestamp   DateTime    @default(now())
  action      AuditAction
  entityType  String // Account, Contact, Opportunity, etc.
  entityId    String? // The ID of the affected entity
  description String? // Human-readable description of the action
  oldValues   Json? // Previous state (for updates/deletes)
  newValues   Json? // New state (for creates/updates)
  ipAddress   String? // IP address of the user
  userAgent   String? // Browser/client information

  // Relationships
  user           User         @relation(fields: [userId], references: [id])
  userId         String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String

  @@index([timestamp])
  @@index([entityType, entityId])
  @@index([userId])
  @@index([organizationId])
  @@index([action])
}

// --- Trello-like Board Models ---
model Board {
  id             String        @id @default(uuid())
  name           String
  description    String?
  owner          User          @relation(fields: [ownerId], references: [id])
  ownerId        String
  organization   Organization  @relation(fields: [organizationId], references: [id])
  organizationId String
  members        BoardMember[]
  columns        BoardColumn[]
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
}

model BoardMember {
  id        String   @id @default(uuid())
  board     Board    @relation(fields: [boardId], references: [id])
  boardId   String
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  role      String // e.g. "admin", "member"
  createdAt DateTime @default(now())

  @@unique([boardId, userId])
}

model BoardColumn {
  id        String      @id @default(uuid())
  name      String
  order     Int
  board     Board       @relation(fields: [boardId], references: [id])
  boardId   String
  tasks     BoardTask[]
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
}

model BoardTask {
  id          String              @id @default(uuid())
  title       String
  description String?
  order       Int
  column      BoardColumn         @relation(fields: [columnId], references: [id])
  columnId    String
  assignee    User?               @relation(fields: [assigneeId], references: [id])
  assigneeId  String?
  dueDate     DateTime?
  completed   Boolean             @default(false)
  activities  BoardTaskActivity[]
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
}

model BoardTaskActivity {
  id        String    @id @default(uuid())
  task      BoardTask @relation(fields: [taskId], references: [id])
  taskId    String
  author    User      @relation(fields: [authorId], references: [id])
  authorId  String
  type      String // e.g. "comment", "status_change"
  content   String
  createdAt DateTime  @default(now())
}

model BlogPost {
  id             String             @id @default(cuid())
  title          String
  seoTitle       String
  seoDescription String
  excerpt        String
  slug           String             @unique
  draft          Boolean            @default(false)
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  contentBlocks  BlogContentBlock[]
  User           User?              @relation(fields: [userId], references: [id])
  userId         String?
}

model BlogContentBlock {
  id           String           @id @default(cuid())
  blog         BlogPost         @relation(fields: [blogId], references: [id])
  blogId       String
  type         ContentBlockType
  content      String
  displayOrder Int
  draft        Boolean          @default(false)
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
}

enum ContentBlockType {
  MARKDOWN
  CODE
  IMAGE
}

model NewsletterSubscriber {
  id                String    @id @default(uuid())
  email             String    @unique
  isActive          Boolean   @default(true)
  subscribedAt      DateTime  @default(now())
  unsubscribedAt    DateTime?
  confirmationToken String?   @unique
  isConfirmed       Boolean   @default(false)
  confirmedAt       DateTime?
  ipAddress         String?
  userAgent         String?

  @@index([email])
  @@index([isActive])
  @@index([subscribedAt])
}

model ContactSubmission {
  id      String @id @default(uuid())
  name    String
  email   String
  message String
  reason  String

  // Tracking fields
  ipAddress String?
  userAgent String?
  referrer  String?

  createdAt DateTime @default(now())
}
