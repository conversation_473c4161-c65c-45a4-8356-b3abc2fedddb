#!/usr/bin/env node

/**
 * Weaviate Setup Script for ButelkowyCRM
 * 
 * This script helps you set up Weaviate integration for your HVAC CRM system.
 * Run with: node scripts/setup-weaviate.js
 */

import { execSync } from 'child_process';
import { writeFileSync, existsSync, readFileSync } from 'fs';
import { join } from 'path';

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`);
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  // Check if we're in the right directory
  if (!existsSync('package.json')) {
    log('❌ Error: package.json not found. Please run this script from the project root.', 'red');
    process.exit(1);
  }

  // Check if <PERSON>risma is installed
  try {
    const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
    if (!packageJson.dependencies?.['@prisma/client'] && !packageJson.devDependencies?.['prisma']) {
      log('❌ Error: Prisma not found in package.json. This script requires Prisma to be installed.', 'red');
      process.exit(1);
    }
  } catch (error) {
    log('❌ Error reading package.json', 'red');
    process.exit(1);
  }

  log('✅ Prerequisites check passed', 'green');
}

function installDependencies() {
  log('📦 Installing Weaviate dependencies...', 'blue');
  
  try {
    // Install Weaviate client
    execSync('pnpm add weaviate-ts-client', { stdio: 'inherit' });
    
    // Install AI/ML utilities
    execSync('pnpm add openai @tensorflow/tfjs-node', { stdio: 'inherit' });
    
    log('✅ Dependencies installed successfully', 'green');
  } catch (error) {
    log('❌ Error installing dependencies:', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

function setupEnvironmentVariables() {
  log('⚙️ Setting up environment variables...', 'blue');
  
  const envPath = '.env';
  let envContent = '';
  
  // Read existing .env file if it exists
  if (existsSync(envPath)) {
    envContent = readFileSync(envPath, 'utf8');
  }
  
  // Add Weaviate configuration if not present
  const weaviateVars = [
    '# Weaviate Configuration',
    'WEAVIATE_HOST=localhost:8080',
    'WEAVIATE_SCHEME=http',
    'OPENAI_API_KEY=your_openai_api_key_here',
    '',
  ];
  
  let needsUpdate = false;
  
  for (const varLine of weaviateVars) {
    if (varLine.startsWith('#') || varLine === '') continue;
    
    const [varName] = varLine.split('=');
    if (!envContent.includes(varName)) {
      needsUpdate = true;
      break;
    }
  }
  
  if (needsUpdate) {
    envContent += '\n' + weaviateVars.join('\n');
    writeFileSync(envPath, envContent);
    log('✅ Environment variables added to .env', 'green');
    log('⚠️  Please update OPENAI_API_KEY with your actual API key', 'yellow');
  } else {
    log('ℹ️  Environment variables already configured', 'cyan');
  }
}

function createWeaviateClient() {
  log('🔧 Creating Weaviate client...', 'blue');
  
  const clientPath = 'src/lib/weaviate.js';
  
  if (existsSync(clientPath)) {
    log('ℹ️  Weaviate client already exists', 'cyan');
    return;
  }
  
  const clientCode = `import weaviate from 'weaviate-ts-client';

const client = weaviate.client({
  scheme: process.env.WEAVIATE_SCHEME || 'http',
  host: process.env.WEAVIATE_HOST || 'localhost:8080',
  headers: {
    'X-OpenAI-Api-Key': process.env.OPENAI_API_KEY,
  },
});

// Test connection
export async function testConnection() {
  try {
    const result = await client.misc.metaGetter().do();
    console.log('✅ Weaviate connected:', result.hostname);
    return true;
  } catch (error) {
    console.error('❌ Weaviate connection failed:', error);
    return false;
  }
}

export default client;
`;
  
  writeFileSync(clientPath, clientCode);
  log('✅ Weaviate client created at src/lib/weaviate.js', 'green');
}

function createDockerCompose() {
  log('🐳 Creating Docker Compose for Weaviate...', 'blue');
  
  const dockerComposePath = 'docker-compose.weaviate.yml';
  
  if (existsSync(dockerComposePath)) {
    log('ℹ️  Docker Compose file already exists', 'cyan');
    return;
  }
  
  const dockerComposeContent = `version: '3.4'
services:
  weaviate:
    command:
      - --host
      - 0.0.0.0
      - --port
      - '8080'
      - --scheme
      - http
    image: semitechnologies/weaviate:1.22.4
    ports:
      - "8080:8080"
    restart: on-failure:0
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'text2vec-openai'
      ENABLE_MODULES: 'text2vec-openai,generative-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate

volumes:
  weaviate_data:
`;
  
  writeFileSync(dockerComposePath, dockerComposeContent);
  log('✅ Docker Compose created at docker-compose.weaviate.yml', 'green');
}

function createInitScript() {
  log('📝 Creating initialization script...', 'blue');
  
  const initScriptPath = 'scripts/init-weaviate-schema.js';
  
  if (existsSync(initScriptPath)) {
    log('ℹ️  Initialization script already exists', 'cyan');
    return;
  }
  
  const initScriptContent = `#!/usr/bin/env node

/**
 * Initialize Weaviate Schema for HVAC CRM
 * Run with: node scripts/init-weaviate-schema.js
 */

import weaviate, { testConnection } from '../src/lib/weaviate.js';

const HVAC_SCHEMA = {
  classes: [
    {
      class: 'HVACCustomer',
      description: 'HVAC customer profiles with semantic search capabilities',
      properties: [
        { name: 'customerId', dataType: ['string'], description: 'Prisma customer ID' },
        { name: 'fullName', dataType: ['string'], description: 'Customer full name' },
        { name: 'email', dataType: ['string'], description: 'Customer email address' },
        { name: 'company', dataType: ['string'], description: 'Company name' },
        { name: 'description', dataType: ['text'], description: 'Customer description and notes' },
        { name: 'location', dataType: ['geoCoordinates'], description: 'Customer location' },
      ],
      vectorizer: 'text2vec-openai',
      moduleConfig: {
        'text2vec-openai': {
          model: 'ada',
          modelVersion: '002',
          type: 'text',
        },
      },
    },
  ],
};

async function initializeSchema() {
  console.log('🚀 Initializing Weaviate schema...');
  
  // Test connection first
  const connected = await testConnection();
  if (!connected) {
    console.error('❌ Cannot connect to Weaviate. Please start Weaviate first.');
    console.log('💡 Run: docker-compose -f docker-compose.weaviate.yml up -d');
    process.exit(1);
  }
  
  try {
    // Check existing schema
    const existingSchema = await weaviate.schema.getter().do();
    
    for (const classConfig of HVAC_SCHEMA.classes) {
      const classExists = existingSchema.classes?.some(c => c.class === classConfig.class);
      
      if (!classExists) {
        await weaviate.schema.classCreator().withClass(classConfig).do();
        console.log(\`✅ Created Weaviate class: \${classConfig.class}\`);
      } else {
        console.log(\`ℹ️  Class already exists: \${classConfig.class}\`);
      }
    }
    
    console.log('🎉 Schema initialization completed!');
  } catch (error) {
    console.error('❌ Schema initialization failed:', error);
    process.exit(1);
  }
}

initializeSchema();
`;
  
  writeFileSync(initScriptPath, initScriptContent);
  log('✅ Initialization script created at scripts/init-weaviate-schema.js', 'green');
}

function printNextSteps() {
  log('\n🎉 Weaviate setup completed successfully!', 'green');
  log('\n📋 Next Steps:', 'bright');
  log('1. Update your OPENAI_API_KEY in .env file', 'yellow');
  log('2. Start Weaviate: docker-compose -f docker-compose.weaviate.yml up -d', 'cyan');
  log('3. Initialize schema: node scripts/init-weaviate-schema.js', 'cyan');
  log('4. Review the implementation guides:', 'cyan');
  log('   - PRISMA_WEAVIATE_MIGRATION_PLAN.md', 'cyan');
  log('   - WEAVIATE_IMPLEMENTATION_GUIDE.md', 'cyan');
  log('\n🚀 You\'re ready to implement hybrid Prisma + Weaviate architecture!', 'green');
}

// Main execution
async function main() {
  log('🚀 ButelkowyCRM Weaviate Setup', 'bright');
  log('=====================================', 'bright');
  
  try {
    checkPrerequisites();
    installDependencies();
    setupEnvironmentVariables();
    createWeaviateClient();
    createDockerCompose();
    createInitScript();
    printNextSteps();
  } catch (error) {
    log('❌ Setup failed:', 'red');
    console.error(error);
    process.exit(1);
  }
}

main();
