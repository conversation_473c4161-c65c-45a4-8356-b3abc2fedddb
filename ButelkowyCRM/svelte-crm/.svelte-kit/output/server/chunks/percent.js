import { E as spread_props, C as pop, A as push } from "./index2.js";
import { I as Icon } from "./Icon.js";
function Percent($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "line",
      {
        "x1": "19",
        "x2": "5",
        "y1": "5",
        "y2": "19"
      }
    ],
    [
      "circle",
      { "cx": "6.5", "cy": "6.5", "r": "2.5" }
    ],
    [
      "circle",
      { "cx": "17.5", "cy": "17.5", "r": "2.5" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "percent" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
export {
  Percent as P
};
