import { p as prisma } from "./prisma.js";
import { r as redirect } from "./index.js";
async function handle({ event, resolve }) {
  const sessionId = await event.cookies.get("session");
  let user = false;
  if (sessionId && sessionId != "") {
    user = await prisma.user.findFirst({
      where: {
        session_id: sessionId
      }
    });
  }
  if (user) {
    event.locals.user = user;
    const orgId = event.cookies.get("org");
    if (orgId) {
      const userOrg = await prisma.userOrganization.findFirst({
        where: {
          userId: user.id,
          organizationId: orgId
        },
        include: {
          organization: true
        }
      });
      if (userOrg) {
        event.locals.org = userOrg.organization;
        const orgName = event.cookies.get("org_name");
        if (orgName) {
          try {
            event.locals.org_name = decodeURIComponent(orgName);
          } catch (e) {
            event.locals.org_name = orgName;
          }
        } else {
          event.locals.org_name = userOrg.organization.name;
        }
      } else {
        event.cookies.delete("org", { path: "/" });
        event.cookies.delete("org_name", { path: "/" });
        throw redirect(307, "/org");
      }
    }
  }
  if (event.url.pathname.startsWith("/app")) {
    if (!user) {
      throw redirect(307, "/login");
    }
    if (!event.locals.org) {
      throw redirect(307, "/org");
    }
  } else if (event.url.pathname.startsWith("/admin")) {
    if (!user) {
      throw redirect(307, "/login");
    }
    if (!user.email || !user.email.endsWith("@micropyramid.com")) {
      throw redirect(307, "/app");
    }
  } else if (event.url.pathname.startsWith("/org")) {
    if (!user) {
      throw redirect(307, "/login");
    }
  }
  return resolve(event);
}
export {
  handle
};
