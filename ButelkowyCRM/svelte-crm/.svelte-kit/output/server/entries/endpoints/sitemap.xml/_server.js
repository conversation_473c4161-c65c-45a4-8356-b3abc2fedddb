import { PrismaClient } from "@prisma/client";
import { D as DEV } from "../../../chunks/false.js";
const dev = DEV;
const prisma = new PrismaClient();
async function GET() {
  try {
    const baseUrl = dev ? "http://localhost:5173" : "https://bottlecrm.io";
    const blogPosts = await prisma.blogPost.findMany({
      where: {
        draft: false
      },
      select: {
        slug: true,
        updatedAt: true
      }
    });
    const manualUrls = [
      { url: "/", priority: "1.0", changefreq: "daily" },
      { url: "/about", priority: "0.8", changefreq: "monthly" },
      { url: "/contact", priority: "0.8", changefreq: "monthly" }
    ];
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    manualUrls.forEach((page) => {
      xml += "  <url>\n";
      xml += `    <loc>${baseUrl}${page.url}</loc>
`;
      xml += `    <changefreq>${page.changefreq}</changefreq>
`;
      xml += `    <priority>${page.priority}</priority>
`;
      xml += "  </url>\n";
    });
    blogPosts.forEach((post) => {
      xml += "  <url>\n";
      xml += `    <loc>${baseUrl}/blog/${post.slug}</loc>
`;
      xml += `    <lastmod>${post.updatedAt.toISOString()}</lastmod>
`;
      xml += "    <changefreq>weekly</changefreq>\n";
      xml += "    <priority>0.7</priority>\n";
      xml += "  </url>\n";
    });
    xml += "</urlset>";
    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "max-age=3600"
      }
    });
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return new Response("Error generating sitemap", { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
export {
  GET
};
