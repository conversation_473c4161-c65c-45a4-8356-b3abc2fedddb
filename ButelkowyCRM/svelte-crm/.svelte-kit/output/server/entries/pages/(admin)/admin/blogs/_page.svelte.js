import { G as ensure_array_like, J as attr, D as escape_html, R as stringify, C as pop, A as push } from "../../../../../chunks/index2.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  const each_array = ensure_array_like(data.blogs);
  $$payload.out += `<div class="container mx-auto px-4 py-8"><h1 class="text-2xl font-bold mb-6">Blogs</h1> <div class="mb-4 flex justify-end"><a href="/admin/blogs/new" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded">+ New Blog</a></div> <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow"><thead><tr class="bg-gray-100"><th class="py-2 px-4 border-b text-left">Title</th><th class="py-2 px-4 border-b text-left">Category</th><th class="py-2 px-4 border-b text-left">Draft</th><th class="py-2 px-4 border-b text-left">Created At</th><th class="py-2 px-4 border-b text-left">Updated At</th></tr></thead><tbody><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let blog = each_array[$$index];
    $$payload.out += `<tr class="hover:bg-gray-50"><td class="py-2 px-4 border-b"><a${attr("href", `/admin/blogs/${stringify(blog.id)}/`)}>${escape_html(blog.title)}</a> - <a${attr("href", `/admin/blogs/${stringify(blog.id)}/edit/`)}>Edit</a></td><td class="py-2 px-4 border-b">${escape_html(blog.category)}</td><td class="py-2 px-4 border-b">`;
    if (blog.draft) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="inline-block px-2 py-1 text-xs font-semibold text-yellow-800 bg-yellow-100 rounded">Draft</span>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<span class="inline-block px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded">Published</span>`;
    }
    $$payload.out += `<!--]--></td><td class="py-2 px-4 border-b">${escape_html(new Date(blog.createdAt).toLocaleString())}</td><td class="py-2 px-4 border-b">${escape_html(new Date(blog.updatedAt).toLocaleString())}</td></tr>`;
  }
  $$payload.out += `<!--]--></tbody></table></div>`;
  pop();
}
export {
  _page as default
};
