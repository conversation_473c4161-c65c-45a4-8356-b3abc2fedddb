import { G as ensure_array_like, J as attr, D as escape_html, M as bind_props, C as pop, A as push } from "../../../../../../../chunks/index2.js";
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var FEATURE_FLAG_NAMES = Object.freeze({
  // This flag exists as a workaround for issue 454 (basically a browser bug) - seems like these rect values take time to update when in grid layout. Setting it to true can cause strange behaviour in the REPL for non-grid zones, see issue 470
  USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT: "USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT"
});
_defineProperty({}, FEATURE_FLAG_NAMES.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT, false);
var _ID_TO_INSTRUCTION;
var INSTRUCTION_IDs$1 = {
  DND_ZONE_ACTIVE: "dnd-zone-active",
  DND_ZONE_DRAG_DISABLED: "dnd-zone-drag-disabled"
};
_ID_TO_INSTRUCTION = {}, _defineProperty(_ID_TO_INSTRUCTION, INSTRUCTION_IDs$1.DND_ZONE_ACTIVE, "Tab to one the items and press space-bar or enter to start dragging it"), _defineProperty(_ID_TO_INSTRUCTION, INSTRUCTION_IDs$1.DND_ZONE_DRAG_DISABLED, "This is a disabled drag and drop list"), _ID_TO_INSTRUCTION;
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let form = $$props["form"];
  let blog = data.blog;
  let contentBlocks = blog.contentBlocks;
  let editingBlockId = null;
  let newBlock = {
    content: "",
    displayOrder: contentBlocks.length + 1,
    draft: false
  };
  function make_slug(title) {
    return title.toLowerCase().replace(/\s+/g, "-").replace(/[^\w-]+/g, "");
  }
  let editable_title = form?.data?.title ?? blog.title;
  let slug = form?.data?.slug ?? blog.slug;
  let previous_editable_title_for_slug_generation = void 0;
  {
    if (previous_editable_title_for_slug_generation === void 0 && editable_title !== void 0) {
      previous_editable_title_for_slug_generation = editable_title;
    } else if (editable_title !== previous_editable_title_for_slug_generation) {
      slug = make_slug(editable_title);
      previous_editable_title_for_slug_generation = editable_title;
    }
  }
  const each_array = ensure_array_like(contentBlocks);
  $$payload.out += `<div class="max-w-5xl mx-auto mt-10 p-8 bg-white rounded-lg shadow"><h1 class="text-2xl font-bold mb-6">Edit Blog</h1> <form method="POST" action="?/update-blog" class="space-y-5"><input type="hidden" name="title"${attr("value", editable_title)}> <input type="hidden" name="slug"${attr("value", slug)}> <div><label class="block font-medium mb-1">Title: <input${attr("value", editable_title)} required class="mt-1 block w-full rounded border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"></label></div> <div><label class="block font-medium mb-1">SEO Title: <input name="seoTitle"${attr("value", blog.seoTitle)} class="mt-1 block w-full rounded border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"></label></div> <div><label class="block font-medium mb-1">SEO Description: <textarea name="seoDescription" class="mt-1 block w-full rounded border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500">`;
  const $$body = escape_html(blog.seoDescription);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></label></div> <div><label class="block font-medium mb-1">Excerpt: <textarea name="excerpt" class="mt-1 block w-full rounded border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500">`;
  const $$body_1 = escape_html(blog.excerpt);
  if ($$body_1) {
    $$payload.out += `${$$body_1}`;
  }
  $$payload.out += `</textarea></label></div> <div><label class="block font-medium mb-1">Slug: <input${attr("value", slug)} required class="mt-1 block w-full rounded border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"${attr("disabled", !blog.draft, true)}></label> `;
  if (!blog.draft) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-xs text-gray-500 mt-1">Slug can only be edited in draft mode.</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><div class="block font-medium mb-1 text-gray-700">Category: <span class="font-semibold">${escape_html(blog.category)}</span></div></div> <div><label class="inline-flex items-center" for="blog-draft">Draft <input id="blog-draft" type="checkbox" name="draft"${attr("checked", blog.draft, true)} class="h-4 w-4 text-blue-600 border-gray-300 rounded ml-2"></label></div> <button type="submit" class="w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition">Save</button></form> <h2 class="text-xl font-semibold mt-10 mb-4">Content Blocks</h2> <ul class="space-y-4"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let block = each_array[$$index];
    $$payload.out += `<li class="border rounded p-4 bg-gray-50"${attr("data-id", block.id)}>`;
    if (editingBlockId === block.id) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<form method="POST" action="?/edit-block" class="space-y-2"><input type="hidden" name="id"${attr("value", block.id)}> <div><label class="block text-sm font-medium"${attr("for", `edit-type-${block.id}`)}>Type:</label> <select${attr("id", `edit-type-${block.id}`)} name="type" class="mt-1 block w-full rounded border-gray-300 shadow-sm"><option value="MARKDOWN">Markdown</option><option value="CODE">Code</option></select></div> <div><label class="block text-sm font-medium"${attr("for", `edit-content-${block.id}`)}>Content:</label> <textarea${attr("id", `edit-content-${block.id}`)} name="content" class="mt-1 block w-full rounded border-gray-300 shadow-sm" rows="15">`;
      const $$body_2 = escape_html(block._editContent);
      if ($$body_2) {
        $$payload.out += `${$$body_2}`;
      }
      $$payload.out += `</textarea></div> <div><label class="inline-flex items-center"${attr("for", `edit-draft-${block.id}`)}>Draft <input${attr("id", `edit-draft-${block.id}`)} type="checkbox" name="draft"${attr("checked", block._editDraft, true)} class="h-4 w-4 text-blue-600 border-gray-300 rounded ml-2"></label></div> <div class="flex gap-2"><button type="submit" class="py-1 px-3 bg-green-600 text-white rounded hover:bg-green-700">Save</button> <button type="button" class="py-1 px-3 bg-gray-300 rounded hover:bg-gray-400">Cancel</button></div></form>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="flex justify-between items-center"><div><span class="font-semibold">${escape_html(block.type)}</span> <span class="ml-2 text-xs text-gray-500">Order: ${escape_html(block.displayOrder)}</span> `;
      if (block.draft) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="ml-2 text-xs text-yellow-600">Draft</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="flex gap-2"><button type="button" class="py-1 px-3 bg-blue-500 text-white rounded hover:bg-blue-600">Edit</button> <form method="POST" action="?/delete-block" class="inline"><input type="hidden" name="id"${attr("value", block.id)}> <button type="submit" class="py-1 px-3 bg-red-600 text-white rounded hover:bg-red-700">Delete</button></form></div></div> <pre class="mt-2 bg-white p-2 rounded text-sm overflow-x-auto">${escape_html(block.content)}</pre>`;
    }
    $$payload.out += `<!--]--></li>`;
  }
  $$payload.out += `<!--]--></ul> <h3 class="text-lg font-semibold mt-8 mb-2">Add Content Block</h3> <form method="POST" action="?/add-block" class="space-y-2"><div><label class="block text-sm font-medium" for="add-type">Type:</label> <select id="add-type" name="type" class="mt-1 block w-full rounded border-gray-300 shadow-sm"><option value="MARKDOWN">Markdown</option><option value="CODE">Code</option></select></div> <div><label class="block text-sm font-medium" for="add-content">Content:</label> <textarea id="add-content" name="content" class="mt-1 block w-full rounded border-gray-300 shadow-sm" rows="15">`;
  const $$body_3 = escape_html(newBlock.content);
  if ($$body_3) {
    $$payload.out += `${$$body_3}`;
  }
  $$payload.out += `</textarea></div> <div><label class="block text-sm font-medium" for="add-displayOrder">Display Order:</label> <input id="add-displayOrder" type="number" name="displayOrder"${attr("value", newBlock.displayOrder)} class="mt-1 block w-full rounded border-gray-300 shadow-sm" min="1"></div> <div><label class="inline-flex items-center" for="add-draft">Draft <input id="add-draft" type="checkbox" name="draft"${attr("checked", newBlock.draft, true)} class="h-4 w-4 text-blue-600 border-gray-300 rounded ml-2"></label></div> <button type="submit" class="py-1 px-3 bg-blue-600 text-white rounded hover:bg-blue-700">Add Block</button></form> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data, form });
  pop();
}
export {
  _page as default
};
