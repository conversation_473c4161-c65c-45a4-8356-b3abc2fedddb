import { D as escape_html, J as attr, C as pop, A as push } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
function _page($$payload, $$props) {
  push();
  let { form } = $$props;
  let title = form?.data?.title ?? "";
  let excerpt = form?.data?.excerpt ?? "";
  function make_slug(title2) {
    return title2.toLowerCase().replace(/\s+/g, "-").replace(/[^\w-]+/g, "");
  }
  let slug = make_slug(title);
  $$payload.out += `<div class="max-w-xl mx-auto mt-10 p-6 bg-white rounded shadow"><h1 class="text-2xl font-bold mb-6">Create New Blog</h1> `;
  if (form?.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">${escape_html(form.error)}</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (form?.success) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">Blog post created successfully!</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" class="space-y-4"><div><label for="title" class="block mb-1 font-medium">Title</label> <input class="w-full border rounded px-3 py-2" id="title" name="title"${attr("value", title)} required></div> <div><label for="excerpt" class="block mb-1 font-medium">Excerpt</label> <textarea class="w-full border rounded px-3 py-2" id="excerpt" name="excerpt" required rows="2">`;
  const $$body = escape_html(excerpt);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> <div><label for="slug" class="block mb-1 font-medium">Slug</label> <input class="w-full border rounded px-3 py-2" id="slug" name="slug"${attr("value", slug)} required></div> <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Create Blog</button></form></div>`;
  pop();
}
export {
  _page as default
};
