import { E as spread_props, C as pop, A as push, P as head, D as escape_html } from "../../../../chunks/index2.js";
import { U as Users } from "../../../../chunks/users.js";
import { B as Building_2 } from "../../../../chunks/building-2.js";
import { I as Icon } from "../../../../chunks/Icon.js";
import { U as User_check } from "../../../../chunks/user-check.js";
import { T as Target } from "../../../../chunks/target.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { C as Calendar } from "../../../../chunks/calendar.js";
import { C as Circle_alert } from "../../../../chunks/circle-alert.js";
import { F as File_text } from "../../../../chunks/file-text.js";
function Contact($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M16 2v2" }],
    [
      "path",
      {
        "d": "M7 22v-2a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2"
      }
    ],
    ["path", { "d": "M8 2v2" }],
    [
      "circle",
      { "cx": "12", "cy": "11", "r": "3" }
    ],
    [
      "rect",
      {
        "x": "3",
        "y": "4",
        "width": "18",
        "height": "18",
        "rx": "2"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "contact" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  const { metrics } = data;
  const formatNumber = (num) => {
    return new Intl.NumberFormat("en-US").format(num);
  };
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Analytics - BottleCRM</title>`;
  });
  $$payload.out += `<div class="p-6 space-y-6"><div class="border-b border-gray-200 pb-4"><h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1> <p class="text-gray-600 mt-2">Overview of your CRM performance and key metrics</p></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Total Users</p> <p class="text-3xl font-bold text-gray-900">${escape_html(formatNumber(metrics.totalUsers))}</p></div> <div class="p-3 bg-blue-100 rounded-full">`;
  Users($$payload, { class: "w-6 h-6 text-blue-600" });
  $$payload.out += `<!----></div></div> <div class="mt-4 text-sm text-gray-500">Active users in the system</div></div> <div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Organizations</p> <p class="text-3xl font-bold text-gray-900">${escape_html(formatNumber(metrics.totalOrganizations))}</p></div> <div class="p-3 bg-purple-100 rounded-full">`;
  Building_2($$payload, { class: "w-6 h-6 text-purple-600" });
  $$payload.out += `<!----></div></div> <div class="mt-4 text-sm text-gray-500">Active organizations</div></div> <div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Accounts</p> <p class="text-3xl font-bold text-gray-900">${escape_html(formatNumber(metrics.totalAccounts))}</p></div> <div class="p-3 bg-green-100 rounded-full">`;
  Contact($$payload, { class: "w-6 h-6 text-green-600" });
  $$payload.out += `<!----></div></div> <div class="mt-4 text-sm text-green-600">+${escape_html(formatNumber(metrics.newAccountsThisMonth))} this month</div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Contacts</p> <p class="text-2xl font-bold text-gray-900">${escape_html(formatNumber(metrics.totalContacts))}</p></div> <div class="p-3 bg-indigo-100 rounded-full">`;
  User_check($$payload, { class: "w-5 h-5 text-indigo-600" });
  $$payload.out += `<!----></div></div></div> <div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Leads</p> <p class="text-2xl font-bold text-gray-900">${escape_html(formatNumber(metrics.totalLeads))}</p></div> <div class="p-3 bg-pink-100 rounded-full">`;
  Target($$payload, { class: "w-5 h-5 text-pink-600" });
  $$payload.out += `<!----></div></div> <div class="mt-2 text-sm text-gray-500">+${escape_html(formatNumber(metrics.newLeadsThisMonth))} this month</div></div> <div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Opportunities</p> <p class="text-2xl font-bold text-gray-900">${escape_html(formatNumber(metrics.totalOpportunities))}</p></div> <div class="p-3 bg-orange-100 rounded-full">`;
  Trending_up($$payload, { class: "w-5 h-5 text-orange-600" });
  $$payload.out += `<!----></div></div> <div class="mt-2 text-sm text-gray-500">${escape_html(formatNumber(metrics.openOpportunities))} active</div></div></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"><div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Tasks</h3> `;
  Calendar($$payload, { class: "w-5 h-5 text-gray-400" });
  $$payload.out += `<!----></div> <div class="space-y-2"><div class="flex justify-between"><span class="text-sm text-gray-600">Total Tasks</span> <span class="font-medium">${escape_html(formatNumber(metrics.totalTasks))}</span></div> <div class="flex justify-between"><span class="text-sm text-gray-600">Completed This Month</span> <span class="font-medium text-green-600">${escape_html(formatNumber(metrics.tasksCompletedThisMonth))}</span></div></div></div> <div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Support Cases</h3> `;
  Circle_alert($$payload, { class: "w-5 h-5 text-gray-400" });
  $$payload.out += `<!----></div> <div class="space-y-2"><div class="flex justify-between"><span class="text-sm text-gray-600">Total Cases</span> <span class="font-medium">${escape_html(formatNumber(metrics.totalCases))}</span></div></div></div> <div class="bg-white rounded-lg shadow border border-gray-200 p-6"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">This Month</h3> `;
  File_text($$payload, { class: "w-5 h-5 text-gray-400" });
  $$payload.out += `<!----></div> <div class="space-y-2"><div class="flex justify-between"><span class="text-sm text-gray-600">New Opportunities</span> <span class="font-medium text-blue-600">${escape_html(formatNumber(metrics.newOpportunitiesThisMonth))}</span></div></div></div></div></div>`;
  pop();
}
export {
  _page as default
};
