import { p as prisma } from "../../../../../../chunks/prisma.js";
async function load() {
  return {};
}
const actions = {
  default: async ({ request }) => {
    const data = await request.formData();
    const title = data.get("title");
    const excerpt = data.get("excerpt");
    const slug = data.get("slug");
    if (!title || !excerpt || !slug) {
      return { error: "All fields are required" };
    }
    try {
      await prisma.blogPost.create({
        data: {
          title,
          excerpt,
          slug,
          seoTitle: "",
          seoDescription: "",
          draft: true
        }
      });
      return { success: true };
    } catch (e) {
      return { error: e?.message || "Error creating blog" };
    }
  }
};
export {
  actions,
  load
};
