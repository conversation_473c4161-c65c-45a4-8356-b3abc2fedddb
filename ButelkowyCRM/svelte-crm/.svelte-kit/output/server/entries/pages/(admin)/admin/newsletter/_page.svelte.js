import { P as head, D as escape_html, G as ensure_array_like, I as attr_class, R as stringify, M as bind_props, C as pop, A as push } from "../../../../../chunks/index2.js";
import { U as Users } from "../../../../../chunks/users.js";
import { U as User_check } from "../../../../../chunks/user-check.js";
import { T as Trending_up } from "../../../../../chunks/trending-up.js";
import { M as Mail } from "../../../../../chunks/mail.js";
import { C as Calendar } from "../../../../../chunks/calendar.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  function getStatusClass(isActive, isConfirmed) {
    if (!isActive) return "bg-red-100 text-red-800";
    if (isConfirmed) return "bg-green-100 text-green-800";
    return "bg-yellow-100 text-yellow-800";
  }
  function getStatusText(isActive, isConfirmed) {
    if (!isActive) return "Unsubscribed";
    if (isConfirmed) return "Active";
    return "Pending";
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Newsletter Subscribers - Admin</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50 py-8"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="mb-8"><h1 class="text-3xl font-bold text-gray-900 mb-2">Newsletter Management</h1> <p class="text-gray-600">Manage and view newsletter subscribers</p></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><div class="flex-shrink-0">`;
  Users($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <div class="ml-4"><p class="text-sm font-medium text-gray-500">Total Subscribers</p> <p class="text-2xl font-bold text-gray-900">${escape_html(data.totalCount)}</p></div></div></div> <div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><div class="flex-shrink-0">`;
  User_check($$payload, { class: "h-8 w-8 text-green-600" });
  $$payload.out += `<!----></div> <div class="ml-4"><p class="text-sm font-medium text-gray-500">Active Subscribers</p> <p class="text-2xl font-bold text-gray-900">${escape_html(data.activeCount)}</p></div></div></div> <div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><div class="flex-shrink-0">`;
  Trending_up($$payload, { class: "h-8 w-8 text-purple-600" });
  $$payload.out += `<!----></div> <div class="ml-4"><p class="text-sm font-medium text-gray-500">Active Rate</p> <p class="text-2xl font-bold text-gray-900">${escape_html(data.totalCount > 0 ? Math.round(data.activeCount / data.totalCount * 100) : 0)}%</p></div></div></div></div> `;
  if (data.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"><p class="text-red-800">${escape_html(data.error)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="bg-white shadow rounded-lg overflow-hidden"><div class="px-6 py-4 border-b border-gray-200"><h2 class="text-lg font-medium text-gray-900 flex items-center">`;
  Mail($$payload, { class: "h-5 w-5 mr-2" });
  $$payload.out += `<!----> Newsletter Subscribers</h2></div> `;
  if (data.subscribers.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-12">`;
    Mail($$payload, { class: "mx-auto h-12 w-12 text-gray-400" });
    $$payload.out += `<!----> <h3 class="mt-2 text-sm font-medium text-gray-900">No subscribers yet</h3> <p class="mt-1 text-sm text-gray-500">Get started by promoting your newsletter.</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(data.subscribers);
    $$payload.out += `<div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribed</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confirmed</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let subscriber = each_array[$$index];
      $$payload.out += `<tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap"><div class="text-sm font-medium text-gray-900">${escape_html(subscriber.email)}</div></td><td class="px-6 py-4 whitespace-nowrap"><span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stringify(getStatusClass(subscriber.isActive, subscriber.isConfirmed))}`)}>${escape_html(getStatusText(subscriber.isActive, subscriber.isConfirmed))}</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><div class="flex items-center">`;
      Calendar($$payload, { class: "h-4 w-4 mr-1" });
      $$payload.out += `<!----> ${escape_html(formatDate(subscriber.subscribedAt))}</div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">`;
      if (subscriber.confirmedAt) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center">`;
        Calendar($$payload, { class: "h-4 w-4 mr-1" });
        $$payload.out += `<!----> ${escape_html(formatDate(subscriber.confirmedAt))}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<span class="text-gray-400">Not confirmed</span>`;
      }
      $$payload.out += `<!--]--></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${escape_html(subscriber.ipAddress || "N/A")}</td></tr>`;
    }
    $$payload.out += `<!--]--></tbody></table></div>`;
  }
  $$payload.out += `<!--]--></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
