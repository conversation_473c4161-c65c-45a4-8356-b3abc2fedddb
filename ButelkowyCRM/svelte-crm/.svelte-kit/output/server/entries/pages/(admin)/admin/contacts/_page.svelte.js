import { G as ensure_array_like, D as escape_html, J as attr, C as pop, A as push } from "../../../../../chunks/index2.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };
  $$payload.out += `<div class="p-6"><div class="mb-6"><h1 class="text-3xl font-bold text-gray-900">Contact Submissions</h1></div> `;
  if (data.contacts && data.contacts.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(data.contacts);
    $$payload.out += `<div class="overflow-x-auto bg-white shadow-lg rounded-lg"><table class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Info</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tracking</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let contact = each_array[$$index];
      $$payload.out += `<tr class="hover:bg-gray-50 transition-colors"><td class="px-6 py-4 whitespace-nowrap"><div class="flex flex-col"><div class="text-sm font-medium text-gray-900">${escape_html(contact.name)}</div> <div class="text-sm text-gray-500">${escape_html(contact.email)}</div></div></td><td class="px-6 py-4 whitespace-nowrap"><span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">${escape_html(contact.reason)}</span></td><td class="px-6 py-4"><div class="text-sm text-gray-900 max-w-xs truncate"${attr("title", contact.message)}>${escape_html(contact.message)}</div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${escape_html(formatDate(contact.createdAt))}</td><td class="px-6 py-4"><div class="text-xs text-gray-500 space-y-1">`;
      if (contact.ipAddress) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div>IP: ${escape_html(contact.ipAddress)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (contact.referrer) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="truncate max-w-32"${attr("title", contact.referrer)}>Ref: ${escape_html(contact.referrer)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></td></tr>`;
    }
    $$payload.out += `<!--]--></tbody></table></div> <div class="mt-4 text-sm text-gray-600">Total submissions: ${escape_html(data.contacts.length)}</div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="text-center py-12"><div class="mx-auto h-12 w-12 text-gray-400"><svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-5m-7 0h5"></path></svg></div> <h3 class="mt-2 text-sm font-medium text-gray-900">No contact submissions</h3> <p class="mt-1 text-sm text-gray-500">No contact form requests have been submitted yet.</p></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
export {
  _page as default
};
