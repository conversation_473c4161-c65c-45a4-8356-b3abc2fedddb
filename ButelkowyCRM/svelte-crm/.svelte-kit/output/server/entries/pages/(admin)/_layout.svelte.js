/* empty css                  */
import { E as spread_props, C as pop, A as push } from "../../../chunks/index2.js";
import { I as Icon } from "../../../chunks/Icon.js";
import { F as File_text } from "../../../chunks/file-text.js";
import { U as User } from "../../../chunks/user.js";
import { L as Log_out } from "../../../chunks/log-out.js";
import { M as Menu } from "../../../chunks/menu.js";
function Chart_bar($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M3 3v16a2 2 0 0 0 2 2h16" }],
    ["path", { "d": "M7 16h8" }],
    ["path", { "d": "M7 11h12" }],
    ["path", { "d": "M7 6h3" }]
  ];
  Icon($$payload, spread_props([
    { name: "chart-bar" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function House($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"
      }
    ],
    [
      "path",
      {
        "d": "M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "house" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _layout($$payload, $$props) {
  let { data, children } = $$props;
  $$payload.out += `<div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm border-b border-gray-200"><div class="px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16"><div class="flex items-center"><div class="flex-shrink-0"><h1 class="text-xl font-bold text-blue-600">BottleCRM</h1></div> <nav class="hidden md:ml-8 md:flex md:space-x-1"><a href="/admin" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors">`;
  House($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Dashboard</a> <a href="/admin/blogs" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors">`;
  File_text($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Blog Posts</a> <a href="/admin/contacts" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors">`;
  User($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Contact Submissions</a> <a href="/admin/newsletter" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-blue-600 transition-colors">`;
  Chart_bar($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Newsletter</a></nav></div> <div class="flex items-center space-x-4"><a class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-red-50 hover:text-red-600 transition-colors" href="/logout">`;
  Log_out($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span class="hidden sm:block">Logout</span></a> <button class="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100">`;
  {
    $$payload.out += "<!--[!-->";
    Menu($$payload, { class: "w-6 h-6" });
  }
  $$payload.out += `<!--]--></button></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></header> <main class="p-6">`;
  children($$payload);
  $$payload.out += `<!----></main></div>`;
}
export {
  _layout as default
};
