import { f as fail } from "../../../../chunks/index.js";
import { p as prisma } from "../../../../chunks/prisma.js";
async function load({ url }) {
  const token = url.searchParams.get("token");
  if (!token) {
    return {
      error: "Invalid unsubscribe link. Please check your email for the correct link."
    };
  }
  try {
    const subscriber = await prisma.newsletterSubscriber.findUnique({
      where: { confirmationToken: token }
    });
    if (!subscriber) {
      return {
        error: "Invalid unsubscribe token. This link may have expired or already been used."
      };
    }
    return {
      subscriber: {
        email: subscriber.email,
        token: subscriber.confirmationToken
      }
    };
  } catch (error) {
    console.error("Unsubscribe load error:", error);
    return {
      error: "An error occurred while processing your request. Please try again later."
    };
  }
}
const actions = {
  unsubscribe: async ({ request }) => {
    const formData = await request.formData();
    const token = formData.get("token")?.toString();
    if (!token) {
      return fail(400, { message: "Invalid unsubscribe token" });
    }
    try {
      const subscriber = await prisma.newsletterSubscriber.findUnique({
        where: { confirmationToken: token }
      });
      if (!subscriber) {
        return fail(404, { message: "Subscriber not found or already unsubscribed" });
      }
      if (!subscriber.isActive) {
        return { success: true, message: "You have already been unsubscribed from our newsletter" };
      }
      await prisma.newsletterSubscriber.update({
        where: { confirmationToken: token },
        data: {
          isActive: false,
          unsubscribedAt: /* @__PURE__ */ new Date()
        }
      });
      return { success: true, message: "Successfully unsubscribed from newsletter" };
    } catch (error) {
      console.error("Unsubscribe error:", error);
      return fail(500, { message: "Failed to unsubscribe. Please try again later." });
    }
  }
};
export {
  actions,
  load
};
