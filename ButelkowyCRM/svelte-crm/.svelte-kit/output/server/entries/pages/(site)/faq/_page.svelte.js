import { P as head, J as attr, R as stringify } from "../../../../chunks/index2.js";
import { C as Circle_help } from "../../../../chunks/circle-help.js";
import { C as Chevron_down } from "../../../../chunks/chevron-down.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { S as Settings } from "../../../../chunks/settings.js";
import { H as Headphones } from "../../../../chunks/headphones.js";
import { B as Briefcase } from "../../../../chunks/briefcase.js";
import { D as Download } from "../../../../chunks/download.js";
import { E as External_link } from "../../../../chunks/external-link.js";
import { G as Github } from "../../../../chunks/github.js";
import { M as Message_circle } from "../../../../chunks/message-circle.js";
import { Z as Zap } from "../../../../chunks/zap.js";
import { h as html } from "../../../../chunks/html.js";
function _page($$payload) {
  let activeFaq = null;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>BottleCRM FAQ | Free Open Source CRM for Startups &amp; SMBs</title>`;
    $$payload2.out += `<meta name="description" content="Answers to common questions about BottleCRM, the free open-source CRM for startups and small businesses. Learn about features, pricing, support, and self-hosting."> <meta name="keywords" content="free crm, open source crm, bottlecrm, crm faq, self hosted crm, startup crm, small business crm, crm support, crm pricing, crm features"> <link rel="canonical" href="https://bottlecrm.io/faq"> <meta name="robots" content="index, follow"> <meta property="og:title" content="BottleCRM FAQ | Free Open Source CRM for Startups &amp; SMBs"> <meta property="og:description" content="Find answers to your questions about BottleCRM, the free open-source CRM for startups and small businesses."> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/faq"> <meta property="og:image" content="https://bottlecrm.io/og-image.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:title" content="BottleCRM FAQ | Free Open Source CRM for Startups &amp; SMBs"> <meta name="twitter:description" content="Answers to common questions about BottleCRM, the free open-source CRM for startups and small businesses."> <meta name="twitter:image" content="https://bottlecrm.io/og-image.png"> ${html(`
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Is BottleCRM really free?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, BottleCRM is 100% free and open-source with no user limits or hidden fees."
          }
        },
        {
          "@type": "Question",
          "name": "Can I self-host BottleCRM?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Absolutely! You can deploy BottleCRM on your own server or cloud for full data control."
          }
        },
        {
          "@type": "Question",
          "name": "What features are included in BottleCRM?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "All core CRM features: contact management, sales pipeline, tasks, analytics, and more are included for free."
          }
        },
        {
          "@type": "Question",
          "name": "How does BottleCRM make money?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We offer optional paid services like setup, support, and custom development. The software itself is always free."
          }
        }
      ]
    }
    <\/script>
  `)}`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><div class="max-w-4xl mx-auto"><div class="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6">`;
  Circle_help($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Frequently Asked Questions</div> <h1 class="text-4xl md:text-6xl font-extrabold mb-6">Everything About <span class="text-yellow-300">BottleCRM</span></h1> <p class="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">Get answers to common questions about our free, open-source CRM software. Learn about pricing, features, technical requirements, and how to get started.</p></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Browse FAQ Categories</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Find answers organized by topic to help you get started with BottleCRM.</p></div> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-16"><div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"><div class="flex items-center mb-4"><div class="rounded-lg bg-gradient-to-r from-blue-100 to-indigo-100 p-3 mr-4">`;
  Circle_help($$payload, { class: "h-6 w-6 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900">General Questions</h3></div> <p class="text-gray-600 mb-4">5 questions</p> <a href="#general" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">View Questions `;
  Chevron_down($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"><div class="flex items-center mb-4"><div class="rounded-lg bg-gradient-to-r from-blue-100 to-indigo-100 p-3 mr-4">`;
  Dollar_sign($$payload, { class: "h-6 w-6 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900">Pricing &amp; Business Model</h3></div> <p class="text-gray-600 mb-4">6 questions</p> <a href="#pricing" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">View Questions `;
  Chevron_down($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"><div class="flex items-center mb-4"><div class="rounded-lg bg-gradient-to-r from-blue-100 to-indigo-100 p-3 mr-4">`;
  Settings($$payload, { class: "h-6 w-6 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900">Technical Questions</h3></div> <p class="text-gray-600 mb-4">7 questions</p> <a href="#technical" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">View Questions `;
  Chevron_down($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"><div class="flex items-center mb-4"><div class="rounded-lg bg-gradient-to-r from-blue-100 to-indigo-100 p-3 mr-4">`;
  Headphones($$payload, { class: "h-6 w-6 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900">Support &amp; Community</h3></div> <p class="text-gray-600 mb-4">6 questions</p> <a href="#support" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">View Questions `;
  Chevron_down($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"><div class="flex items-center mb-4"><div class="rounded-lg bg-gradient-to-r from-blue-100 to-indigo-100 p-3 mr-4">`;
  Briefcase($$payload, { class: "h-6 w-6 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900">Business &amp; Legal</h3></div> <p class="text-gray-600 mb-4">6 questions</p> <a href="#business" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">View Questions `;
  Chevron_down($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div></div> <div id="general" class="mb-16"><div class="text-center mb-12"><div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 mb-4">`;
  Circle_help($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> General Questions</div> <h2 class="text-3xl font-extrabold text-gray-900">General Questions</h2></div> <div class="max-w-4xl mx-auto space-y-4"><div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "general-0")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What is BottleCRM and why is it free?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "general-1")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Is BottleCRM really completely free forever?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "general-2")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">How does BottleCRM compare to commercial CRM platforms?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "general-3")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Who should use BottleCRM?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "general-4")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What features are included in the free version?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div id="pricing" class="mb-16"><div class="text-center mb-12"><div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 mb-4">`;
  Dollar_sign($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Pricing &amp; Business Model</div> <h2 class="text-3xl font-extrabold text-gray-900">Pricing &amp; Business Model</h2></div> <div class="max-w-4xl mx-auto space-y-4"><div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "pricing-0")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">If the software is free, how do you make money?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "pricing-1")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What's included in the Professional Support ($197)?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "pricing-2")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What's included in the Enterprise Setup ($497)?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "pricing-3")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Do you offer refunds for support services?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "pricing-4")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Can I switch from paid support back to free?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "pricing-5")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Do you offer custom pricing for large organizations?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div id="technical" class="mb-16"><div class="text-center mb-12"><div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 mb-4">`;
  Settings($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Technical Questions</div> <h2 class="text-3xl font-extrabold text-gray-900">Technical Questions</h2></div> <div class="max-w-4xl mx-auto space-y-4"><div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-0")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What technology stack does BottleCRM use?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-1")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Can I self-host BottleCRM on my own servers?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-2")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What are the system requirements for BottleCRM?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-3")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Is BottleCRM mobile-friendly?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-4")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Can I customize BottleCRM for my specific business needs?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-5")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Does BottleCRM integrate with other tools and services?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "technical-6")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">How do I migrate data from my existing CRM?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div id="support" class="mb-16"><div class="text-center mb-12"><div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 mb-4">`;
  Headphones($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Support &amp; Community</div> <h2 class="text-3xl font-extrabold text-gray-900">Support &amp; Community</h2></div> <div class="max-w-4xl mx-auto space-y-4"><div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "support-0")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What kind of support is available for the free version?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "support-1")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">How quickly can I get help with paid support services?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "support-2")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Do you provide training for my team?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "support-3")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Is there documentation available?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "support-4")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Can I contribute to BottleCRM development?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "support-5")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">How often is BottleCRM updated?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div id="business" class="mb-16"><div class="text-center mb-12"><div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 mb-4">`;
  Briefcase($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Business &amp; Legal</div> <h2 class="text-3xl font-extrabold text-gray-900">Business &amp; Legal</h2></div> <div class="max-w-4xl mx-auto space-y-4"><div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "business-0")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What license does BottleCRM use?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "business-1")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Who owns the data in my BottleCRM installation?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "business-2")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Is BottleCRM secure for business use?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "business-3")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Can BottleCRM handle GDPR compliance?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "business-4")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">Is BottleCRM suitable for regulated industries?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"><button class="w-full px-6 py-6 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition-colors duration-200"${attr("aria-expanded", activeFaq === "business-5")}><div class="flex justify-between items-start"><h3 class="text-lg font-semibold text-gray-900 pr-4 leading-relaxed">What happens if BottleCRM development stops?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mt-1 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Still Have Questions?</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Can't find what you're looking for? Here are some helpful resources and ways to get in touch.</p></div> <div class="grid gap-8 md:grid-cols-3 max-w-4xl mx-auto"><div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 text-center"><div class="rounded-lg bg-blue-100 p-3 inline-block mb-4">`;
  Download($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900 mb-3">Get Started</h3> <p class="text-gray-600 mb-4">Download and try BottleCRM for free right now.</p> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">Download Free `;
  External_link($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 text-center"><div class="rounded-lg bg-green-100 p-3 inline-block mb-4">`;
  Github($$payload, { class: "h-8 w-8 text-green-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900 mb-3">GitHub Support</h3> <p class="text-gray-600 mb-4">Report issues and get community help.</p> <a href="https://github.com/MicroPyramid/opensource-startup-crm/issues" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold">Open Issue `;
  External_link($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 text-center"><div class="rounded-lg bg-orange-100 p-3 inline-block mb-4">`;
  Message_circle($$payload, { class: "h-8 w-8 text-orange-600" });
  $$payload.out += `<!----></div> <h3 class="text-xl font-bold text-gray-900 mb-3">Contact Support</h3> <p class="text-gray-600 mb-4">Get professional help and paid support.</p> <a href="/contact" class="inline-flex items-center text-orange-600 hover:text-orange-700 font-semibold">Contact Us `;
  External_link($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-4xl md:text-5xl font-extrabold mb-6">Ready to Start Your Free CRM?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">Now that you know everything about BottleCRM, why not give it a try? It's completely free, and you can have it running in minutes.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  Zap($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Try Free Demo</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Download($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Download Source</a></div> <p class="mt-8 text-blue-200">🚀 No credit card • No setup fees • No user limits • No vendor lock-in</p></div></section>`;
}
export {
  _page as default
};
