import { E as spread_props, C as pop, A as push, P as head } from "../../../../../chunks/index2.js";
import { C as Chart_column } from "../../../../../chunks/chart-column.js";
import { C as Check } from "../../../../../chunks/check.js";
import { E as Eye } from "../../../../../chunks/eye.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { F as Funnel } from "../../../../../chunks/funnel.js";
import { C as Calendar } from "../../../../../chunks/calendar.js";
import { R as Refresh_cw } from "../../../../../chunks/refresh-cw.js";
import { T as Trending_up } from "../../../../../chunks/trending-up.js";
import { C as Clock } from "../../../../../chunks/clock.js";
import { T as Target } from "../../../../../chunks/target.js";
import { C as Circle_alert } from "../../../../../chunks/circle-alert.js";
import { G as Git_branch } from "../../../../../chunks/git-branch.js";
import { I as Icon } from "../../../../../chunks/Icon.js";
import { Z as Zap } from "../../../../../chunks/zap.js";
import { C as Code } from "../../../../../chunks/code.js";
import { h as html } from "../../../../../chunks/html.js";
function Bell($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      { "d": "M10.268 21a2 2 0 0 0 3.464 0" }
    ],
    [
      "path",
      {
        "d": "M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "bell" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  const schema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "BottleCRM",
    "image": "https://bottlecrm.io/logo.png",
    "url": "https://bottlecrm.io/features/sales-pipeline",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "All",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "Free open-source CRM with visual sales pipeline management, deal tracking, sales forecasting, and workflow automation.",
    "publisher": { "@type": "Organization", "name": "BottleCRM" }
  };
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Free Sales Pipeline CRM | Visual Deal Tracking &amp; Forecasting</title>`;
    $$payload2.out += `<meta name="description" content="Free CRM with visual sales pipeline, deal tracking, sales forecasting &amp; automation. Boost conversions with BottleCRM's open-source pipeline management."> <link rel="canonical" href="https://bottlecrm.io/features/sales-pipeline"> <meta property="og:title" content="Free Sales Pipeline CRM | Visual Deal Tracking"> <meta property="og:description" content="Open-source CRM for sales pipeline management, deal tracking, and forecasting. Try BottleCRM free."> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/features/sales-pipeline"> <meta property="og:image" content="https://bottlecrm.io/logo.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:title" content="Free Sales Pipeline CRM | Visual Deal Tracking"> <meta name="twitter:description" content="Open-source CRM for sales pipeline management, deal tracking, and forecasting. Try BottleCRM free."> <meta name="twitter:image" content="https://bottlecrm.io/logo.png"> ${html(`<script type="application/ld+json">${JSON.stringify(schema)}<\/script>`)}`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white relative overflow-hidden"><div class="absolute inset-0 bg-black/10"></div> <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div> <div class="max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:px-8 relative z-10"><div class="text-center max-w-4xl mx-auto"><div class="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6">`;
  Chart_column($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> <span class="text-sm font-medium">Coming Soon • Visual Sales Pipeline Management</span></div> <h1 class="text-4xl md:text-6xl font-extrabold tracking-tight mb-6 leading-tight">Visualize Your Sales Process with <span class="text-yellow-300">Smart Pipeline Management</span></h1> <p class="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">Transform your sales workflow with BottleCRM's intuitive visual pipeline. Track deals from first contact to closed-won, forecast revenue accurately, and automate your sales process for maximum efficiency.</p> <ul class="mb-8 space-y-3 text-lg max-w-2xl mx-auto"><li class="flex items-center justify-center md:justify-start">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Drag-and-drop deal management</span></li> <li class="flex items-center justify-center md:justify-start">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Automated sales forecasting</span></li> <li class="flex items-center justify-center md:justify-start">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Real-time collaboration and insights</span></li></ul> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4"><a href="/login" class="inline-flex items-center justify-center px-6 py-4 border border-transparent text-base font-semibold rounded-lg text-blue-700 bg-white hover:bg-gray-100 shadow-lg transition-all duration-200 hover:scale-105">`;
  Eye($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Preview Pipeline Demo</a> <a href="/features" class="inline-flex items-center justify-center px-6 py-4 border-2 border-white text-base font-semibold rounded-lg text-white hover:bg-white/10 transition-all duration-200">View All Features</a></div></div></div></section> <section class="py-16 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-12"><h2 class="text-3xl font-extrabold text-gray-900 mb-4">Real-time Sales Performance Dashboard</h2> <p class="text-lg text-gray-600">Monitor key sales metrics and track your pipeline performance at a glance</p></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Interactive Sales Pipeline Demo</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Experience how BottleCRM's visual pipeline makes deal management intuitive and efficient. Drag deals between stages and watch your sales process come to life.</p></div> <div class="bg-white rounded-2xl shadow-xl p-6 overflow-x-auto"><div class="flex space-x-6 min-w-max"><div class="flex-shrink-0 w-80"><div class="bg-gray-50 rounded-t-xl p-4 border-b-4 border-blue-500"><div class="flex items-center justify-between mb-2"><h3 class="font-bold text-gray-900 flex items-center"><div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div> Lead</h3> <span class="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">24</span></div> <p class="text-sm text-gray-600 mb-2">Initial prospects and incoming leads</p> <p class="text-lg font-bold text-gray-900">$120,000</p></div> <div class="bg-gray-50 rounded-b-xl p-4 min-h-96 space-y-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors group">`;
  Plus($$payload, {
    class: "w-5 h-5 mx-auto mb-1 group-hover:scale-110 transition-transform"
  });
  $$payload.out += `<!----> <span class="text-sm">Add Deal</span></button></div></div> <div class="flex-shrink-0 w-80"><div class="bg-gray-50 rounded-t-xl p-4 border-b-4 border-indigo-500"><div class="flex items-center justify-between mb-2"><h3 class="font-bold text-gray-900 flex items-center"><div class="w-3 h-3 rounded-full bg-indigo-500 mr-2"></div> Qualified</h3> <span class="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">18</span></div> <p class="text-sm text-gray-600 mb-2">Leads that meet qualification criteria</p> <p class="text-lg font-bold text-gray-900">$180,000</p></div> <div class="bg-gray-50 rounded-b-xl p-4 min-h-96 space-y-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors group">`;
  Plus($$payload, {
    class: "w-5 h-5 mx-auto mb-1 group-hover:scale-110 transition-transform"
  });
  $$payload.out += `<!----> <span class="text-sm">Add Deal</span></button></div></div> <div class="flex-shrink-0 w-80"><div class="bg-gray-50 rounded-t-xl p-4 border-b-4 border-purple-500"><div class="flex items-center justify-between mb-2"><h3 class="font-bold text-gray-900 flex items-center"><div class="w-3 h-3 rounded-full bg-purple-500 mr-2"></div> Proposal</h3> <span class="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">12</span></div> <p class="text-sm text-gray-600 mb-2">Proposals sent and under review</p> <p class="text-lg font-bold text-gray-900">$240,000</p></div> <div class="bg-gray-50 rounded-b-xl p-4 min-h-96 space-y-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors group">`;
  Plus($$payload, {
    class: "w-5 h-5 mx-auto mb-1 group-hover:scale-110 transition-transform"
  });
  $$payload.out += `<!----> <span class="text-sm">Add Deal</span></button></div></div> <div class="flex-shrink-0 w-80"><div class="bg-gray-50 rounded-t-xl p-4 border-b-4 border-orange-500"><div class="flex items-center justify-between mb-2"><h3 class="font-bold text-gray-900 flex items-center"><div class="w-3 h-3 rounded-full bg-orange-500 mr-2"></div> Negotiation</h3> <span class="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">8</span></div> <p class="text-sm text-gray-600 mb-2">Active contract negotiations</p> <p class="text-lg font-bold text-gray-900">$320,000</p></div> <div class="bg-gray-50 rounded-b-xl p-4 min-h-96 space-y-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors group">`;
  Plus($$payload, {
    class: "w-5 h-5 mx-auto mb-1 group-hover:scale-110 transition-transform"
  });
  $$payload.out += `<!----> <span class="text-sm">Add Deal</span></button></div></div> <div class="flex-shrink-0 w-80"><div class="bg-gray-50 rounded-t-xl p-4 border-b-4 border-green-500"><div class="flex items-center justify-between mb-2"><h3 class="font-bold text-gray-900 flex items-center"><div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div> Closed Won</h3> <span class="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">15</span></div> <p class="text-sm text-gray-600 mb-2">Successfully closed deals</p> <p class="text-lg font-bold text-gray-900">$450,000</p></div> <div class="bg-gray-50 rounded-b-xl p-4 min-h-96 space-y-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors group">`;
  Plus($$payload, {
    class: "w-5 h-5 mx-auto mb-1 group-hover:scale-110 transition-transform"
  });
  $$payload.out += `<!----> <span class="text-sm">Add Deal</span></button></div></div></div></div> <div class="mt-8 flex flex-wrap items-center justify-between bg-white rounded-xl shadow-lg p-6"><div class="flex items-center space-x-4 mb-4 lg:mb-0"><button class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">`;
  Funnel($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Filter Deals</button> <button class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">`;
  Calendar($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Date Range</button> <button class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">`;
  Refresh_cw($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Refresh</button></div> <div class="flex items-center space-x-4"><span class="text-sm text-gray-600">Pipeline Value:</span> <span class="text-2xl font-bold text-gray-900">$1,310,000</span> <span class="text-sm text-green-600 font-medium">+12.5% this month</span></div></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Powerful Pipeline Features</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Everything you need to manage your sales pipeline effectively and close more deals faster.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-gradient-to-r from-gray-50 to-blue-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="lg:flex lg:items-center lg:space-x-12"><div class="lg:w-1/2"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-8">Why Visual Pipeline Management Matters</h2> <p class="text-xl text-gray-700 mb-8 leading-relaxed">Transform your sales process from chaotic spreadsheets to organized, visual workflow management. See immediate improvements in deal closure rates and sales team productivity.</p> <div class="space-y-6"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="rounded-lg bg-green-100 p-3">`;
  Trending_up($$payload, { class: "w-6 h-6 text-green-600" });
  $$payload.out += `<!----></div></div> <div><h4 class="text-lg font-semibold text-gray-900 mb-2">Increase Conversion Rates by 30%</h4> <p class="text-gray-600">Visual pipeline management helps identify bottlenecks and optimize your sales process for better conversion rates.</p></div></div> <div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="rounded-lg bg-blue-100 p-3">`;
  Clock($$payload, { class: "w-6 h-6 text-blue-600" });
  $$payload.out += `<!----></div></div> <div><h4 class="text-lg font-semibold text-gray-900 mb-2">Reduce Sales Cycle by 25%</h4> <p class="text-gray-600">Automated workflows and clear process visualization help sales teams move deals through stages faster.</p></div></div> <div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="rounded-lg bg-purple-100 p-3">`;
  Target($$payload, { class: "w-6 h-6 text-purple-600" });
  $$payload.out += `<!----></div></div> <div><h4 class="text-lg font-semibold text-gray-900 mb-2">Improve Forecast Accuracy by 40%</h4> <p class="text-gray-600">Probability-based forecasting and historical data analysis provide more accurate revenue predictions.</p></div></div></div></div> <div class="mt-12 lg:mt-0 lg:w-1/2"><div class="bg-white rounded-2xl shadow-2xl p-8"><h3 class="text-2xl font-bold text-gray-900 mb-6">Pipeline Performance Impact</h3> <div class="space-y-6"><div><div class="flex justify-between items-center mb-2"><span class="text-sm font-medium text-gray-700">Deal Closure Rate</span> <span class="text-sm font-semibold text-green-600">+30%</span></div> <div class="w-full bg-gray-200 rounded-full h-3"><div class="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full" style="width: 85%"></div></div></div> <div><div class="flex justify-between items-center mb-2"><span class="text-sm font-medium text-gray-700">Sales Team Productivity</span> <span class="text-sm font-semibold text-blue-600">+45%</span></div> <div class="w-full bg-gray-200 rounded-full h-3"><div class="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full" style="width: 90%"></div></div></div> <div><div class="flex justify-between items-center mb-2"><span class="text-sm font-medium text-gray-700">Forecast Accuracy</span> <span class="text-sm font-semibold text-purple-600">+40%</span></div> <div class="w-full bg-gray-200 rounded-full h-3"><div class="bg-gradient-to-r from-purple-400 to-purple-600 h-3 rounded-full" style="width: 88%"></div></div></div> <div><div class="flex justify-between items-center mb-2"><span class="text-sm font-medium text-gray-700">Customer Follow-up</span> <span class="text-sm font-semibold text-orange-600">+60%</span></div> <div class="w-full bg-gray-200 rounded-full h-3"><div class="bg-gradient-to-r from-orange-400 to-orange-600 h-3 rounded-full" style="width: 95%"></div></div></div></div> <div class="mt-6 p-4 bg-gray-50 rounded-lg"><p class="text-sm text-gray-600 text-center"><strong class="text-gray-900">Average ROI:</strong> 320% within 6 months of implementation</p></div></div></div></div></div></section> <section class="py-16 bg-yellow-50 border-y border-yellow-200"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><div class="inline-flex items-center px-4 py-2 rounded-full bg-yellow-200 text-yellow-800 mb-6">`;
  Circle_alert($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> <span class="font-semibold">Development Update</span></div> <h2 class="text-3xl font-bold text-gray-900 mb-4">Sales Pipeline Features Coming Soon</h2> <p class="text-lg text-gray-700 mb-8 leading-relaxed">Our development team is actively working on the visual sales pipeline features. Want to contribute to the development or get early access? Join our open-source community and be part of building the future of free CRM software.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4"><a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 shadow-lg transition-all duration-200">`;
  Git_branch($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Contribute to Development</a> <a href="/contact" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 shadow-lg transition-all duration-200">`;
  Bell($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Get Notified When Ready</a></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-4xl md:text-5xl font-extrabold mb-6">Ready to Transform Your Sales Process?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">Start using BottleCRM today and experience the power of visual sales pipeline management. Join the community building the future of free CRM software.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  Zap($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Start Free Demo</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Code($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> View Source Code</a></div> <p class="mt-8 text-blue-200 text-lg">🚀 No credit card required • Open source • Self-host anywhere</p></div></section> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  pop();
}
export {
  _page as default
};
