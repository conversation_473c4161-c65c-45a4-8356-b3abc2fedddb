import { p as prisma } from "../../../../chunks/prisma.js";
async function load({ url }) {
  try {
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const pageSize = 5;
    const skip = (page - 1) * pageSize;
    const posts = await prisma.blogPost.findMany({
      // Temporarily showing all posts for testing
      where: {
        draft: false
        // Only show published posts
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: "desc"
      },
      skip,
      take: pageSize
    });
    const totalPosts = await prisma.blogPost.count({
      // Temporarily counting all posts for testing
      where: {
        draft: false
      }
    });
    const totalPages = Math.ceil(totalPosts / pageSize);
    return {
      posts,
      pagination: {
        page,
        pageSize,
        totalPosts,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    };
  } catch (error) {
    console.error("Error loading blog posts:", error);
    return {
      posts: [],
      pagination: {
        page: 1,
        pageSize: 5,
        totalPosts: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false
      },
      error: "Failed to load blog posts"
    };
  }
}
export {
  load
};
