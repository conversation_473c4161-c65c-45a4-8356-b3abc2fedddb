import { P as head, D as escape_html, J as attr, I as attr_class, R as stringify, M as bind_props, C as pop, A as push } from "../../../../chunks/index2.js";
import "../../../../chunks/client.js";
import { M as Message_circle } from "../../../../chunks/message-circle.js";
import { H as Headphones } from "../../../../chunks/headphones.js";
import { C as Code } from "../../../../chunks/code.js";
import { C as Check } from "../../../../chunks/check.js";
import { C as Circle_alert } from "../../../../chunks/circle-alert.js";
import { S as Send } from "../../../../chunks/send.js";
import { M as Mail } from "../../../../chunks/mail.js";
import { G as Github } from "../../../../chunks/github.js";
import { C as Clock } from "../../../../chunks/clock.js";
import { Z as Zap } from "../../../../chunks/zap.js";
import { S as Shield } from "../../../../chunks/shield.js";
import { U as Users } from "../../../../chunks/users.js";
import { D as Download } from "../../../../chunks/download.js";
import { h as html } from "../../../../chunks/html.js";
function _page($$payload, $$props) {
  push();
  let formData = {
    name: "",
    email: "",
    serviceType: "",
    message: ""
  };
  let isSubmitting = false;
  let form = $$props["form"];
  if (form && !form.success) {
    formData = {
      name: form.name || "",
      email: form.email || "",
      serviceType: form.serviceType || "",
      message: form.message || ""
    };
  }
  if (form?.success) {
    formData = {
      name: "",
      email: "",
      serviceType: "",
      message: ""
    };
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Contact BottleCRM | Free CRM &amp; Professional Services</title>`;
    $$payload2.out += `<meta name="description" content="Contact BottleCRM for free CRM software, setup, hosting, customization, and expert support. Get started or request professional CRM services today."> <meta name="keywords" content="free crm, open source crm, bottlecrm, crm setup, crm hosting, crm customization, crm support, crm development, crm training"> <link rel="canonical" href="https://bottlecrm.io/contact"> <meta property="og:title" content="Contact BottleCRM | Free CRM &amp; Professional Services"> <meta property="og:description" content="Contact BottleCRM for free CRM software, setup, hosting, customization, and expert support. Get started or request professional CRM services today."> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/contact"> <meta property="og:image" content="https://bottlecrm.io/og-image.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:title" content="Contact BottleCRM | Free CRM &amp; Professional Services"> <meta name="twitter:description" content="Contact BottleCRM for free CRM software, setup, hosting, customization, and expert support. Get started or request professional CRM services today."> <meta name="twitter:image" content="https://bottlecrm.io/og-image.png"> ${html(`
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "BottleCRM",
      "url": "https://bottlecrm.io",
      "logo": "https://bottlecrm.io/logo.png",
      "contactPoint": [{
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer support",
        "areaServed": "Global",
        "availableLanguage": ["English"]
      }]
    }
    <\/script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "name": "Contact BottleCRM",
      "description": "Contact BottleCRM for free CRM software, setup, hosting, customization, and expert support.",
      "url": "https://bottlecrm.io/contact"
    }
    <\/script>
  `)}`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center max-w-4xl mx-auto"><div class="inline-flex items-center px-4 py-2 rounded-full bg-green-500 text-white text-sm font-semibold mb-6">`;
  Message_circle($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Free Software • Professional Support Available</div> <h1 class="text-4xl md:text-6xl font-extrabold mb-6">Get Expert Help with <span class="text-green-300">BottleCRM</span></h1> <p class="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">The software is free forever. Get professional setup, hosting, customization, and training services to accelerate your CRM implementation and maximize your ROI.</p> <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"><div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">`;
  Message_circle($$payload, { class: "w-8 h-8 text-green-300 mx-auto mb-2" });
  $$payload.out += `<!----> <div class="text-sm font-medium">Free Community Support</div> <div class="text-xs text-blue-200">GitHub</div></div> <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">`;
  Headphones($$payload, { class: "w-8 h-8 text-yellow-300 mx-auto mb-2" });
  $$payload.out += `<!----> <div class="text-sm font-medium">Professional Setup</div> <div class="text-xs text-blue-200">Starting at $197</div></div> <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">`;
  Code($$payload, { class: "w-8 h-8 text-purple-300 mx-auto mb-2" });
  $$payload.out += `<!----> <div class="text-sm font-medium">Custom Development</div> <div class="text-xs text-blue-200">Bespoke solutions</div></div></div></div></div></section> <section class="py-16 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-12"><h2 class="text-3xl font-extrabold text-gray-900 mb-4">Choose Your Support Level</h2> <p class="text-xl text-gray-600">From free community help to enterprise-grade professional services</p></div> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="grid gap-12 lg:grid-cols-2"><div><h2 class="text-3xl font-extrabold text-gray-900 mb-4">Get in Touch</h2> <p class="text-gray-600 mb-8">Tell us about your project and we'll get back to you within 24 hours.</p> `;
  if (form?.success) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8"><div class="flex items-center">`;
    Check($$payload, { class: "w-6 h-6 text-green-500 mr-3" });
    $$payload.out += `<!----> <div><h3 class="text-lg font-semibold text-green-800">Message Sent Successfully!</h3> <p class="text-green-700 mt-1">${escape_html(form.message)}</p></div></div></div>`;
  } else if (form?.error) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8"><div class="flex items-center">`;
    Circle_alert($$payload, { class: "w-6 h-6 text-red-500 mr-3" });
    $$payload.out += `<!----> <div><h3 class="text-lg font-semibold text-red-800">Error</h3> <p class="text-red-700 mt-1">${escape_html(form.error)}</p></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" class="space-y-6"><div class="grid gap-6 md:grid-cols-2"><div><label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name *</label> <input type="text" id="name" name="name"${attr("value", formData.name)}${attr_class(`w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${stringify(form?.errors?.name ? "border-red-500" : "")}`)} placeholder="John Doe"> `;
  if (form?.errors?.name) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 text-sm mt-1">${escape_html(form.errors.name)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label> <input type="email" id="email" name="email"${attr("value", formData.email)}${attr_class(`w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${stringify(form?.errors?.email ? "border-red-500" : "")}`)} placeholder="<EMAIL>"> `;
  if (form?.errors?.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 text-sm mt-1">${escape_html(form.errors.email)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div><label class="block text-sm font-medium text-gray-700 mb-2">What can we help you with? *</label> <div class="grid gap-3 md:grid-cols-2"><label${attr_class(`flex items-start p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 ${stringify(formData.serviceType === "professional-setup" ? "border-blue-500 bg-blue-50" : "")}`)}><input type="radio" name="serviceType"${attr("checked", formData.serviceType === "professional-setup", true)} value="professional-setup" class="mt-1 mr-3"> <div><div class="font-medium text-gray-900 text-sm">Professional Setup ($197)</div> <div class="text-xs text-gray-600">Expert installation &amp; setup</div></div></label> <label${attr_class(`flex items-start p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 ${stringify(formData.serviceType === "custom-development" ? "border-blue-500 bg-blue-50" : "")}`)}><input type="radio" name="serviceType"${attr("checked", formData.serviceType === "custom-development", true)} value="custom-development" class="mt-1 mr-3"> <div><div class="font-medium text-gray-900 text-sm">Custom Development</div> <div class="text-xs text-gray-600">Bespoke features &amp; integrations</div></div></label> <label${attr_class(`flex items-start p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 ${stringify(formData.serviceType === "hosting-management" ? "border-blue-500 bg-blue-50" : "")}`)}><input type="radio" name="serviceType"${attr("checked", formData.serviceType === "hosting-management", true)} value="hosting-management" class="mt-1 mr-3"> <div><div class="font-medium text-gray-900 text-sm">Managed Hosting</div> <div class="text-xs text-gray-600">We handle hosting &amp; maintenance</div></div></label> <label${attr_class(`flex items-start p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 ${stringify(formData.serviceType === "other" ? "border-blue-500 bg-blue-50" : "")}`)}><input type="radio" name="serviceType"${attr("checked", formData.serviceType === "other", true)} value="other" class="mt-1 mr-3"> <div><div class="font-medium text-gray-900 text-sm">Other / Not Sure</div> <div class="text-xs text-gray-600">Let's discuss your needs</div></div></label></div> `;
  if (form?.errors?.serviceType) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 text-sm mt-1">${escape_html(form.errors.serviceType)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="message" class="block text-sm font-medium text-gray-700 mb-2">Tell us about your project *</label> <textarea id="message" name="message" rows="5"${attr_class(`w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${stringify(form?.errors?.message ? "border-red-500" : "")}`)} placeholder="Describe your project, requirements, timeline, team size, or any questions you have...">`;
  const $$body = escape_html(formData.message);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea> `;
  if (form?.errors?.message) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 text-sm mt-1">${escape_html(form.errors.message)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <button type="submit"${attr("disabled", isSubmitting, true)} class="w-full inline-flex items-center justify-center px-6 py-4 text-lg font-semibold rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">`;
  {
    $$payload.out += "<!--[!-->";
    Send($$payload, { class: "w-5 h-5 mr-2" });
    $$payload.out += `<!----> Send Message`;
  }
  $$payload.out += `<!--]--></button> <p class="text-sm text-gray-500 text-center">We typically respond within 24 hours during business days</p></form></div> <div class="lg:pl-8"><div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 mb-8"><h3 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3> <div class="space-y-4"><div class="flex items-start">`;
  Mail($$payload, { class: "w-6 h-6 text-blue-600 mr-4 mt-1" });
  $$payload.out += `<!----> <div><div class="font-semibold text-gray-900">Email</div> <div class="text-gray-600"><EMAIL></div> <div class="text-sm text-gray-500">Response within 24 hours</div></div></div> <div class="flex items-start">`;
  Github($$payload, { class: "w-6 h-6 text-blue-600 mr-4 mt-1" });
  $$payload.out += `<!----> <div><div class="font-semibold text-gray-900">GitHub Issues</div> <div class="text-gray-600">Bug reports &amp; feature requests</div> <a href="https://github.com/MicroPyramid/opensource-startup-crm/issues" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-700 text-sm">Open an issue →</a></div></div> <div class="flex items-start">`;
  Clock($$payload, { class: "w-6 h-6 text-blue-600 mr-4 mt-1" });
  $$payload.out += `<!----> <div><div class="font-semibold text-gray-900">Business Hours</div> <div class="text-gray-600">Monday - Friday</div> <div class="text-sm text-gray-500">9 AM - 6 PM IST</div></div></div></div></div> <div><h4 class="text-lg font-bold text-gray-900 mb-4">Frequently Asked Questions</h4> <div class="space-y-3"><details class="bg-white rounded-lg border border-gray-200 p-4"><summary class="font-medium text-gray-900 cursor-pointer">How quickly can you set up BottleCRM for my business?</summary> <p class="text-gray-600 text-sm mt-2 leading-relaxed">Basic setup can be completed within 24-48 hours. Professional Setup typically takes 3-5 business days, while Enterprise Setup with custom features may take 1-2 weeks depending on complexity.</p></details> <details class="bg-white rounded-lg border border-gray-200 p-4"><summary class="font-medium text-gray-900 cursor-pointer">Do you provide training for my team?</summary> <p class="text-gray-600 text-sm mt-2 leading-relaxed">Yes! Training is included in our Professional and Enterprise Setup packages. We also offer dedicated training sessions at $97-197/hour for teams who need additional support.</p></details> <details class="bg-white rounded-lg border border-gray-200 p-4"><summary class="font-medium text-gray-900 cursor-pointer">Can you migrate data from our current CRM?</summary> <p class="text-gray-600 text-sm mt-2 leading-relaxed">We're currently developing data migration scripts for popular CRM platforms including Salesforce, HubSpot, and Pipedrive. These tools will be available soon. In the meantime, we can help you manually export/import your data and provide guidance on the best migration approach for your specific needs.</p></details> <details class="bg-white rounded-lg border border-gray-200 p-4"><summary class="font-medium text-gray-900 cursor-pointer">What if I need ongoing support after setup?</summary> <p class="text-gray-600 text-sm mt-2 leading-relaxed">We offer various ongoing support options from monthly managed hosting ($97-297/month) to hourly consultation ($97-197/hour). You can also access our free community support anytime.</p></details></div></div></div></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Why Choose Our Professional Services?</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">While BottleCRM is free, our professional services save you time and ensure optimal implementation.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4"><div class="text-center"><div class="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">`;
  Zap($$payload, { class: "w-8 h-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-lg font-bold text-gray-900 mb-2">Faster Implementation</h3> <p class="text-gray-600">Get up and running in days, not weeks. Our experts handle the technical setup.</p></div> <div class="text-center"><div class="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">`;
  Shield($$payload, { class: "w-8 h-8 text-green-600" });
  $$payload.out += `<!----></div> <h3 class="text-lg font-bold text-gray-900 mb-2">Best Practices</h3> <p class="text-gray-600">Benefit from our experience with dozens of CRM implementations.</p></div> <div class="text-center"><div class="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">`;
  Users($$payload, { class: "w-8 h-8 text-purple-600" });
  $$payload.out += `<!----></div> <h3 class="text-lg font-bold text-gray-900 mb-2">Team Training</h3> <p class="text-gray-600">Ensure your team knows how to use BottleCRM effectively from day one.</p></div> <div class="text-center"><div class="bg-yellow-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">`;
  Headphones($$payload, { class: "w-8 h-8 text-yellow-600" });
  $$payload.out += `<!----></div> <h3 class="text-lg font-bold text-gray-900 mb-2">Ongoing Support</h3> <p class="text-gray-600">Get priority support when you need help or have questions.</p></div></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-4xl md:text-5xl font-extrabold mb-6">Ready to Get Started?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">The software is free forever. Professional services help you implement it faster and more effectively.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6"><a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  Download($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Try BottleCRM Free</a> <button class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Send($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Get Professional Help</button></div></div></section>`;
  bind_props($$props, { form });
  pop();
}
export {
  _page as default
};
