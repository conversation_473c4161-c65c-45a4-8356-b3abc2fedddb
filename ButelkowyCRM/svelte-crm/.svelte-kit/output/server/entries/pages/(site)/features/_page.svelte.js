import { E as spread_props, C as pop, A as push, P as head } from "../../../../chunks/index2.js";
import { Z as Zap } from "../../../../chunks/zap.js";
import { C as Check } from "../../../../chunks/check.js";
import { U as Users } from "../../../../chunks/users.js";
import { C as Clock } from "../../../../chunks/clock.js";
import { C as Chart_column } from "../../../../chunks/chart-column.js";
import { S as Square_check_big } from "../../../../chunks/square-check-big.js";
import { M as Message_square } from "../../../../chunks/message-square.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { I as Icon } from "../../../../chunks/Icon.js";
import { T as Target } from "../../../../chunks/target.js";
import { F as File_text } from "../../../../chunks/file-text.js";
import { U as User_check } from "../../../../chunks/user-check.js";
import { M as Mail } from "../../../../chunks/mail.js";
import { S as Smartphone } from "../../../../chunks/smartphone.js";
import { S as Shield } from "../../../../chunks/shield.js";
import { C as Code } from "../../../../chunks/code.js";
import { S as Server } from "../../../../chunks/server.js";
import { S as Settings } from "../../../../chunks/settings.js";
import { G as Globe } from "../../../../chunks/globe.js";
import { D as Database } from "../../../../chunks/database.js";
import { P as Palette } from "../../../../chunks/palette.js";
import { G as Git_branch } from "../../../../chunks/git-branch.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { L as Lock } from "../../../../chunks/lock.js";
function Chart_pie($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z"
      }
    ],
    [
      "path",
      { "d": "M21.21 15.89A10 10 0 1 1 8 2.83" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "chart-pie" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload) {
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>BottleCRM Features: Free &amp; Open Source CRM Software</title>`;
    $$payload2.out += `<meta name="description" content="Explore BottleCRM's powerful features: contact management, sales pipeline, task automation, analytics &amp; more. A free, open-source CRM for growing businesses."> <meta name="keywords" content="bottlecrm features, free crm features, open source crm, crm software features, contact management, sales pipeline, task automation, crm analytics, business management software"> <meta property="og:title" content="BottleCRM Features: Free &amp; Open Source CRM Software"> <meta property="og:description" content="Discover BottleCRM's comprehensive features: contact management, sales automation, analytics &amp; more. Enterprise-grade, free, and open-source."> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/features"> <meta property="og:image" content="https://bottlecrm.io/logo_social.png"> <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "BottleCRM Features",
      "description": "Explore BottleCRM's powerful features: contact management, sales pipeline, task automation, analytics & more. A free, open-source CRM for growing businesses.",
      "url": "https://bottlecrm.io/features",
      "isPartOf": {
        "@type": "WebSite",
        "name": "BottleCRM",
        "url": "https://bottlecrm.io"
      },
      "mainEntity": {
        "@type": "SoftwareApplication",
        "name": "BottleCRM",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web (Cross-platform)",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "description": "BottleCRM is a free and open-source Customer Relationship Management (CRM) software offering enterprise-grade features like contact management, sales pipeline, task automation, analytics, and invoicing.",
        "url": "https://bottlecrm.io",
        "softwareVersion": "1.0", // Update as versions change
        "keywords": "crm, free crm, open source crm, contact management, sales pipeline, business software",
        "featureList": [
          "Advanced Contact Management",
          "Sales Pipeline Management (Coming Soon)",
          "Task & Activity Management",
          "Communication Center (Coming Soon)",
          "Real-time Analytics Dashboard (Coming Soon)",
          "Advanced Reporting System (Coming Soon)",
          "Invoice & Billing Management (Coming Soon)",
          "Customer Support Center (Coming Soon)",
          "Marketing Campaign Management (Coming Soon)",
          "Mobile-First Design",
          "Data Security & Privacy",
          "API & Integrations (Coming Soon)",
          "Self-Hosting & Deployment",
          "Complete Customization",
          "Multi-language Support (Coming Soon)"
        ],
        "provider": {
          "@type": "Organization",
          "name": "BottleCRM",
          "url": "https://bottlecrm.io",
          "logo": "https://bottlecrm.io/logo.png" // Add a relevant logo URL
        }
      },
      "publisher": {
        "@type": "Organization",
        "name": "BottleCRM",
        "url": "https://bottlecrm.io",
        "logo": {
          "@type": "ImageObject",
          "url": "https://bottlecrm.io/logo.png" // Add a relevant logo URL
        }
      }
    }
  <\/script>`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white relative overflow-hidden"><div class="absolute inset-0 bg-black/10"></div> <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div> <div class="max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:px-8 relative z-10"><div class="text-center max-w-4xl mx-auto"><div class="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6">`;
  Zap($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> <span class="text-sm font-medium">Enterprise Features • Zero Cost • Open Source</span></div> <h1 class="text-4xl md:text-6xl font-extrabold tracking-tight mb-6 leading-tight">Enterprise CRM Features at <span class="text-yellow-300">Zero Cost</span></h1> <p class="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">Discover powerful features that rival premium CRM solutions. From contact management to advanced analytics, everything you need without subscription fees or vendor lock-in.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4"><a href="/login" class="inline-flex items-center justify-center px-6 py-4 border border-transparent text-base font-semibold rounded-lg text-blue-700 bg-white hover:bg-gray-100 shadow-lg transition-all duration-200 hover:scale-105">`;
  Check($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Try All Features Free</a> <a href="#features" class="inline-flex items-center justify-center px-6 py-4 border-2 border-white text-base font-semibold rounded-lg text-white hover:bg-white/10 transition-all duration-200">Explore Features Below</a></div></div></div></section> <section class="py-20 bg-gray-50" id="features"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Professional CRM Feature Set</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">BottleCRM provides enterprise-grade functionality typically found in premium CRM solutions. All features are included in our free, open-source platform with no hidden costs or limitations.</p></div> <div class="mb-20"><div class="text-center mb-12"><h3 class="text-3xl font-bold text-gray-900 mb-4">Core CRM Features</h3> <p class="text-lg text-gray-600">Essential customer relationship management tools</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-2"><div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-green-100 text-green-800 border-green-200">`;
  Check($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Available Now</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Users($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Advanced Contact Management</h4> <p class="text-gray-600 mb-6 leading-relaxed">Centralize all customer information in one place with our comprehensive contact management system. Store detailed customer profiles, track interaction history, manage contact segmentation, and build 360-degree customer views.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Unlimited contact storage</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom fields and tags</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Contact segmentation</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Interaction timeline</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Duplicate detection</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Import/Export tools</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Advanced search &amp; filtering</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Contact scoring</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Chart_column($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Sales Pipeline Management</h4> <p class="text-gray-600 mb-6 leading-relaxed">Visualize and manage your entire sales process with intuitive pipeline management. Track deals from initial contact to closing, forecast revenue, and optimize conversion rates with automated workflows.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Drag-and-drop pipeline</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom deal stages</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Revenue forecasting</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Deal probability tracking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Sales velocity metrics</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Win/loss analysis</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Activity reminders</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Team collaboration</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-green-100 text-green-800 border-green-200">`;
  Check($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Available Now</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Square_check_big($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Task &amp; Activity Management</h4> <p class="text-gray-600 mb-6 leading-relaxed">Never miss a follow-up with comprehensive task management. Schedule activities, set reminders, assign tasks to team members, and track completion rates to ensure maximum productivity.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Task scheduling &amp; reminders</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Activity logging</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Team task assignment</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Priority management</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Calendar integration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Recurring tasks</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Task templates</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Progress tracking</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Message_square($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Communication Center</h4> <p class="text-gray-600 mb-6 leading-relaxed">Manage all customer communications from one central hub. Track emails, phone calls, meetings, and notes. Maintain complete conversation history and ensure no communication falls through the cracks.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Email integration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Call logging</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Meeting notes</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Communication timeline</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Email templates</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Auto-response setup</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Message scheduling</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Team communication</span></div></div></div></div></div> <div class="mb-20"><div class="text-center mb-12"><h3 class="text-3xl font-bold text-gray-900 mb-4">Analytics &amp; Reporting</h3> <p class="text-lg text-gray-600">Business intelligence and performance insights</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-2"><div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Trending_up($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Real-time Analytics Dashboard</h4> <p class="text-gray-600 mb-6 leading-relaxed">Get instant insights into your business performance with customizable dashboards. Monitor key metrics, track trends, and make data-driven decisions to accelerate growth.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Customizable dashboards</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Real-time data updates</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Key performance indicators</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Visual chart library</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Goal tracking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Comparative analysis</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Export capabilities</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Mobile dashboard access</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Chart_pie($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Advanced Reporting System</h4> <p class="text-gray-600 mb-6 leading-relaxed">Generate comprehensive reports on sales performance, customer behavior, and business metrics. Create custom reports, schedule automated delivery, and share insights with stakeholders.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom report builder</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Automated report scheduling</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Multiple export formats</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Interactive charts</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Data filtering options</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Report templates</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Team sharing</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Historical comparisons</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Target($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Sales Performance Analytics</h4> <p class="text-gray-600 mb-6 leading-relaxed">Track and analyze sales team performance with detailed metrics. Monitor individual and team achievements, identify top performers, and optimize sales strategies.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Individual performance tracking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Team leaderboards</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Conversion rate analysis</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Activity reports</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Revenue attribution</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Performance benchmarking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Commission calculations</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Sales coaching insights</span></div></div></div></div></div> <div class="mb-20"><div class="text-center mb-12"><h3 class="text-3xl font-bold text-gray-900 mb-4">Business Management</h3> <p class="text-lg text-gray-600">Comprehensive business process automation</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-2"><div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  File_text($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Invoice &amp; Billing Management</h4> <p class="text-gray-600 mb-6 leading-relaxed">Streamline your billing process with integrated invoicing. Create professional invoices, track payments, manage recurring billing, and integrate with payment gateways.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Professional invoice templates</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Automated invoice generation</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Payment tracking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Recurring billing setup</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Payment gateway integration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Tax calculations</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Late payment reminders</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Financial reporting</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  User_check($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Customer Support Center</h4> <p class="text-gray-600 mb-6 leading-relaxed">Provide exceptional customer support with integrated ticketing system. Track support requests, manage resolution workflows, and maintain customer satisfaction.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Ticket management system</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Support workflow automation</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Customer satisfaction surveys</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Knowledge base integration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Multi-channel support</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>SLA tracking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Escalation rules</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Support analytics</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Mail($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Marketing Campaign Management</h4> <p class="text-gray-600 mb-6 leading-relaxed">Plan, execute, and track marketing campaigns with integrated tools. Manage email campaigns, track ROI, and nurture leads through automated marketing workflows.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Email campaign builder</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Marketing automation</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Lead nurturing workflows</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Campaign performance tracking</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>A/B testing</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Landing page integration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Social media management</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>ROI measurement</span></div></div></div></div></div> <div class="mb-20"><div class="text-center mb-12"><h3 class="text-3xl font-bold text-gray-900 mb-4">Technical Features</h3> <p class="text-lg text-gray-600">Modern technology and infrastructure capabilities</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-2"><div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-green-100 text-green-800 border-green-200">`;
  Check($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Available Now</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Smartphone($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Mobile-First Design</h4> <p class="text-gray-600 mb-6 leading-relaxed">Access your CRM from anywhere with our responsive, mobile-optimized interface. Work offline, sync data automatically, and manage your business on-the-go.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Responsive web design</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Offline data access</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Automatic synchronization</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Touch-optimized interface</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Mobile notifications</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Cross-device compatibility</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Progressive web app</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Fast loading times</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-green-100 text-green-800 border-green-200">`;
  Check($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Available Now</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Shield($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Data Security &amp; Privacy</h4> <p class="text-gray-600 mb-6 leading-relaxed">Protect your business data with enterprise-grade security features. Enjoy encrypted data storage, user access controls, and complete data ownership through self-hosting.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>End-to-end encryption</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Role-based access control</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Data backup &amp; recovery</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Audit trail logging</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>GDPR compliance tools</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Two-factor authentication</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>IP restrictions</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Security monitoring</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Code($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">API &amp; Integrations</h4> <p class="text-gray-600 mb-6 leading-relaxed">Connect BottleCRM with your existing tools through our comprehensive API. Build custom integrations, automate workflows, and create a unified business ecosystem.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>RESTful API access</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Webhook support</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Third-party integrations</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom field mapping</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Data import/export</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Zapier integration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>OAuth authentication</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>API documentation</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-green-100 text-green-800 border-green-200">`;
  Check($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Available Now</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Server($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Self-Hosting &amp; Deployment</h4> <p class="text-gray-600 mb-6 leading-relaxed">Deploy BottleCRM on your own infrastructure for complete control. Choose from various hosting options, scale as needed, and maintain full data sovereignty.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Docker containerization</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Cloud deployment guides</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Database flexibility</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Scalable architecture</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Load balancing support</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Automated backups</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Environment configuration</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Performance monitoring</span></div></div></div></div></div> <div class="mb-20"><div class="text-center mb-12"><h3 class="text-3xl font-bold text-gray-900 mb-4">Customization &amp; Flexibility</h3> <p class="text-lg text-gray-600">Adapt BottleCRM to your unique business needs</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-2"><div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-green-100 text-green-800 border-green-200">`;
  Check($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Available Now</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Settings($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Complete Customization</h4> <p class="text-gray-600 mb-6 leading-relaxed">Tailor BottleCRM to your exact business needs with extensive customization options. Modify workflows, create custom fields, and adapt the interface to match your processes.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom field creation</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Workflow customization</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>UI theme customization</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Form builder</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom data validation</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Business rule engine</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Layout customization</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Brand integration</span></div></div></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border bg-yellow-100 text-yellow-800 border-yellow-200">`;
  Clock($$payload, { class: "w-3 h-3 mr-1" });
  $$payload.out += `<!----> Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Globe($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h4 class="text-2xl font-bold text-gray-900 mb-4">Multi-language Support</h4> <p class="text-gray-600 mb-6 leading-relaxed">Use BottleCRM in your preferred language with comprehensive internationalization support. Easily add new languages and adapt to local business requirements.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"><div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Multiple language support</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>RTL text support</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Currency localization</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Date/time formatting</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Number formatting</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Timezone handling</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Cultural adaptations</span></div> <div class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Translation management</span></div></div></div></div></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Built with Modern Technology</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">BottleCRM is built using cutting-edge technologies to ensure performance, security, and maintainability.</p></div> <div class="grid grid-cols-2 md:grid-cols-4 gap-8"><div class="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">`;
  Code($$payload, { class: "w-12 h-12 text-blue-600 mx-auto mb-4" });
  $$payload.out += `<!----> <h4 class="font-bold text-gray-900 mb-2">SvelteKit 2.21+</h4> <p class="text-sm text-gray-600">Modern web framework</p></div> <div class="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">`;
  Database($$payload, { class: "w-12 h-12 text-green-600 mx-auto mb-4" });
  $$payload.out += `<!----> <h4 class="font-bold text-gray-900 mb-2">Prisma ORM</h4> <p class="text-sm text-gray-600">Database management</p></div> <div class="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">`;
  Palette($$payload, {
    class: "w-12 h-12 text-purple-600 mx-auto mb-4"
  });
  $$payload.out += `<!----> <h4 class="font-bold text-gray-900 mb-2">TailwindCSS 4.1+</h4> <p class="text-sm text-gray-600">Modern styling</p></div> <div class="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">`;
  Git_branch($$payload, {
    class: "w-12 h-12 text-orange-600 mx-auto mb-4"
  });
  $$payload.out += `<!----> <h4 class="font-bold text-gray-900 mb-2">Open Source</h4> <p class="text-sm text-gray-600">MIT License</p></div></div></div></section> <section class="py-20 bg-gradient-to-r from-gray-50 to-blue-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Compare Feature Availability</h2> <p class="text-xl text-gray-600">See what's available now and what's coming soon in BottleCRM development roadmap.</p></div> <div class="grid gap-8 lg:grid-cols-2"><div class="bg-white rounded-2xl shadow-xl p-8"><div class="flex items-center mb-6"><div class="rounded-lg bg-green-100 p-3 mr-4">`;
  Check($$payload, { class: "w-8 h-8 text-green-600" });
  $$payload.out += `<!----></div> <div><h3 class="text-2xl font-bold text-gray-900">Available Now</h3> <p class="text-gray-600">Ready to use features</p></div></div> <ul class="space-y-3"><li class="flex items-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mr-3" });
  $$payload.out += `<!----> <span>Advanced Contact Management</span></li> <li class="flex items-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mr-3" });
  $$payload.out += `<!----> <span>Task &amp; Activity Management</span></li> <li class="flex items-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mr-3" });
  $$payload.out += `<!----> <span>Mobile-First Design</span></li> <li class="flex items-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mr-3" });
  $$payload.out += `<!----> <span>Data Security &amp; Privacy</span></li> <li class="flex items-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mr-3" });
  $$payload.out += `<!----> <span>Self-Hosting &amp; Deployment</span></li> <li class="flex items-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mr-3" });
  $$payload.out += `<!----> <span>Complete Customization</span></li></ul></div> <div class="bg-white rounded-2xl shadow-xl p-8"><div class="flex items-center mb-6"><div class="rounded-lg bg-yellow-100 p-3 mr-4">`;
  Clock($$payload, { class: "w-8 h-8 text-yellow-600" });
  $$payload.out += `<!----></div> <div><h3 class="text-2xl font-bold text-gray-900">Coming Soon</h3> <p class="text-gray-600">In active development</p></div></div> <ul class="space-y-3"><li class="flex items-center">`;
  Clock($$payload, { class: "w-5 h-5 text-yellow-500 mr-3" });
  $$payload.out += `<!----> <span>Sales Pipeline Management</span></li> <li class="flex items-center">`;
  Clock($$payload, { class: "w-5 h-5 text-yellow-500 mr-3" });
  $$payload.out += `<!----> <span>Real-time Analytics Dashboard</span></li> <li class="flex items-center">`;
  Clock($$payload, { class: "w-5 h-5 text-yellow-500 mr-3" });
  $$payload.out += `<!----> <span>Invoice &amp; Billing Management</span></li> <li class="flex items-center">`;
  Clock($$payload, { class: "w-5 h-5 text-yellow-500 mr-3" });
  $$payload.out += `<!----> <span>Communication Center</span></li> <li class="flex items-center">`;
  Clock($$payload, { class: "w-5 h-5 text-yellow-500 mr-3" });
  $$payload.out += `<!----> <span>API &amp; Integrations</span></li> <li class="flex items-center">`;
  Clock($$payload, { class: "w-5 h-5 text-yellow-500 mr-3" });
  $$payload.out += `<!----> <span>Multi-language Support</span></li></ul></div></div> <div class="text-center mt-12"><p class="text-lg text-gray-600 mb-6">Want to contribute to feature development or request a specific feature?</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4"><a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 shadow-lg transition-all duration-200">`;
  Git_branch($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Contribute on GitHub</a> <a href="/contact" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 shadow-lg transition-all duration-200">`;
  Message_square($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Request Feature</a></div></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Why Choose BottleCRM?</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Unlike traditional CRM solutions, BottleCRM offers enterprise-grade features without the enterprise price tag.</p></div> <div class="grid gap-8 lg:grid-cols-3"><div class="text-center p-8 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl"><div class="rounded-xl bg-green-100 p-4 inline-block mb-6">`;
  Dollar_sign($$payload, { class: "h-8 w-8 text-green-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Cost-Effective Solution</h3> <p class="text-gray-600 mb-6">Save thousands on CRM costs while getting professional features. No per-user fees, no monthly subscriptions, no surprise charges.</p> <ul class="text-left space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>$0 monthly fees</span></li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Unlimited users</span></li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>No feature restrictions</span></li></ul></div> <div class="text-center p-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl"><div class="rounded-xl bg-blue-100 p-4 inline-block mb-6">`;
  Lock($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Complete Data Control</h3> <p class="text-gray-600 mb-6">Your data stays yours. Self-host on your infrastructure with full control over security, privacy, and compliance.</p> <ul class="text-left space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Self-hosted deployment</span></li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>No vendor lock-in</span></li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>GDPR compliance ready</span></li></ul></div> <div class="text-center p-8 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl"><div class="rounded-xl bg-purple-100 p-4 inline-block mb-6">`;
  Code($$payload, { class: "h-8 w-8 text-purple-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Unlimited Customization</h3> <p class="text-gray-600 mb-6">Modify and extend BottleCRM to fit your exact needs. Open-source means no limitations on customization.</p> <ul class="text-left space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Full source code access</span></li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Custom integrations</span></li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, {
    class: "w-4 h-4 text-green-500 mr-2 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Community support</span></li></ul></div></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-4xl md:text-5xl font-extrabold mb-6">Ready to Experience Professional CRM Features?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">Join businesses that have chosen BottleCRM for enterprise-grade features without enterprise costs.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  Zap($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Start Using All Features Free</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Code($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> View Source Code</a></div> <p class="mt-8 text-blue-200 text-lg">🚀 No subscription fees • Enterprise features included • Deploy anywhere</p></div></section>`;
}
export {
  _page as default
};
