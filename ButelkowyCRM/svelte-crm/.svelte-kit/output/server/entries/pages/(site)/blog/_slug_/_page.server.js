import { p as prisma } from "../../../../../chunks/prisma.js";
import { e as error } from "../../../../../chunks/index.js";
async function load({ params }) {
  const { slug } = params;
  try {
    const post = await prisma.blogPost.findUnique({
      where: {
        slug,
        draft: false
        // Only show published posts
      },
      include: {
        contentBlocks: {
          orderBy: { displayOrder: "asc" }
        }
      }
    });
    if (!post) {
      throw error(404, "Blog post not found");
    }
    return { post };
  } catch (err) {
    console.error("Error loading blog post:", err);
    throw error(404, "Blog post not found");
  }
}
export {
  load
};
