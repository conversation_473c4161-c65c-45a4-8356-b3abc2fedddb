import { E as spread_props, C as pop, A as push, P as head, I as attr_class, J as attr, Q as slot, D as escape_html, R as stringify } from "../../../chunks/index2.js";
import { p as page } from "../../../chunks/index3.js";
/* empty css                  */
import { i as imgLogo } from "../../../chunks/logo.js";
import "../../../chunks/client.js";
import { G as Github } from "../../../chunks/github.js";
import { I as Icon } from "../../../chunks/Icon.js";
import { X } from "../../../chunks/x.js";
import { M as Menu } from "../../../chunks/menu.js";
import { S as Square_check_big } from "../../../chunks/square-check-big.js";
import { D as Dollar_sign } from "../../../chunks/dollar-sign.js";
import { S as Square_pen } from "../../../chunks/square-pen.js";
import { M as Message_circle } from "../../../chunks/message-circle.js";
import { T as Twitter, L as Linkedin } from "../../../chunks/twitter.js";
function Arrow_right($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M5 12h14" }],
    ["path", { "d": "m12 5 7 7-7 7" }]
  ];
  Icon($$payload, spread_props([
    { name: "arrow-right" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Video($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"
      }
    ],
    [
      "rect",
      {
        "x": "2",
        "y": "6",
        "width": "14",
        "height": "12",
        "rx": "2"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "video" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _layout($$payload, $$props) {
  push();
  let navbarClass;
  let isMenuOpen = false;
  if (page.url.pathname) {
    isMenuOpen = false;
  }
  navbarClass = "bg-white/80 backdrop-blur-sm shadow-md";
  head($$payload, ($$payload2) => {
    $$payload2.out += `<meta charset="utf-8"> <meta name="viewport" content="width=device-width, initial-scale=1"> <link rel="icon" href="/favicon.png" type="image/png"> <link rel="apple-touch-icon" href="/apple-touch-icon.png"> <meta name="theme-color" content="#2563EB"> <meta name="msapplication-TileColor" content="#2563EB"> <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"> <meta name="language" content="English"> <meta name="author" content="MicroPyramid"> <link rel="canonical"${attr("href", `https://bottlecrm.io${stringify(page.url.pathname)}`)}> <meta property="og:site_name" content="BottleCRM"> <meta property="og:locale" content="en_US"> <meta name="twitter:site" content="@micropyramid"> <meta name="twitter:creator" content="@micropyramid"> <link rel="preconnect" href="https://fonts.googleapis.com"> <link rel="preconnect" href="https://github.com">`;
  });
  $$payload.out += `<div class="min-h-screen flex flex-col bg-gray-50 overflow-x-hidden"><nav${attr_class(`fixed top-0 w-full z-50 transition-all duration-300 ${stringify(navbarClass)}`)}><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center h-16"><div class="flex items-center flex-shrink-0"><a href="/" class="flex items-center group" aria-label="BottleCRM Homepage"><div class="relative"><img${attr("src", imgLogo)} alt="BottleCRM Logo" class="h-7 w-7 sm:h-8 sm:w-8 group-hover:opacity-90 transition-opacity duration-200"> <div class="absolute -top-1 -right-1 w-2.5 h-2.5 sm:w-3 sm:h-3 bg-green-500 rounded-full animate-pulse"></div></div> <span class="ml-2 text-lg sm:text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">BottleCRM</span> <span class="hidden xs:block ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">FREE</span></a></div> <div class="hidden xl:flex items-center space-x-1"><a href="/features" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Features</a> <a href="/pricing" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Pricing</a> <a href="https://www.youtube.com/@bottlecrm" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Live Demo</a> <a href="/blog" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Blog</a> <a href="/contact" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Support</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200" aria-label="Star BottleCRM on GitHub">`;
  Github($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----></a> <div class="h-6 w-px bg-gray-300 mx-2"></div> <a href="/login" class="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Login</a> <a href="/login" class="inline-flex items-center px-4 lg:px-6 py-2.5 rounded-lg text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">Start Free `;
  Arrow_right($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div> <div class="hidden lg:flex xl:hidden items-center space-x-2"><a href="https://www.youtube.com/@bottlecrm" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Demo</a> <a href="/login" class="px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200">Login</a> <a href="/signup" class="px-4 py-2 rounded-lg text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">Sign Up</a> <button class="ml-2 inline-flex items-center justify-center p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200" aria-label="Toggle navigation menu">`;
  if (isMenuOpen) {
    $$payload.out += "<!--[-->";
    X($$payload, { class: "h-5 w-5" });
  } else {
    $$payload.out += "<!--[!-->";
    Menu($$payload, { class: "h-5 w-5" });
  }
  $$payload.out += `<!--]--></button></div> <div class="lg:hidden flex items-center space-x-2"><div class="hidden sm:flex items-center space-x-2"><a href="/login" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200">Login</a> <a href="/signup" class="px-3 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200">Sign Up</a></div> <button class="inline-flex items-center justify-center p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 active:bg-gray-200 transition-all duration-200" aria-label="Toggle navigation menu"${attr("aria-expanded", isMenuOpen)}>`;
  if (isMenuOpen) {
    $$payload.out += "<!--[-->";
    X($$payload, { class: "h-6 w-6" });
  } else {
    $$payload.out += "<!--[!-->";
    Menu($$payload, { class: "h-6 w-6" });
  }
  $$payload.out += `<!--]--></button></div></div></div> `;
  if (isMenuOpen) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="xl:hidden bg-white/98 backdrop-blur-xl border-t border-gray-200/60 shadow-2xl animate-in slide-in-from-top-5 duration-200"><div class="px-4 pt-4 pb-6 space-y-2 max-h-[calc(100vh-4rem)] overflow-y-auto"><div class="space-y-1"><a href="/features" class="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100 transition-all duration-200">`;
    Square_check_big($$payload, { class: "w-5 h-5 mr-3 text-blue-500" });
    $$payload.out += `<!----> Features</a> <a href="/pricing" class="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100 transition-all duration-200">`;
    Dollar_sign($$payload, { class: "w-5 h-5 mr-3 text-green-500" });
    $$payload.out += `<!----> <span class="flex-1">Pricing</span> <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">Free</span></a> <a href="https://www.youtube.com/@bottlecrm" class="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100 transition-all duration-200">`;
    Video($$payload, { class: "w-5 h-5 mr-3 text-purple-500" });
    $$payload.out += `<!----> Live Demo</a> <a href="/blog" class="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100 transition-all duration-200">`;
    Square_pen($$payload, { class: "w-5 h-5 mr-3 text-red-500" });
    $$payload.out += `<!----> Blog</a> <a href="/contact" class="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100 transition-all duration-200">`;
    Message_circle($$payload, { class: "w-5 h-5 mr-3 text-indigo-500" });
    $$payload.out += `<!----> Support</a></div> <div class="pt-2"><a href="https://github.com/MicroPyramid/svelte-crm" target="_blank" rel="noopener noreferrer" class="flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 active:bg-gray-100 transition-all duration-200">`;
    Github($$payload, { class: "w-5 h-5 mr-3" });
    $$payload.out += `<!----> <span class="flex-1">GitHub Repository</span> <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-medium">★ 1K+</span></a></div> <div class="pt-4 border-t border-gray-200 space-y-3 sm:hidden"><a href="/login" class="block w-full text-center px-4 py-3 rounded-xl text-base font-medium text-gray-700 border-2 border-gray-300 hover:bg-gray-50 active:bg-gray-100 transition-all duration-200">Sign In</a> <a href="/signup" class="block w-full text-center px-4 py-3 rounded-xl text-base font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 active:from-blue-800 active:to-blue-900 shadow-lg transition-all duration-200">Start Free Trial</a></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></nav> <main class="flex-grow pt-16"><!---->`;
  slot($$payload, $$props, "default", {}, null);
  $$payload.out += `<!----></main> <footer class="bg-gray-900 text-gray-300"><div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8"><div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 mb-12"><div class="text-center"><h3 class="text-2xl font-bold text-white mb-4">Stay Updated with BottleCRM</h3> <p class="text-blue-100 mb-6 max-w-2xl mx-auto">Get the latest updates on new features, best practices, and CRM tips delivered to your inbox.</p> <form method="POST" action="/?/subscribe" class="max-w-md mx-auto">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3"><input type="email" name="email" placeholder="Enter your email" required class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white/50 focus:outline-none"> <button type="submit" class="px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 disabled:opacity-50">Subscribe Free</button></div></form></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8"><div class="lg:col-span-2"><div class="flex items-center mb-4"><img${attr("src", imgLogo)} alt="BottleCRM Logo" class="h-8 w-8 mr-3"> <span class="text-xl font-bold text-white">BottleCRM</span></div> <p class="text-gray-400 mb-6 leading-relaxed">The only CRM you'll ever need - completely free, open-source, and designed for startups. Build better customer relationships without breaking the bank.</p> <div class="flex space-x-4"><a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="p-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors duration-200" aria-label="GitHub">`;
  Github($$payload, { class: "h-5 w-5" });
  $$payload.out += `<!----></a> <a href="https://x.com/micropyramid" target="_blank" rel="noopener noreferrer" class="p-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors duration-200" aria-label="Twitter">`;
  Twitter($$payload, { class: "h-5 w-5" });
  $$payload.out += `<!----></a> <a href="https://linkedin.com/company/micropyramid" target="_blank" rel="noopener noreferrer" class="p-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors duration-200" aria-label="LinkedIn">`;
  Linkedin($$payload, { class: "h-5 w-5" });
  $$payload.out += `<!----></a></div></div> <div><h3 class="text-lg font-semibold text-white mb-4">CRM Solutions</h3> <ul class="space-y-3 text-sm"><li><a href="/features/contact-management" class="hover:text-white transition-colors duration-200">Contact Management</a></li> <li><a href="/features/lead-management" class="hover:text-white transition-colors duration-200">Lead Management</a></li> <li><a href="/features/account-management" class="hover:text-white transition-colors duration-200">Account Management</a></li> <li><a href="/features/sales-pipeline" class="hover:text-white transition-colors duration-200">Sales Pipeline</a></li> <li><a href="/features/analytics" class="hover:text-white transition-colors duration-200 inline-flex items-center">Analytics &amp; Reports <span class="ml-2 text-[10px] bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">Coming Soon</span></a></li></ul></div> <div><h3 class="text-lg font-semibold text-white mb-4">Resources</h3> <ul class="space-y-3 text-sm"><li><a href="/blog" class="hover:text-white transition-colors duration-200">CRM Blog</a></li> <li><a href="/documentation" class="hover:text-white transition-colors duration-200 inline-flex items-center">Documentation <span class="ml-2 text-[10px] bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">Coming Soon</span></a></li> <li><a href="/tutorials" class="hover:text-white transition-colors duration-200 inline-flex items-center">Video Tutorials <span class="ml-2 text-[10px] bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">Coming Soon</span></a></li> <li><a href="/api-docs" class="hover:text-white transition-colors duration-200 inline-flex items-center">API Documentation <span class="ml-2 text-[10px] bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">Coming Soon</span></a></li> <li><a href="/case-studies" class="hover:text-white transition-colors duration-200 inline-flex items-center">Case Studies <span class="ml-2 text-[10px] bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">Coming Soon</span></a></li></ul></div> <div><h3 class="text-lg font-semibold text-white mb-4">Support</h3> <ul class="space-y-3 text-sm"><li><a href="/contact" class="hover:text-white transition-colors duration-200">Contact Support</a></li> <li><a href="/faq" class="hover:text-white transition-colors duration-200">FAQ</a></li> <li><a href="/hosting-services" class="hover:text-white transition-colors duration-200 inline-flex items-center">Hosting Services <span class="ml-2 text-[10px] bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full font-medium">Coming Soon</span></a></li> <li><a href="/customization" class="hover:text-white transition-colors duration-200">Custom Development</a></li> <li><a href="/migration" class="hover:text-white transition-colors duration-200">CRM Migration</a></li></ul></div></div> <div class="mt-12 pt-8 border-t border-gray-800"><div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0"><div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6"><p class="text-sm text-gray-400">© ${escape_html((/* @__PURE__ */ new Date()).getFullYear())} BottleCRM by <a href="https://micropyramid.com" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:text-blue-300">MicroPyramid</a>. Open Source &amp; Free Forever.</p> <div class="flex space-x-4 text-sm"><a href="/privacy-policy" class="text-gray-400 hover:text-white transition-colors duration-200">Privacy</a> <a href="/terms-of-service" class="text-gray-400 hover:text-white transition-colors duration-200">Terms</a> <a href="/sitemap.xml" class="text-gray-400 hover:text-white transition-colors duration-200">Sitemap</a></div></div></div></div></div></footer></div>`;
  pop();
}
export {
  _layout as default
};
