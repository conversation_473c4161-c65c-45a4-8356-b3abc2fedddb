import { P as head, J as attr, R as stringify, C as pop, A as push } from "../../../../../chunks/index2.js";
import { U as Users } from "../../../../../chunks/users.js";
import { C as Check } from "../../../../../chunks/check.js";
import { U as User_plus } from "../../../../../chunks/user-plus.js";
import { G as Github } from "../../../../../chunks/github.js";
import { S as Search } from "../../../../../chunks/search.js";
import { F as Funnel } from "../../../../../chunks/funnel.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { B as Building_2 } from "../../../../../chunks/building-2.js";
import { M as Mail } from "../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../chunks/phone.js";
import { M as Map_pin } from "../../../../../chunks/map-pin.js";
import { C as Clock } from "../../../../../chunks/clock.js";
import { E as Eye } from "../../../../../chunks/eye.js";
import { S as Square_pen } from "../../../../../chunks/square-pen.js";
import { Z as Zap } from "../../../../../chunks/zap.js";
import { T as Target } from "../../../../../chunks/target.js";
import { D as Database } from "../../../../../chunks/database.js";
import { S as Smartphone } from "../../../../../chunks/smartphone.js";
import { M as Message_circle } from "../../../../../chunks/message-circle.js";
import { X } from "../../../../../chunks/x.js";
import { C as Chevron_down } from "../../../../../chunks/chevron-down.js";
import { h as html } from "../../../../../chunks/html.js";
function _page($$payload, $$props) {
  push();
  let activeFaq = null;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Free Contact Management Software | Unlimited CRM - BottleCRM</title>`;
    $$payload2.out += `<meta name="description" content="Manage unlimited contacts for free with BottleCRM. Advanced search, custom fields, mobile access. No per-contact fees. 100% data ownership."> <meta name="keywords" content="free crm, contact management, unlimited contacts, open source crm, customer database, contact manager, crm software, bottlecrm"> <link rel="canonical" href="https://bottlecrm.io/features/contact-management"> <meta property="og:title" content="Free Contact Management Software | Unlimited CRM - BottleCRM"> <meta property="og:description" content="Manage unlimited contacts for free with BottleCRM. Advanced search, custom fields, mobile access. No per-contact fees. 100% data ownership."> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/features/contact-management"> <meta property="og:image" content="https://bottlecrm.io/og-image-contact-management.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:title" content="Free Contact Management Software | Unlimited CRM - BottleCRM"> <meta name="twitter:description" content="Manage unlimited contacts for free with BottleCRM. Advanced search, custom fields, mobile access. No per-contact fees. 100% data ownership."> <meta name="twitter:image" content="https://bottlecrm.io/og-image-contact-management.png"> ${html(`
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "BottleCRM",
        "url": "https://bottlecrm.io/features/contact-management",
        "image": "https://bottlecrm.io/og-image-contact-management.png",
        "description": "Free contact management software with unlimited contacts, advanced search, custom fields, and mobile access. No per-contact fees.",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "All",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "publisher": {
          "@type": "Organization",
          "name": "BottleCRM"
        }
      }
    <\/script>
  `)}`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="lg:flex lg:items-center lg:space-x-12"><div class="lg:w-1/2 mb-12 lg:mb-0"><div class="inline-flex items-center px-4 py-2 rounded-full bg-green-500 text-white text-sm font-semibold mb-6">`;
  Users($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Unlimited Contacts • Always Free</div> <h1 class="text-4xl md:text-6xl font-extrabold mb-6 leading-tight">Contact Management That <span class="text-green-300">Doesn't Cost Per Contact</span></h1> <p class="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">Stop paying $2-10 per contact. BottleCRM gives you unlimited contact storage, advanced search, custom fields, and mobile access - all completely free. Build stronger customer relationships without breaking the bank.</p> <ul class="mb-8 space-y-3 text-lg"><li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Unlimited contacts and custom fields</span></li> <li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Advanced search and smart filtering</span></li> <li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Complete interaction history and timeline</span></li> <li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span>Mobile-optimized with offline access</span></li></ul> <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  User_plus($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Start Managing Contacts</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Github($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> View Source Code</a></div></div> <div class="lg:w-1/2"><div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20"><h3 class="text-2xl font-bold mb-6 text-center">💰 Industry Cost Comparison*</h3> <div class="space-y-4"><div class="bg-white/20 rounded-lg p-4"><div class="flex justify-between items-center mb-2"><span class="font-semibold">1,000 contacts in Enterprise CRM:</span> <span class="text-2xl font-bold text-red-300">$25,000+/year</span></div> <div class="flex justify-between items-center mb-2"><span class="font-semibold">1,000 contacts in Popular CRM:</span> <span class="text-2xl font-bold text-red-300">$3,000-8,000/year</span></div> <div class="flex justify-between items-center mb-4"><span class="font-semibold">1,000 contacts in Standard CRM:</span> <span class="text-2xl font-bold text-red-300">$1,500-3,000/year</span></div> <div class="border-t border-white/30 pt-4"><div class="flex justify-between items-center"><span class="font-bold text-lg">Unlimited contacts in BottleCRM:</span> <span class="text-3xl font-bold text-green-300">$0</span></div></div></div> <div class="text-center text-green-300 font-bold text-xl">Save $1,500 - $25,000+ per year!</div> <div class="text-center text-xs text-blue-200 mt-4">*Based on industry-standard per-contact pricing models as of 2024</div></div></div></div></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Professional Contact Management Features</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Everything you need to organize, track, and nurture your customer relationships. All features included for free, no premium tiers or hidden costs.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">See Contact Management in Action</h2> <p class="text-xl text-gray-600">Experience the intuitive interface and powerful features of BottleCRM's contact management system.</p></div> <div class="bg-gray-50 rounded-2xl p-8"><div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"><div class="flex items-center space-x-4"><div class="relative">`;
  Search($$payload, {
    class: "w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
  });
  $$payload.out += `<!----> <input type="text" placeholder="Search contacts..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"></div> <button class="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">`;
  Funnel($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Filter</button></div> <button class="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">`;
  Plus($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Add Contact</button></div> <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-green-500"><div class="flex items-start justify-between mb-4"><div class="flex-1"><h4 class="text-lg font-bold text-gray-900">Sarah Johnson</h4> <p class="text-gray-600 flex items-center mt-1">`;
  Building_2($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> TechStart Inc.</p></div> <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Qualified</span></div> <div class="space-y-2 text-sm text-gray-600"><p class="flex items-center">`;
  Mail($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> <EMAIL></p> <p class="flex items-center">`;
  Phone($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> +****************</p> <p class="flex items-center">`;
  Map_pin($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> San Francisco, CA</p> <p class="flex items-center">`;
  Clock($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Last contact: 2 days ago</p></div> <div class="mt-4 flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">Hot Lead</span> <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">Enterprise</span></div> <div class="mt-4 flex space-x-2"><button class="flex-1 flex items-center justify-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 text-sm">`;
  Eye($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> View</button> <button class="flex-1 flex items-center justify-center px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 text-sm">`;
  Square_pen($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> Edit</button></div></div> <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-yellow-500"><div class="flex items-start justify-between mb-4"><div class="flex-1"><h4 class="text-lg font-bold text-gray-900">Michael Chen</h4> <p class="text-gray-600 flex items-center mt-1">`;
  Building_2($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> Growth Co.</p></div> <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Negotiation</span></div> <div class="space-y-2 text-sm text-gray-600"><p class="flex items-center">`;
  Mail($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> <EMAIL></p> <p class="flex items-center">`;
  Phone($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> +****************</p> <p class="flex items-center">`;
  Map_pin($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> New York, NY</p> <p class="flex items-center">`;
  Clock($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Last contact: 1 week ago</p></div> <div class="mt-4 flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">Proposal Sent</span> <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">Decision Maker</span></div> <div class="mt-4 flex space-x-2"><button class="flex-1 flex items-center justify-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 text-sm">`;
  Eye($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> View</button> <button class="flex-1 flex items-center justify-center px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 text-sm">`;
  Square_pen($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> Edit</button></div></div> <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-blue-500"><div class="flex items-start justify-between mb-4"><div class="flex-1"><h4 class="text-lg font-bold text-gray-900">Emma Rodriguez</h4> <p class="text-gray-600 flex items-center mt-1">`;
  Building_2($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> Scale Solutions</p></div> <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">New</span></div> <div class="space-y-2 text-sm text-gray-600"><p class="flex items-center">`;
  Mail($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> <EMAIL></p> <p class="flex items-center">`;
  Phone($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> +****************</p> <p class="flex items-center">`;
  Map_pin($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Austin, TX</p> <p class="flex items-center">`;
  Clock($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Last contact: 3 days ago</p></div> <div class="mt-4 flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">New Contact</span> <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">SMB</span></div> <div class="mt-4 flex space-x-2"><button class="flex-1 flex items-center justify-center px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 text-sm">`;
  Eye($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> View</button> <button class="flex-1 flex items-center justify-center px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 text-sm">`;
  Square_pen($$payload, { class: "w-4 h-4 mr-1" });
  $$payload.out += `<!----> Edit</button></div></div></div> <div class="mt-8 text-center"><p class="text-gray-600 mb-4">This is just a preview. The actual BottleCRM interface includes advanced filtering, bulk operations, custom fields, and much more.</p> <a href="/login" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-semibold">`;
  Zap($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Try the Full Interface</a></div></div></div></section> <section class="py-20 bg-gradient-to-r from-orange-50 to-amber-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">🚀 Coming Soon Features</h2> <p class="text-xl text-gray-600">We're continuously improving BottleCRM with new contact management features based on user feedback.</p></div> <div class="grid gap-8 md:grid-cols-3"><div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-200"><div class="rounded-xl bg-gradient-to-r from-orange-100 to-amber-100 p-4 inline-block mb-6">`;
  Target($$payload, { class: "h-8 w-8 text-orange-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Smart Contact Segmentation</h3> <p class="text-gray-600 mb-4">Automatically segment contacts based on behavior, engagement, and custom criteria for targeted campaigns.</p> <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Q2 2024</div></div> <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-200"><div class="rounded-xl bg-gradient-to-r from-orange-100 to-amber-100 p-4 inline-block mb-6">`;
  Database($$payload, { class: "h-8 w-8 text-orange-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Bulk Import &amp; Export</h3> <p class="text-gray-600 mb-4">Import thousands of contacts from CSV/Excel files and export your data in multiple formats with advanced mapping.</p> <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Q2 2024</div></div> <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-200"><div class="rounded-xl bg-gradient-to-r from-orange-100 to-amber-100 p-4 inline-block mb-6">`;
  Smartphone($$payload, { class: "h-8 w-8 text-orange-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Mobile-First Contact Access</h3> <p class="text-gray-600 mb-4">Native mobile app with offline sync, push notifications, and optimized contact management on the go.</p> <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Q3 2024</div></div></div> <div class="mt-12 text-center bg-white rounded-2xl p-8 shadow-lg"><h3 class="text-2xl font-bold text-gray-900 mb-4">Want to influence our roadmap?</h3> <p class="text-gray-600 mb-6">Join our community and help us prioritize which features to build next. Your feedback shapes BottleCRM's future!</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4"><a href="https://github.com/MicroPyramid/opensource-startup-crm/discussions" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 font-semibold">`;
  Message_circle($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Join Discussions</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm/issues" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 font-semibold">`;
  Github($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Request Features</a></div></div></div></section> <section class="py-20 bg-gradient-to-r from-blue-50 to-indigo-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Simple Contact Management Workflow</h2> <p class="text-xl text-gray-600">Get started with professional contact management in just 4 easy steps.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Contact Management Feature Comparison</h2> <p class="text-xl text-gray-600">See how BottleCRM's contact management compares to typical CRM pricing models.</p></div> <div class="overflow-x-auto"><table class="w-full bg-white rounded-xl shadow-lg overflow-hidden"><thead class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white"><tr><th class="px-6 py-4 text-left text-sm font-semibold">Platform Type</th><th class="px-6 py-4 text-center text-sm font-semibold">Contact Limit</th><th class="px-6 py-4 text-center text-sm font-semibold">Typical Pricing</th><th class="px-6 py-4 text-center text-sm font-semibold">Custom Fields</th><th class="px-6 py-4 text-center text-sm font-semibold">Data Export</th><th class="px-6 py-4 text-center text-sm font-semibold">Mobile App</th><th class="px-6 py-4 text-center text-sm font-semibold">API Access</th><th class="px-6 py-4 text-center text-sm font-semibold">Self-Hosted</th></tr></thead><tbody><tr class="border-b border-gray-200 bg-green-50 border-l-4 border-l-green-500"><td class="px-6 py-4 font-semibold text-green-700"><div class="flex items-center">BottleCRM <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Free &amp; Open Source</span></div></td><td class="px-6 py-4 text-center font-bold text-green-600">Unlimited</td><td class="px-6 py-4 text-center font-bold text-green-600">$0</td><td class="px-6 py-4 text-center text-gray-700">Unlimited</td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td></tr><tr class="border-b border-gray-200 hover:bg-gray-50"><td class="px-6 py-4 font-semibold text-gray-900"><div class="flex items-center">Enterprise CRM Platforms</div></td><td class="px-6 py-4 text-center text-gray-700">Plan-dependent</td><td class="px-6 py-4 text-center text-gray-700">$25-300+/user</td><td class="px-6 py-4 text-center text-gray-700">Limited by tier</td><td class="px-6 py-4 text-center"><span class="text-gray-700 text-sm">Often restricted</span></td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center"><span class="text-gray-700 text-sm">Premium tiers</span></td><td class="px-6 py-4 text-center">`;
  X($$payload, { class: "w-5 h-5 text-red-500 mx-auto" });
  $$payload.out += `<!----></td></tr><tr class="border-b border-gray-200 hover:bg-gray-50"><td class="px-6 py-4 font-semibold text-gray-900"><div class="flex items-center">Popular Cloud CRMs</div></td><td class="px-6 py-4 text-center text-gray-700">Limited on free plans</td><td class="px-6 py-4 text-center text-gray-700">$15-100+/month</td><td class="px-6 py-4 text-center text-gray-700">Tier-restricted</td><td class="px-6 py-4 text-center"><span class="text-gray-700 text-sm">Basic exports</span></td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center"><span class="text-gray-700 text-sm">Paid features</span></td><td class="px-6 py-4 text-center">`;
  X($$payload, { class: "w-5 h-5 text-red-500 mx-auto" });
  $$payload.out += `<!----></td></tr><tr class="border-b border-gray-200 hover:bg-gray-50"><td class="px-6 py-4 font-semibold text-gray-900"><div class="flex items-center">Mid-Market CRM Solutions</div></td><td class="px-6 py-4 text-center text-gray-700">Per-user limitations</td><td class="px-6 py-4 text-center text-gray-700">$15-99/user</td><td class="px-6 py-4 text-center text-gray-700">Limited</td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center">`;
  Check($$payload, { class: "w-5 h-5 text-green-500 mx-auto" });
  $$payload.out += `<!----></td><td class="px-6 py-4 text-center"><span class="text-gray-700 text-sm">Higher tiers</span></td><td class="px-6 py-4 text-center">`;
  X($$payload, { class: "w-5 h-5 text-red-500 mx-auto" });
  $$payload.out += `<!----></td></tr></tbody></table></div> <div class="mt-8 text-center"><p class="text-sm text-gray-600">*Pricing and features based on publicly available information as of 2024. Individual platform offerings may vary.</p></div></div></section> <section class="py-20 bg-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 mb-6">Contact Management FAQ</h2> <p class="text-xl text-gray-600">Common questions about BottleCRM's contact management features.</p></div> <div class="space-y-4"><div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 0)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">How many contacts can I store in BottleCRM?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 1)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Can I import contacts from my existing CRM or spreadsheet?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 2)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Is my contact data secure and private?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 3)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Can I customize contact fields for my business needs?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 4)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Does BottleCRM work on mobile devices?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 5)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">How does contact management in BottleCRM compare to paid alternatives?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-4xl md:text-5xl font-extrabold mb-6">Ready to Manage Unlimited Contacts for Free?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">Stop paying per contact or hitting storage limits. Start building better customer relationships with BottleCRM's powerful, free contact management system.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-8"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  User_plus($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Start Managing Contacts</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Github($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Download Source Code</a></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto"><div class="bg-white/10 backdrop-blur-sm rounded-lg p-4"><div class="text-2xl font-bold text-green-300">∞</div> <div class="text-sm text-blue-200">Unlimited Contacts</div></div> <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4"><div class="text-2xl font-bold text-green-300">$0</div> <div class="text-sm text-blue-200">Monthly Cost</div></div> <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4"><div class="text-2xl font-bold text-green-300">100%</div> <div class="text-sm text-blue-200">Data Ownership</div></div></div> <p class="mt-8 text-blue-200">🚀 No setup fees • No user limits • No vendor lock-in • Free forever</p></div></section>`;
  pop();
}
export {
  _page as default
};
