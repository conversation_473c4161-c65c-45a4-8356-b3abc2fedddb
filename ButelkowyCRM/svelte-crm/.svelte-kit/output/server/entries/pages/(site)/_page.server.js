import { f as fail } from "../../../chunks/index.js";
import { p as prisma } from "../../../chunks/prisma.js";
import crypto from "crypto";
const actions = {
  subscribe: async ({ request, getClientAddress }) => {
    const formData = await request.formData();
    const email = formData.get("email")?.toString().trim();
    if (!email) {
      return fail(400, { message: "Email is required" });
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return fail(400, { message: "Please enter a valid email address" });
    }
    if (email.includes("+")) {
      return fail(400, { message: "Please enter a valid email address" });
    }
    try {
      const existingSubscriber = await prisma.newsletterSubscriber.findUnique({
        where: { email }
      });
      if (existingSubscriber) {
        if (existingSubscriber.isActive) {
          return fail(400, { message: "You are already subscribed to our newsletter" });
        } else {
          await prisma.newsletterSubscriber.update({
            where: { email },
            data: {
              isActive: true,
              subscribedAt: /* @__PURE__ */ new Date(),
              unsubscribedAt: null,
              confirmationToken: crypto.randomUUID(),
              isConfirmed: false,
              confirmedAt: null,
              ipAddress: getClientAddress(),
              userAgent: request.headers.get("user-agent")
            }
          });
          return { success: true, message: "Successfully resubscribed to newsletter" };
        }
      }
      await prisma.newsletterSubscriber.create({
        data: {
          email,
          isActive: true,
          confirmationToken: crypto.randomUUID(),
          isConfirmed: false,
          ipAddress: getClientAddress(),
          userAgent: request.headers.get("user-agent")
        }
      });
      return { success: true, message: "Successfully subscribed to newsletter" };
    } catch (error) {
      console.error("Newsletter subscription error:", error);
      return fail(500, { message: "Failed to subscribe. Please try again later." });
    }
  }
};
export {
  actions
};
