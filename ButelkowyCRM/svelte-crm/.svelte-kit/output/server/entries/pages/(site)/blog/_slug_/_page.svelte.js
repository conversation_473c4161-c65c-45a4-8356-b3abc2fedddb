import { P as head, D as escape_html, M as bind_props, C as pop, A as push, J as attr } from "../../../../../chunks/index2.js";
import { marked } from "marked";
import { h as html } from "../../../../../chunks/html.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { post } = data;
  let renderedContent = post.contentBlocks?.map((block) => marked(block.content)).join("") || "";
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>${escape_html(post.title)} | BottleCRM Blog</title>`;
    $$payload2.out += `<meta name="description"${attr("content", post.excerpt || `Read ${post.title} on the BottleCRM blog`)}>`;
  });
  $$payload.out += `<article class="bg-white"><div class="py-16 bg-gradient-to-b from-blue-50 to-white"><div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"><nav class="mb-6"><ol class="flex items-center space-x-2 text-sm text-gray-600"><li><a href="/blog" class="hover:text-blue-600">Blog</a></li> <li><svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></li> <li><span class="text-gray-900 font-medium truncate" aria-current="page">${escape_html(post.title)}</span></li></ol></nav> <div class="text-center"><h1 class="text-3xl font-extrabold text-gray-900 sm:text-4xl md:text-5xl">${escape_html(post.title)}</h1></div></div></div> <div class="py-12"><div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"><div class="prose prose-blue prose-lg mx-auto">${html(renderedContent)}</div></div></div></article> <div class="bg-white py-8"><div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center"><a href="/blog" class="inline-flex items-center text-blue-600 hover:underline"><svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path></svg> Back to all blog posts</a></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
