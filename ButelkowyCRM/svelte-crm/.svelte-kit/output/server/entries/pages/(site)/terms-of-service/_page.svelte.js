import { P as head, C as pop, A as push } from "../../../../chunks/index2.js";
import { A as Arrow_left } from "../../../../chunks/arrow-left.js";
import { h as html } from "../../../../chunks/html.js";
function _page($$payload, $$props) {
  push();
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Terms of Service | Free Open Source CRM - BottleCRM</title>`;
    $$payload2.out += `<meta name="description" content="Read the Terms of Service for BottleCRM, a free and open source CRM software for startups and small businesses. MIT licensed. No hidden fees."> <meta name="keywords" content="free crm, open source crm, crm software, mit license, bottlecrm, terms of service, startup crm, small business crm"> <meta name="robots" content="index, follow"> <link rel="canonical" href="https://bottlecrm.io/terms-of-service"> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/terms-of-service"> <meta property="og:title" content="Terms of Service | Free Open Source CRM - BottleCRM"> <meta property="og:description" content="Terms of Service for BottleCRM, a free and open source CRM software for startups and small businesses. MIT licensed."> <meta property="og:site_name" content="BottleCRM"> <meta property="og:image" content="https://bottlecrm.io/og-image.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:url" content="https://bottlecrm.io/terms-of-service"> <meta name="twitter:title" content="Terms of Service | Free Open Source CRM - BottleCRM"> <meta name="twitter:description" content="Terms of Service for BottleCRM, a free and open source CRM software for startups and small businesses. MIT licensed."> <meta name="twitter:image" content="https://bottlecrm.io/og-image.png"> ${html(`
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Terms of Service - BottleCRM",
        "description": "Terms of Service for BottleCRM, a free and open source CRM software for startups and small businesses. MIT licensed.",
        "url": "https://bottlecrm.io/terms-of-service",
        "lastReviewed": "2024-12-01",
        "about": {
          "@type": "SoftwareApplication",
          "name": "BottleCRM",
          "applicationCategory": "Customer Relationship Management",
          "operatingSystem": "All",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          }
        }
      }
    <\/script>
  `)}`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white py-16"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="bg-gray-50 py-4 border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><a href="/" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">`;
  Arrow_left($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Back to Home</a></div></section> <section class="py-12 bg-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></section> <section class="py-16 bg-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="prose prose-lg max-w-none">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-16 bg-gradient-to-r from-blue-600 to-indigo-700 text-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></section>`;
  pop();
}
export {
  _page as default
};
