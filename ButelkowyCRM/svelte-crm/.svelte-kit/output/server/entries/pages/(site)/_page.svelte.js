import { E as spread_props, C as pop, A as push, P as head, J as attr, R as stringify } from "../../../chunks/index2.js";
import { C as Check } from "../../../chunks/check.js";
import { Z as Zap } from "../../../chunks/zap.js";
import { G as Github } from "../../../chunks/github.js";
import { U as Users } from "../../../chunks/users.js";
import { C as Chart_column } from "../../../chunks/chart-column.js";
import { S as Square_check_big } from "../../../chunks/square-check-big.js";
import { T as Trending_up } from "../../../chunks/trending-up.js";
import { F as File_text } from "../../../chunks/file-text.js";
import { S as Smartphone } from "../../../chunks/smartphone.js";
import { X } from "../../../chunks/x.js";
import { C as Chevron_right } from "../../../chunks/chevron-right.js";
import { C as Chevron_down } from "../../../chunks/chevron-down.js";
import { I as Icon } from "../../../chunks/Icon.js";
import { M as Message_circle } from "../../../chunks/message-circle.js";
function Git_fork($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "circle",
      { "cx": "12", "cy": "18", "r": "3" }
    ],
    ["circle", { "cx": "6", "cy": "6", "r": "3" }],
    ["circle", { "cx": "18", "cy": "6", "r": "3" }],
    [
      "path",
      {
        "d": "M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9"
      }
    ],
    ["path", { "d": "M12 12v3" }]
  ];
  Icon($$payload, spread_props([
    { name: "git-fork" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
const banner_img = "/_app/immutable/assets/banner.BaS3tnl2.png";
function _page($$payload, $$props) {
  push();
  let activeFaq = null;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>BottleCRM: Free Open Source CRM for Startups &amp; Small Business</title>`;
    $$payload2.out += `<meta name="description" content="BottleCRM is a free, open-source CRM for startups and small businesses. Self-host, manage contacts, sales, and tasks. No subscription fees. Download now!"> <meta name="keywords" content="free crm, open source crm, crm software, startup crm, small business crm, self hosted crm, customer relationship management"> <meta name="robots" content="index, follow"> <link rel="canonical" href="https://bottlecrm.io/"> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/"> <meta property="og:title" content="BottleCRM: Free Open Source CRM for Startups &amp; Small Business"> <meta property="og:description" content="Free, open-source CRM for startups and small businesses. Self-host, unlimited users, no subscription fees."> <meta property="og:image" content="https://bottlecrm.io/og-image.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:url" content="https://bottlecrm.io/"> <meta name="twitter:title" content="BottleCRM: Free Open Source CRM for Startups &amp; Small Business"> <meta name="twitter:description" content="Free, open-source CRM for startups and small businesses. Self-host, unlimited users, no subscription fees."> <meta name="twitter:image" content="https://bottlecrm.io/twitter-image.png"> <script type="application/ld+json">
    {@html JSON.stringify(schema)}
  <\/script>`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white relative overflow-hidden"><div class="absolute inset-0 bg-black/10"></div> <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div> <div class="max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:px-8 flex flex-col lg:flex-row items-center relative z-10"><div class="lg:w-1/2 mb-10 lg:mb-0"><div><div class="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6"><span class="text-sm font-medium">🚀 Free Forever • No Credit Card Required</span></div> <h1 class="text-4xl md:text-6xl font-extrabold tracking-tight mb-6 leading-tight">The Free <span class="text-yellow-300">CRM Software</span> That Startups Actually Need</h1> <p class="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">Stop paying $50-300/month for CRM subscriptions. BottleCRM is a 100% free, open-source, and self-hostable customer relationship management solution built specifically for startups and growing businesses.</p> <ul class="mb-8 space-y-3 text-lg"><li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span class="break-words">No monthly fees - Save thousands per year</span></li> <li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span class="break-words">Complete data ownership - Host on your servers</span></li> <li class="flex items-center">`;
  Check($$payload, {
    class: "w-6 h-6 text-green-400 mr-3 flex-shrink-0"
  });
  $$payload.out += `<!----> <span class="break-words">Unlimited users and customization</span></li></ul> <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"><a href="/login" class="inline-flex items-center justify-center px-6 py-4 border border-transparent text-base font-semibold rounded-lg text-blue-700 bg-white hover:bg-gray-100 shadow-lg transition-all duration-200 hover:scale-105 whitespace-nowrap">`;
  Zap($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Start Free Demo</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-4 border-2 border-white text-base font-semibold rounded-lg text-white hover:bg-white/10 transition-all duration-200 whitespace-nowrap">`;
  Github($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> View Source</a></div></div></div> <div class="lg:w-1/2 flex justify-center lg:justify-end"><div class="w-full max-w-lg"><div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl overflow-hidden border border-white/20"><div class="h-80 bg-gradient-to-br from-gray-100 to-gray-200 relative"><img${attr("src", banner_img)} alt="BottleCRM Dashboard Preview - Free CRM Software Interface" class="w-full h-full object-cover"> <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div></div> <div class="p-6 bg-white/5"><div class="space-y-3"><div class="w-full bg-gradient-to-r from-blue-400 to-purple-500 h-3 rounded-full"></div> <div class="w-4/5 bg-gradient-to-r from-blue-300 to-indigo-400 h-3 rounded-full"></div> <div class="w-3/5 bg-gradient-to-r from-indigo-300 to-blue-400 h-3 rounded-full"></div></div></div></div></div></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Enterprise-Grade CRM Features <span class="text-blue-600">Without Enterprise Costs</span></h2> <p class="mt-4 max-w-3xl text-xl text-gray-600 mx-auto leading-relaxed">Everything your startup or small business needs to manage customer relationships, automate sales processes, and drive sustainable growth. All features included, no premium tiers.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"><div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Users($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Smart Contact Management System</h3> <p class="text-gray-600 mb-6 leading-relaxed">Centralize customer data with our intuitive contact management system. Track customer interactions, manage qualified leads, and build stronger customer relationships. Perfect for startups, small businesses, and growing enterprises looking for efficient CRM software.</p> <ul class="space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> 360° Customer View</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Lead Scoring</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Contact Segmentation</li></ul></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Chart_column($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Visual Sales Pipeline Management</h3> <p class="text-gray-600 mb-6 leading-relaxed">Streamline your sales process with our drag-and-drop sales pipeline. Track deals from lead qualification to closing, forecast revenue accurately, and implement powerful sales automation workflows to boost your conversion rates.</p> <ul class="space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Deal Tracking</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Sales Forecasting</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Automation Workflows</li></ul></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Square_check_big($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Advanced Task &amp; Project Management</h3> <p class="text-gray-600 mb-6 leading-relaxed">Never miss a follow-up with integrated task management and project tracking. Set reminders, assign tasks to team members, track project milestones, and ensure maximum productivity across your sales and marketing teams.</p> <ul class="space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Task Automation</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Team Collaboration</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Deadline Tracking</li></ul></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Trending_up($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Real-time Analytics &amp; Reporting</h3> <p class="text-gray-600 mb-6 leading-relaxed">Make data-driven decisions with comprehensive CRM analytics and business intelligence. Track sales performance, monitor marketing campaign effectiveness, and generate detailed reports to optimize your customer acquisition strategies.</p> <ul class="space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Custom Dashboards</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Performance Metrics</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> ROI Tracking</li></ul></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  File_text($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Automated Invoice &amp; Billing Management</h3> <p class="text-gray-600 mb-6 leading-relaxed">Streamline your billing process with integrated invoicing software. Create professional invoices, track payments, manage recurring billing, and integrate with popular payment gateways. Perfect for service-based businesses and SaaS startups.</p> <ul class="space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Payment Tracking</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Recurring Billing</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Payment Integration</li></ul></div> <div class="bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100 group relative overflow-hidden"><div class="absolute top-4 right-4"><span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">Coming Soon</span></div> <div class="rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 p-4 inline-block mb-6 group-hover:scale-110 transition-transform duration-300">`;
  Smartphone($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="text-2xl font-bold text-gray-900 mb-4">Mobile CRM &amp; Cloud Access</h3> <p class="text-gray-600 mb-6 leading-relaxed">Access your CRM data anywhere with our responsive, mobile-optimized interface. Work offline, sync data automatically, and manage your business on-the-go. Perfect for sales teams, remote workers, and field service management.</p> <ul class="space-y-2"><li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Offline Access</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Real-time Sync</li> <li class="flex items-center text-sm text-gray-700">`;
  Check($$payload, { class: "w-4 h-4 text-green-500 mr-2" });
  $$payload.out += `<!----> Cross-platform Support</li></ul></div></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Why Choose BottleCRM Over <span class="text-red-600">Expensive CRM Solutions?</span></h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">Compare BottleCRM with typical commercial CRM solutions and see how much you can save while getting powerful features.</p></div> <div class="overflow-x-auto -mx-4 sm:mx-0"><div class="min-w-full inline-block align-middle"><table class="min-w-full bg-white rounded-xl shadow-lg overflow-hidden"><thead class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white"><tr><th class="px-3 sm:px-6 py-4 text-left text-xs sm:text-sm font-semibold whitespace-nowrap">CRM Category</th><th class="px-3 sm:px-6 py-4 text-center text-xs sm:text-sm font-semibold whitespace-nowrap">Typical Pricing</th><th class="px-3 sm:px-6 py-4 text-center text-xs sm:text-sm font-semibold whitespace-nowrap">Open Source</th><th class="px-3 sm:px-6 py-4 text-center text-xs sm:text-sm font-semibold whitespace-nowrap">Self-Hosted</th><th class="px-3 sm:px-6 py-4 text-center text-xs sm:text-sm font-semibold whitespace-nowrap">Customizable</th><th class="px-3 sm:px-6 py-4 text-center text-xs sm:text-sm font-semibold whitespace-nowrap">User Limit</th></tr></thead><tbody><tr class="border-b border-gray-200 bg-blue-50 border-l-4 border-l-blue-500"><td class="px-3 sm:px-6 py-4 font-semibold text-blue-700 whitespace-nowrap"><div class="flex flex-col sm:flex-row sm:items-center"><span>BottleCRM</span> <span class="mt-1 sm:mt-0 sm:ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full whitespace-nowrap">Recommended</span></div></td><td class="px-3 sm:px-6 py-4 text-center font-medium text-green-600 text-xs sm:text-sm">Free</td><td class="px-3 sm:px-6 py-4 text-center">`;
  Check($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-green-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center">`;
  Check($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-green-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Yes</td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Unlimited</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50"><td class="px-3 sm:px-6 py-4 font-semibold text-gray-900 whitespace-nowrap"><span>Enterprise CRM Solutions</span></td><td class="px-3 sm:px-6 py-4 text-center font-medium text-gray-700 text-xs sm:text-sm">$25-300+/user/month</td><td class="px-3 sm:px-6 py-4 text-center">`;
  X($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-red-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center">`;
  X($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-red-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Yes</td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Per seat pricing</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50"><td class="px-3 sm:px-6 py-4 font-semibold text-gray-900 whitespace-nowrap"><span>Popular Cloud CRMs</span></td><td class="px-3 sm:px-6 py-4 text-center font-medium text-gray-700 text-xs sm:text-sm">$50-3200+/month</td><td class="px-3 sm:px-6 py-4 text-center">`;
  X($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-red-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center">`;
  X($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-red-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Limited</td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Contact-based pricing</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50"><td class="px-3 sm:px-6 py-4 font-semibold text-gray-900 whitespace-nowrap"><span>Traditional CRM Platforms</span></td><td class="px-3 sm:px-6 py-4 text-center font-medium text-gray-700 text-xs sm:text-sm">$15-99+/user/month</td><td class="px-3 sm:px-6 py-4 text-center">`;
  X($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-red-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center">`;
  X($$payload, {
    class: "w-4 h-4 sm:w-5 sm:h-5 text-red-500 mx-auto"
  });
  $$payload.out += `<!----></td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Limited</td><td class="px-3 sm:px-6 py-4 text-center text-gray-700 text-xs sm:text-sm">Per seat pricing</td></tr></tbody></table></div></div> <div class="mt-8 text-center"><p class="text-lg text-gray-600 mb-4"><strong class="text-green-600">Potential Annual Savings with BottleCRM:</strong> $3,000 - $36,000+ per year for a typical team</p> <a href="/pricing" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">See detailed cost comparison `;
  Chevron_right($$payload, { class: "ml-2 w-4 h-4" });
  $$payload.out += `<!----></a></div></div></section> <section class="py-20 bg-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Frequently Asked Questions</h2> <p class="text-xl text-gray-600">Everything you need to know about BottleCRM and free CRM software.</p></div> <div class="space-y-4"><div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 0)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Is BottleCRM really free to use?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 1)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">How does BottleCRM compare to traditional CRM platforms?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 2)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Can I self-host BottleCRM on my own servers?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 3)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">What technology stack does BottleCRM use?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 4)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Do you provide support for BottleCRM implementation?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden"><button class="w-full px-6 py-5 text-left focus:outline-none focus:bg-gray-100 hover:bg-gray-100 transition-colors duration-200"${attr("aria-expanded", activeFaq === 5)}><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-gray-900 pr-4">Is BottleCRM suitable for my industry?</h3> `;
  Chevron_down($$payload, {
    class: `w-5 h-5 text-gray-500 transform transition-transform duration-200 ${stringify("")}`
  });
  $$payload.out += `<!----></div></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></section> <section class="py-20 bg-gradient-to-r from-gray-50 to-blue-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="lg:flex lg:items-center lg:justify-between lg:space-x-12"><div class="lg:w-1/2"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-8">100% Free &amp; Open Source CRM Software</h2> <p class="text-xl text-gray-700 mb-8 leading-relaxed">BottleCRM is completely free, open-source CRM software hosted on GitHub. Download, customize, self-host, and contribute to the project without any licensing restrictions. Perfect for startups seeking a cost-effective CRM alternative to expensive subscription-based solutions.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10"><div class="bg-white p-6 rounded-xl shadow-lg"><h4 class="font-bold text-gray-900 mb-2">Zero Licensing Costs</h4> <p class="text-gray-600 text-sm">Download and use forever without any subscription fees or hidden costs</p></div> <div class="bg-white p-6 rounded-xl shadow-lg"><h4 class="font-bold text-gray-900 mb-2">Complete Customization</h4> <p class="text-gray-600 text-sm">Modify source code to match your exact business requirements</p></div> <div class="bg-white p-6 rounded-xl shadow-lg"><h4 class="font-bold text-gray-900 mb-2">Self-Hosting Options</h4> <p class="text-gray-600 text-sm">Host on your own servers for complete data control and privacy</p></div> <div class="bg-white p-6 rounded-xl shadow-lg"><h4 class="font-bold text-gray-900 mb-2">Active Community</h4> <p class="text-gray-600 text-sm">Benefit from community contributions and collaborative improvements</p></div></div> <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"><a href="https://github.com/MicroPyramid/opensource-startup-crm" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 shadow-lg transition-all duration-200 hover:scale-105 whitespace-nowrap">`;
  Github($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> ⭐ Star on GitHub</a> <a href="https://github.com/MicroPyramid/opensource-startup-crm/fork" target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 shadow-lg transition-all duration-200 whitespace-nowrap">`;
  Git_fork($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Fork &amp; Customize</a></div></div> <div class="mt-12 lg:mt-0 lg:w-1/2"><div class="bg-white rounded-xl p-8 border border-gray-200 shadow-xl"><div class="flex items-center mb-6">`;
  Github($$payload, { class: "h-10 w-10 text-gray-700 mr-4" });
  $$payload.out += `<!----> <div><h3 class="text-2xl font-bold text-gray-900">MicroPyramid/opensource-startup-crm</h3> <p class="text-gray-700">Free Open Source CRM: SvelteKit + Prisma</p></div></div> <div class="grid grid-cols-3 gap-4 mb-6"><div class="text-center"><div class="text-2xl font-bold text-blue-600">2+</div> <div class="text-sm text-gray-600">GitHub Stars</div></div> <div class="text-center"><div class="text-2xl font-bold text-green-600">0+</div> <div class="text-sm text-gray-600">Forks</div></div> <div class="text-center"><div class="text-2xl font-bold text-purple-600">MIT</div> <div class="text-sm text-gray-600">License</div></div></div> <div class="bg-gray-900 rounded-lg p-4 text-sm font-mono text-green-400 overflow-x-auto"><div class="mb-2 whitespace-nowrap"># Clone and install BottleCRM</div> <div class="text-gray-300 whitespace-nowrap">$ git clone https://github.com/MicroPyramid/opensource-startup-crm.git</div> <div class="text-gray-300 whitespace-nowrap">$ cd opensource-startup-crm</div> <div class="text-gray-300 whitespace-nowrap">$ pnpm install &amp;&amp; pnpm run dev</div> <div class="mt-2 text-yellow-400 whitespace-nowrap"># 🎉 Your free CRM is ready!</div></div></div></div></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20 relative overflow-hidden"><div class="absolute inset-0 bg-black/10"></div> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"><div class="text-center"><h2 class="text-4xl md:text-6xl font-extrabold mb-6">Ready to Stop Paying for CRM?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-4xl mx-auto leading-relaxed">Join the growing community of startups and small businesses who are ditching expensive CRM subscriptions. 
        Start managing customer relationships more effectively today with BottleCRM - completely free, forever.</p> <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto mb-12"><h3 class="text-2xl font-bold mb-4">💰 Your Annual Savings Calculator</h3> <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center"><div><div class="text-3xl font-bold text-yellow-300">$3,000+</div> <div class="text-sm text-blue-100">5 Users vs Enterprise CRM</div></div> <div><div class="text-3xl font-bold text-yellow-300">$6,000+</div> <div class="text-sm text-blue-100">10 Users vs Cloud CRM</div></div> <div><div class="text-3xl font-bold text-yellow-300">$18,000+</div> <div class="text-sm text-blue-100">30 Users vs Commercial CRM</div></div></div></div> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  Zap($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Start Your Free CRM Today</a> <a href="/contact" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Message_circle($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Get Professional Support</a></div> <p class="mt-8 text-blue-200 text-lg">🚀 No credit card • No setup fees • No user limits • No vendor lock-in</p></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Trusted by a Growing Global Community</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">BottleCRM is empowering startups and small businesses worldwide to build better customer relationships without breaking the bank.</p></div> <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4"><div class="bg-white rounded-2xl shadow-xl p-8 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"><div class="text-5xl font-extrabold text-blue-600 mb-2">100%</div> <div class="text-lg font-bold text-gray-700 mb-2">Free &amp; Open Source</div> <div class="text-sm text-gray-500">No hidden costs, licensing fees, or subscription charges ever.</div></div> <div class="bg-white rounded-2xl shadow-xl p-8 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"><div class="text-5xl font-extrabold text-green-600 mb-2">New!</div> <div class="text-lg font-bold text-gray-700 mb-2">Fresh &amp; Modern</div> <div class="text-sm text-gray-500">Built with the latest technologies and modern best practices.</div></div> <div class="bg-white rounded-2xl shadow-xl p-8 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"><div class="text-5xl font-extrabold text-purple-600 mb-2">MIT</div> <div class="text-lg font-bold text-gray-700 mb-2">Open License</div> <div class="text-sm text-gray-500">Complete freedom to use, modify, and distribute.</div></div> <div class="bg-white rounded-2xl shadow-xl p-8 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"><div class="text-5xl font-extrabold text-yellow-600 mb-2">24/7</div> <div class="text-lg font-bold text-gray-700 mb-2">Self-Hosted</div> <div class="text-sm text-gray-500">Run on your own servers with complete control over your data.</div></div></div> <div class="mt-16 text-center"><p class="text-gray-600 mb-8 text-lg">Developed with modern, secure, high-performance technologies:</p> <div class="flex flex-wrap justify-center items-center gap-4"><div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-md whitespace-nowrap"><span class="font-semibold text-gray-700">SvelteKit 2.21+</span></div> <div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-md whitespace-nowrap"><span class="font-semibold text-gray-700">Prisma ORM</span></div> <div class="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-md whitespace-nowrap"><span class="font-semibold text-gray-700">TailwindCSS 4.1+</span></div></div></div></div></section>`;
  pop();
}
export {
  _page as default
};
