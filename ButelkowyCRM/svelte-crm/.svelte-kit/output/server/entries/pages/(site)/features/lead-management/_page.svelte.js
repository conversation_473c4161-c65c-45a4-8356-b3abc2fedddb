import { E as spread_props, C as pop, A as push, P as head } from "../../../../../chunks/index2.js";
import { T as Target } from "../../../../../chunks/target.js";
import { I as Icon } from "../../../../../chunks/Icon.js";
import { T as Trending_up } from "../../../../../chunks/trending-up.js";
import { Z as Zap } from "../../../../../chunks/zap.js";
import { D as Dollar_sign } from "../../../../../chunks/dollar-sign.js";
import { S as Shield } from "../../../../../chunks/shield.js";
import { S as Settings } from "../../../../../chunks/settings.js";
import { C as Code } from "../../../../../chunks/code.js";
import { E as Eye } from "../../../../../chunks/eye.js";
function Workflow($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      {
        "width": "8",
        "height": "8",
        "x": "3",
        "y": "3",
        "rx": "2"
      }
    ],
    ["path", { "d": "M7 11v4a2 2 0 0 0 2 2h4" }],
    [
      "rect",
      {
        "width": "8",
        "height": "8",
        "x": "13",
        "y": "13",
        "rx": "2"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "workflow" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Free CRM Lead Management Software | BottleCRM</title>`;
    $$payload2.out += `<meta name="description" content="Free, open-source CRM lead management software for startups &amp; small businesses. Capture, score, nurture &amp; convert leads with automated workflows."> <meta name="keywords" content="lead management software, free crm, lead generation, lead nurturing, open source crm, sales crm, lead scoring, crm for startups, crm for small business"> <link rel="canonical" href="https://bottlecrm.io/features/lead-management"> <meta name="robots" content="index, follow"> <meta property="og:title" content="Free CRM Lead Management Software | BottleCRM"> <meta property="og:description" content="Free, open-source CRM lead management software for startups &amp; small businesses. Capture, score, nurture &amp; convert leads with automated workflows."> <meta property="og:type" content="website"> <meta property="og:url" content="https://bottlecrm.io/features/lead-management"> <meta property="og:image" content="https://bottlecrm.io/og-lead-management.png"> <meta name="twitter:card" content="summary_large_image"> <meta name="twitter:title" content="Free CRM Lead Management Software | BottleCRM"> <meta name="twitter:description" content="Free, open-source CRM lead management software for startups &amp; small businesses. Capture, score, nurture &amp; convert leads with automated workflows."> <meta name="twitter:image" content="https://bottlecrm.io/og-lead-management.png"> <script type="application/ld+json">{JSON.stringify(schema)}<\/script>`;
  });
  $$payload.out += `<section class="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white relative overflow-hidden"><div class="absolute inset-0 bg-black/10"></div> <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div> <div class="max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:px-8 relative z-10"><div class="text-center max-w-4xl mx-auto"><div class="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-6">`;
  Target($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> <span class="text-sm font-medium">Convert More Leads • Accelerate Growth • 100% Free</span></div> <h1 class="text-4xl md:text-6xl font-extrabold tracking-tight mb-6 leading-tight">Transform Prospects into <span class="text-yellow-300">Paying Customers</span></h1> <p class="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">Capture, score, nurture, and convert leads with BottleCRM's comprehensive lead management system. Stop losing potential customers and start maximizing your sales pipeline.</p> <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10"><div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">`;
  Target($$payload, {
    class: "w-10 h-10 text-yellow-300 mx-auto mb-3"
  });
  $$payload.out += `<!----> <div class="text-lg font-semibold mb-2">Smart Lead Scoring</div> <div class="text-sm text-blue-100">Automatically identify your hottest prospects</div></div> <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">`;
  Workflow($$payload, {
    class: "w-10 h-10 text-yellow-300 mx-auto mb-3"
  });
  $$payload.out += `<!----> <div class="text-lg font-semibold mb-2">Automated Nurturing</div> <div class="text-sm text-blue-100">Guide leads through personalized journeys</div></div> <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">`;
  Trending_up($$payload, {
    class: "w-10 h-10 text-yellow-300 mx-auto mb-3"
  });
  $$payload.out += `<!----> <div class="text-lg font-semibold mb-2">Higher Conversions</div> <div class="text-sm text-blue-100">Increase lead-to-customer conversion rates</div></div></div> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-4"><a href="/login" class="inline-flex items-center justify-center px-6 py-4 border border-transparent text-base font-semibold rounded-lg text-blue-700 bg-white hover:bg-gray-100 shadow-lg transition-all duration-200 hover:scale-105">`;
  Zap($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Start Managing Leads Free</a> <a href="#features" class="inline-flex items-center justify-center px-6 py-4 border-2 border-white text-base font-semibold rounded-lg text-white hover:bg-white/10 transition-all duration-200">Explore Lead Features</a></div></div></div></section> <section class="py-20 bg-gray-50" id="features"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Complete Lead Management Process</h2> <p class="text-xl text-gray-600 max-w-3xl mx-auto">From initial capture to final conversion, BottleCRM streamlines every step of your lead management process.</p></div> <div class="grid gap-8 lg:grid-cols-4">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Powerful Lead Management Features</h2> <p class="text-xl text-gray-600">Everything you need to capture, manage, and convert leads into customers.</p></div> <div class="grid gap-8 lg:grid-cols-2">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-gradient-to-r from-gray-50 to-blue-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Track What Matters Most</h2> <p class="text-xl text-gray-600">Monitor key lead metrics and optimize your conversion funnel with actionable insights.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Complete Lead Management Toolkit</h2> <p class="text-xl text-gray-600">All the tools you need to manage leads effectively, integrated into one platform.</p></div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></section> <section class="py-20 bg-gray-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="lg:flex lg:items-center lg:justify-between lg:space-x-12"><div class="lg:w-1/2"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-8">Why Choose BottleCRM for Lead Management?</h2> <div class="space-y-8"><div class="flex items-start"><div class="flex-shrink-0"><div class="rounded-lg bg-blue-100 p-3">`;
  Dollar_sign($$payload, { class: "w-6 h-6 text-blue-600" });
  $$payload.out += `<!----></div></div> <div class="ml-4"><h3 class="text-xl font-bold text-gray-900 mb-2">100% Free Forever</h3> <p class="text-gray-600">No per-lead fees, no monthly subscriptions. Use all lead management features completely free.</p></div></div> <div class="flex items-start"><div class="flex-shrink-0"><div class="rounded-lg bg-green-100 p-3">`;
  Shield($$payload, { class: "w-6 h-6 text-green-600" });
  $$payload.out += `<!----></div></div> <div class="ml-4"><h3 class="text-xl font-bold text-gray-900 mb-2">Complete Data Ownership</h3> <p class="text-gray-600">Self-host your lead data for complete privacy and control. No vendor lock-in.</p></div></div> <div class="flex items-start"><div class="flex-shrink-0"><div class="rounded-lg bg-purple-100 p-3">`;
  Settings($$payload, { class: "w-6 h-6 text-purple-600" });
  $$payload.out += `<!----></div></div> <div class="ml-4"><h3 class="text-xl font-bold text-gray-900 mb-2">Fully Customizable</h3> <p class="text-gray-600">Adapt lead scoring, workflows, and processes to match your unique business needs.</p></div></div> <div class="flex items-start"><div class="flex-shrink-0"><div class="rounded-lg bg-orange-100 p-3">`;
  Code($$payload, { class: "w-6 h-6 text-orange-600" });
  $$payload.out += `<!----></div></div> <div class="ml-4"><h3 class="text-xl font-bold text-gray-900 mb-2">Open Source</h3> <p class="text-gray-600">Built on open-source technologies with active community support and development.</p></div></div></div></div> <div class="mt-12 lg:mt-0 lg:w-1/2"><div class="bg-white rounded-2xl shadow-2xl p-8"><h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Lead Management ROI Calculator</h3> <div class="space-y-6"><div class="bg-gray-50 rounded-xl p-6"><div class="text-center"><div class="text-3xl font-extrabold text-blue-600 mb-2">5x</div> <div class="text-sm text-gray-600">Faster Lead Response Time</div></div></div> <div class="grid grid-cols-2 gap-4"><div class="bg-green-50 rounded-xl p-4 text-center"><div class="text-2xl font-bold text-green-600 mb-1">+25%</div> <div class="text-xs text-gray-600">Conversion Rate</div></div> <div class="bg-blue-50 rounded-xl p-4 text-center"><div class="text-2xl font-bold text-blue-600 mb-1">-40%</div> <div class="text-xs text-gray-600">Sales Cycle</div></div></div> <div class="text-center pt-4 border-t border-gray-200"><div class="text-lg font-semibold text-gray-700 mb-2">Potential Annual Savings</div> <div class="text-3xl font-extrabold text-green-600">$12,000+</div> <div class="text-sm text-gray-500">vs. paid lead management tools</div></div></div></div></div></div></div></section> <section class="py-20 bg-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16"><h2 class="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">Lead Management FAQs</h2> <p class="text-xl text-gray-600">Common questions about BottleCRM's lead management capabilities.</p></div> <div class="space-y-6"><div class="bg-gray-50 rounded-xl p-6"><h3 class="text-lg font-semibold text-gray-900 mb-3">How does lead scoring work in BottleCRM?</h3> <p class="text-gray-600">BottleCRM's lead scoring system automatically evaluates leads based on demographic information, behavioral data, and engagement levels. You can customize scoring rules to match your ideal customer profile and business priorities.</p></div> <div class="bg-gray-50 rounded-xl p-6"><h3 class="text-lg font-semibold text-gray-900 mb-3">Can I import leads from other systems?</h3> <p class="text-gray-600">Yes, BottleCRM supports bulk lead import from CSV files, API integrations, and direct migration from other CRM systems. Our duplicate detection ensures clean data integration.</p></div> <div class="bg-gray-50 rounded-xl p-6"><h3 class="text-lg font-semibold text-gray-900 mb-3">How are lead nurturing workflows automated?</h3> <p class="text-gray-600">Create automated sequences based on triggers like lead source, behavior, or time delays. Send personalized emails, assign tasks, and move leads through your sales funnel automatically.</p></div> <div class="bg-gray-50 rounded-xl p-6"><h3 class="text-lg font-semibold text-gray-900 mb-3">What lead analytics are available?</h3> <p class="text-gray-600">Track conversion rates, lead sources, response times, and pipeline velocity. Generate custom reports to identify your best-performing lead channels and optimize your strategy.</p></div></div></div></section> <section class="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-20"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-4xl md:text-5xl font-extrabold mb-6">Ready to Transform Your Lead Management?</h2> <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">Stop losing leads and start converting more prospects into customers. Try BottleCRM's lead management features today.</p> <div class="flex flex-col sm:flex-row sm:justify-center space-y-4 sm:space-y-0 sm:space-x-6"><a href="/login" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-blue-700 bg-white hover:bg-gray-100 shadow-2xl transition-all duration-200 hover:scale-105">`;
  Target($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> Start Managing Leads Free</a> <a href="/features" class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold rounded-xl text-white border-2 border-white hover:bg-white/10 transition-all duration-200">`;
  Eye($$payload, { class: "w-6 h-6 mr-3" });
  $$payload.out += `<!----> View All CRM Features</a></div> <p class="mt-8 text-blue-200 text-lg">🚀 No credit card • Unlimited leads • Self-host anywhere • Open source</p></div></section>`;
  pop();
}
export {
  _page as default
};
