import { p as prisma } from "../../../../../chunks/prisma.js";
import "../../../../../chunks/index.js";
async function load({ cookies }) {
}
const actions = {
  default: async ({ request, cookies, locals }) => {
    const user = locals.user;
    if (!user) {
      return {
        error: {
          name: "You must be logged in to create an organization"
        }
      };
    }
    const formData = await request.formData();
    const orgName = formData.get("org_name");
    try {
      const existingOrg = await prisma.organization.findFirst({
        where: {
          name: orgName
        }
      });
      if (existingOrg) {
        return {
          error: {
            name: "Organization with this name already exists"
          }
        };
      }
      const result = await prisma.$transaction(async (prisma2) => {
        const newOrg = await prisma2.organization.create({
          data: {
            name: orgName
          }
        });
        const userOrg = await prisma2.userOrganization.create({
          data: {
            userId: user.id,
            organizationId: newOrg.id,
            role: "ADMIN"
          }
        });
        return { newOrg, userOrg };
      });
      cookies.set("org", result.newOrg.id, {
        path: "/",
        httpOnly: true,
        sameSite: "strict"
      });
      return {
        data: {
          name: orgName
        }
      };
    } catch (err) {
      console.error("Error creating organization:", err);
      return {
        error: {
          name: "An unexpected error occurred while creating the organization."
        }
      };
    }
  }
};
export {
  actions,
  load
};
