import { P as head, M as bind_props, C as pop, A as push } from "../../../../chunks/index2.js";
/* empty css                     */
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Login | BottleCRM - Free Open-Source CRM for Startups</title>`;
    $$payload2.out += `<meta name="description" content="Sign in to BottleCRM - the completely free, open-source CRM solution for startups and small businesses. No subscription fees, unlimited users." class="svelte-14w1199">`;
  });
  $$payload.out += `<div class="min-h-screen bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 relative overflow-hidden svelte-14w1199"><div class="absolute inset-0 bg-black/10 svelte-14w1199"></div> <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent svelte-14w1199"></div> <div class="absolute top-10 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse svelte-14w1199"></div> <div class="absolute bottom-10 right-10 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse svelte-14w1199" style="animation-delay: 1s;"></div> <div class="relative z-10 flex items-center justify-center min-h-screen p-4 svelte-14w1199"><div class="w-full max-w-6xl mx-auto svelte-14w1199"><div class="grid lg:grid-cols-2 gap-12 items-center svelte-14w1199"><div class="hidden lg:block text-white svelte-14w1199">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="flex justify-center lg:justify-end svelte-14w1199">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
