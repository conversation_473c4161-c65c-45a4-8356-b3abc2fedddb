import { D as escape_html, M as bind_props, C as pop, A as push } from "../../../../../chunks/index2.js";
/* empty css                        */
import { g as goto } from "../../../../../chunks/client.js";
import { B as Building_2 } from "../../../../../chunks/building-2.js";
import { C as Circle_alert } from "../../../../../chunks/circle-alert.js";
import { C as Check } from "../../../../../chunks/check.js";
import { A as Arrow_left } from "../../../../../chunks/arrow-left.js";
function _page($$payload, $$props) {
  push();
  let form = $$props["form"];
  if (form?.data) {
    setTimeout(
      () => {
        goto();
      },
      1500
    );
  }
  $$payload.out += `<div class="min-h-screen bg-gray-50 px-4 py-8"><div class="max-w-lg mx-auto"><div class="text-center mb-8"><div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">`;
  Building_2($$payload, { class: "w-8 h-8 text-blue-600" });
  $$payload.out += `<!----></div> <h1 class="text-3xl font-bold text-gray-900 mb-2">Create Organization</h1> <p class="text-gray-600">Set up your new organization to get started</p></div> <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sm:p-8"><form action="/org/new" method="POST" class="space-y-6"><div class="space-y-2"><label for="org_name" class="block text-sm font-medium text-gray-700">Organization Name</label> <input type="text" id="org_name" name="org_name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white" placeholder="Enter organization name"></div> `;
  if (form?.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg">`;
    Circle_alert($$payload, { class: "w-5 h-5 text-red-500 flex-shrink-0" });
    $$payload.out += `<!----> <span class="text-red-700 text-sm">${escape_html(form.error.name)}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (form?.data) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-2 p-4 bg-green-50 border border-green-200 rounded-lg">`;
    Check($$payload, { class: "w-5 h-5 text-green-500 flex-shrink-0" });
    $$payload.out += `<!----> <span class="text-green-700 text-sm">Organization "${escape_html(form.data.name)}" created successfully!</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Create Organization</button> <div class="text-center"><a href="/org" class="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors duration-200">`;
  Arrow_left($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Back to Organizations</a></div></form></div></div></div>`;
  bind_props($$props, { form });
  pop();
}
export {
  _page as default
};
