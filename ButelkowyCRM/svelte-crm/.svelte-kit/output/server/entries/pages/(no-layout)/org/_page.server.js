import { p as prisma } from "../../../../chunks/prisma.js";
async function load({ cookies, locals }) {
  const user = locals.user;
  if (!user) {
    return { orgs: [] };
  }
  const userOrgs = await prisma.userOrganization.findMany({
    where: {
      userId: user.id
    },
    include: {
      organization: true
    }
  });
  const orgs = userOrgs.map((userOrg) => ({
    id: userOrg.organization.id,
    name: userOrg.organization.name,
    logo: userOrg.organization.logo,
    role: userOrg.role
  }));
  return { orgs };
}
export {
  load
};
