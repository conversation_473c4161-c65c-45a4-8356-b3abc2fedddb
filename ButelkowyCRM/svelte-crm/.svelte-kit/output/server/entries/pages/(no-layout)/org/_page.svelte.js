import { G as ensure_array_like, J as attr, R as stringify, D as escape_html, M as bind_props, C as pop, A as push } from "../../../../chunks/index2.js";
/* empty css                     */
import "../../../../chunks/client.js";
import { L as Log_out } from "../../../../chunks/log-out.js";
import { B as Building } from "../../../../chunks/building.js";
import { P as Plus } from "../../../../chunks/plus.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { orgs } = data;
  $$payload.out += `<div class="min-h-screen bg-gray-50 p-4"><div class="max-w-6xl mx-auto"><div class="flex justify-between items-center mb-8"><div><h1 class="text-3xl font-bold text-gray-900 mb-2">Select Organization</h1> <p class="text-gray-600">Choose an organization to continue</p></div> <a href="/logout" class="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200" title="Logout">`;
  Log_out($$payload, { class: "w-5 h-5" });
  $$payload.out += `<!----> <span class="hidden sm:inline">Logout</span></a></div> `;
  if (orgs.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(orgs);
    $$payload.out += `<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let org = each_array[$$index];
      $$payload.out += `<button class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-200 cursor-pointer group w-full text-left" type="button"${attr("aria-label", `Select ${stringify(org.name)} organization`)}><div class="p-6"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">`;
      Building($$payload, { class: "w-6 h-6 text-blue-600" });
      $$payload.out += `<!----></div> <div><h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">${escape_html(org.name)}</h3> <p class="text-sm text-gray-500 capitalize">${escape_html(org.role?.toLowerCase() || "Member")}</p></div></div></div> <div class="w-full py-2.5 px-4 bg-blue-600 group-hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">Select Organization</div></div></button>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="text-center py-16"><div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">`;
    Building($$payload, { class: "w-8 h-8 text-gray-400" });
    $$payload.out += `<!----></div> <h3 class="text-lg font-semibold text-gray-900 mb-2">No organizations found</h3> <p class="text-gray-600 mb-6">Create your first organization to get started</p> <a href="/org/new" class="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">`;
    Plus($$payload, { class: "w-5 h-5" });
    $$payload.out += `<!----> Create Organization</a></div>`;
  }
  $$payload.out += `<!--]--> `;
  if (orgs.length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a href="/org/new" class="fixed bottom-6 right-6 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl flex items-center justify-center transition-all duration-200 z-50" title="Create New Organization">`;
    Plus($$payload, { class: "w-6 h-6" });
    $$payload.out += `<!----></a>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
