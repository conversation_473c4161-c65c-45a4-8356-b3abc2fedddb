import { G as ensure_array_like, P as head, D as escape_html, J as attr, C as pop, A as push } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
import { A as Arrow_left } from "../../../../../../chunks/arrow-left.js";
import { T as Target } from "../../../../../../chunks/target.js";
import { B as Building_2 } from "../../../../../../chunks/building-2.js";
import { D as Dollar_sign } from "../../../../../../chunks/dollar-sign.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
function _page($$payload, $$props) {
  push();
  let { data, form } = $$props;
  let isSubmitting = false;
  let selectedContacts = [];
  let formData = {
    name: form?.data?.name || "",
    accountId: form?.data?.accountId || data.preSelectedAccountId || "",
    stage: form?.data?.stage || "PROSPECTING",
    amount: form?.data?.amount || "",
    closeDate: form?.data?.closeDate || "",
    probability: form?.data?.probability || "",
    type: form?.data?.type || "",
    leadSource: form?.data?.leadSource || "",
    nextStep: form?.data?.nextStep || "",
    description: form?.data?.description || "",
    ownerId: form?.data?.ownerId || ""
  };
  const opportunityStages = [
    {
      value: "PROSPECTING",
      label: "Prospecting",
      color: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
    },
    {
      value: "QUALIFICATION",
      label: "Qualification",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    },
    {
      value: "PROPOSAL",
      label: "Proposal",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    },
    {
      value: "NEGOTIATION",
      label: "Negotiation",
      color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
    },
    {
      value: "CLOSED_WON",
      label: "Closed Won",
      color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    },
    {
      value: "CLOSED_LOST",
      label: "Closed Lost",
      color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    }
  ];
  const opportunityTypes = [
    "New Business",
    "Existing Business",
    "Renewal",
    "Upsell",
    "Cross-sell"
  ];
  const leadSources = [
    "Website",
    "Referral",
    "Cold Call",
    "Trade Show",
    "Advertisement",
    "Partner",
    "Social Media",
    "Email Campaign",
    "Other"
  ];
  function calculateExpectedRevenue() {
    const amount = parseFloat(formData.amount);
    const probability = parseFloat(formData.probability);
    if (amount && probability) {
      return (amount * probability / 100).toFixed(2);
    }
    return 0;
  }
  const each_array = ensure_array_like(data.accounts);
  const each_array_1 = ensure_array_like(opportunityStages);
  const each_array_2 = ensure_array_like(data.users);
  const each_array_3 = ensure_array_like(opportunityTypes);
  const each_array_4 = ensure_array_like(leadSources);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>New Opportunity - BottleCRM</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16"><div class="flex items-center space-x-4"><button class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">`;
  Arrow_left($$payload, { class: "w-5 h-5" });
  $$payload.out += `<!----></button> <div class="flex items-center space-x-3"><div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">`;
  Target($$payload, {
    class: "w-6 h-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-xl font-semibold text-gray-900 dark:text-white">New Opportunity</h1> <p class="text-sm text-gray-500 dark:text-gray-400">Create a new sales opportunity</p></div></div></div></div></div></div> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">`;
  if (form?.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"><p class="text-red-600 dark:text-red-400">${escape_html(form.error)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" action="?/create" class="space-y-8"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h2 class="text-lg font-medium text-gray-900 dark:text-white mb-6 flex items-center">`;
  Building_2($$payload, {
    class: "w-5 h-5 mr-2 text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Basic Information</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="md:col-span-2"><label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Opportunity Name *</label> <input type="text" id="name" name="name"${attr("value", formData.name)} required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Enter opportunity name"> `;
  if (form?.errors?.name) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(form.errors.name)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="accountId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Account *</label> <select id="accountId" name="accountId" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"${attr("disabled", data.preSelectedAccountId, true)}><option value="">Select an account</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let account = each_array[$$index];
    $$payload.out += `<option${attr("value", account.id)}>${escape_html(account.name)} ${escape_html(account.type ? `(${account.type})` : "")}</option>`;
  }
  $$payload.out += `<!--]--></select> `;
  if (data.preSelectedAccountId) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Account pre-selected from ${escape_html(data.preSelectedAccountName)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (form?.errors?.accountId) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(form.errors.accountId)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="stage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stage *</label> <select id="stage" name="stage" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let stage = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", stage.value)}>${escape_html(stage.label)}</option>`;
  }
  $$payload.out += `<!--]--></select> `;
  if (form?.errors?.stage) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(form.errors.stage)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="ownerId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Owner</label> <select id="ownerId" name="ownerId" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><option value="">Assign to me</option><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let user = each_array_2[$$index_2];
    $$payload.out += `<option${attr("value", user.id)}>${escape_html(user.name || user.email)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label> <select id="type" name="type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><option value="">Select type</option><!--[-->`;
  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
    let type = each_array_3[$$index_3];
    $$payload.out += `<option${attr("value", type)}>${escape_html(type)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h2 class="text-lg font-medium text-gray-900 dark:text-white mb-6 flex items-center">`;
  Dollar_sign($$payload, {
    class: "w-5 h-5 mr-2 text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Financial Information</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"><div><label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Amount ($)</label> <input type="number" id="amount" name="amount"${attr("value", formData.amount)} min="0" step="0.01" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="0.00"> `;
  if (form?.errors?.amount) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(form.errors.amount)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="probability" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Probability (%)</label> <input type="number" id="probability" name="probability"${attr("value", formData.probability)} min="0" max="100" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="0"> `;
  if (form?.errors?.probability) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(form.errors.probability)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Expected Revenue</label> <div class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 text-gray-900 dark:text-white">$${escape_html(calculateExpectedRevenue())}</div></div> <div><label for="closeDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Expected Close Date</label> <input type="date" id="closeDate" name="closeDate"${attr("value", formData.closeDate)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></div> <div><label for="leadSource" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Lead Source</label> <select id="leadSource" name="leadSource" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><option value="">Select source</option><!--[-->`;
  for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
    let source = each_array_4[$$index_4];
    $$payload.out += `<option${attr("value", source)}>${escape_html(source)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h2 class="text-lg font-medium text-gray-900 dark:text-white mb-6">Additional Information</h2> <div class="grid grid-cols-1 gap-6">`;
  if (data.accountContacts.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_5 = ensure_array_like(data.accountContacts);
    $$payload.out += `<div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Associated Contacts ${escape_html(data.preSelectedAccountId ? `from ${data.preSelectedAccountName}` : "")}</label> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-3 bg-gray-50 dark:bg-gray-700"><!--[-->`;
    for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
      let contact = each_array_5[$$index_5];
      $$payload.out += `<label class="flex items-center space-x-2 cursor-pointer hover:bg-white dark:hover:bg-gray-600 p-2 rounded"><input type="checkbox" name="contactIds"${attr("value", contact.id)}${attr("checked", selectedContacts.includes(contact.id), true)} class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"> <span class="text-sm text-gray-900 dark:text-white">${escape_html(contact.firstName)} ${escape_html(contact.lastName)} `;
      if (contact.email) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="text-gray-500 dark:text-gray-400">(${escape_html(contact.email)})</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></span></label>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div><label for="nextStep" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Next Step</label> <input type="text" id="nextStep" name="nextStep"${attr("value", formData.nextStep)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="What's the next step?"></div> <div><label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label> <textarea id="description" name="description" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Additional details about this opportunity...">`;
  const $$body = escape_html(formData.description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div></div></div> <div class="flex items-center justify-end space-x-4 pt-6"><button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors">Cancel</button> <button type="submit"${attr("disabled", isSubmitting, true)} class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center">`;
  {
    $$payload.out += "<!--[!-->";
    Plus($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> Create Opportunity`;
  }
  $$payload.out += `<!--]--></button></div></form></div></div>`;
  pop();
}
export {
  _page as default
};
