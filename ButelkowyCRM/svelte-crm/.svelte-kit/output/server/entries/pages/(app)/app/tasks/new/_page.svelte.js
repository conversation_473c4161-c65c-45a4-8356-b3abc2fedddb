import { F as store_get, G as ensure_array_like, D as escape_html, J as attr, K as unsubscribe_stores, C as pop, A as push } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { X } from "../../../../../../chunks/x.js";
import { F as File_text } from "../../../../../../chunks/file-text.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
import { F as Flag } from "../../../../../../chunks/flag.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../chunks/user.js";
import { B as Building } from "../../../../../../chunks/building.js";
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data, form } = $$props;
  const urlAccountId = store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("accountId");
  let subject = form?.subject ?? "";
  form?.status ?? "Not Started";
  form?.priority ?? "Normal";
  let dueDate = form?.dueDate ?? "";
  form?.ownerId ?? "";
  let accountId = form?.accountId ?? urlAccountId ?? "";
  let description = form?.description ?? "";
  const selectedAccount = data.accounts.find((account) => account.id === accountId);
  const each_array = ensure_array_like(data.users);
  $$payload.out += `<div class="min-h-screen bg-gray-50 px-4 py-4 dark:bg-gray-900"><div class="mx-auto max-w-4xl"><div class="mb-4"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create New Task</h1> <p class="text-sm text-gray-600 dark:text-gray-300">`;
  if (selectedAccount) {
    $$payload.out += "<!--[-->";
    $$payload.out += `Add a new task for ${escape_html(selectedAccount.name)}`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `Add a new task to keep track of your work`;
  }
  $$payload.out += `<!--]--></p></div> <button type="button" class="rounded-lg p-2 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600 dark:text-gray-500 dark:hover:bg-gray-800 dark:hover:text-gray-300" aria-label="Close">`;
  X($$payload, { size: 20 });
  $$payload.out += `<!----></button></div></div> <div class="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">`;
  if (form?.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="border-l-4 border-red-400 bg-red-50 p-3 dark:border-red-500 dark:bg-red-900/20"><div class="flex"><div class="flex-shrink-0"><svg class="h-4 w-4 text-red-400 dark:text-red-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg></div> <div class="ml-2"><p class="text-sm font-medium text-red-700 dark:text-red-300">${escape_html(form.error)}</p></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" class="p-6">`;
  if (urlAccountId) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<input type="hidden" name="accountId"${attr("value", urlAccountId)}>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="space-y-5"><div><h2 class="mb-4 flex items-center text-base font-semibold text-gray-900 dark:text-white">`;
  File_text($$payload, {
    size: 18,
    class: "mr-2 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Task Details</h2> <div class="space-y-4"><div><label for="subject" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">Subject <span class="text-red-500 dark:text-red-400">*</span></label> <input type="text" id="subject" name="subject"${attr("value", subject)} class="w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 placeholder-gray-400 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:ring-blue-400" placeholder="Enter task subject" required></div> <div class="grid grid-cols-1 gap-4 md:grid-cols-3"><div><label for="status" class="mb-1 block flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">`;
  Clock($$payload, {
    size: 14,
    class: "mr-1 text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Status</label> <select id="status" name="status" class="w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400"><option value="Not Started">Not Started</option><option value="In Progress">In Progress</option><option value="Completed">Completed</option><option value="Waiting on someone else">Waiting on someone else</option><option value="Deferred">Deferred</option></select></div> <div><label for="priority" class="mb-1 block flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">`;
  Flag($$payload, {
    size: 14,
    class: "mr-1 text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Priority</label> <select id="priority" name="priority" class="w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400"><option value="High">High</option><option value="Normal">Normal</option><option value="Low">Low</option></select></div> <div><label for="dueDate" class="mb-1 block flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">`;
  Calendar($$payload, {
    size: 14,
    class: "mr-1 text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Due Date</label> <input type="date" id="dueDate" name="dueDate"${attr("value", dueDate)} class="w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400"></div></div></div></div> <div class="border-t border-gray-200 pt-5 dark:border-gray-700"><h2 class="mb-4 flex items-center text-base font-semibold text-gray-900 dark:text-white">`;
  User($$payload, {
    size: 18,
    class: "mr-2 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Assignment</h2> <div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><label for="ownerId" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">Owner <span class="text-red-500 dark:text-red-400">*</span></label> <select id="ownerId" name="ownerId" class="w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400" required><option value="" disabled>Select owner</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let user = each_array[$$index];
    $$payload.out += `<option${attr("value", user.id)}>${escape_html(user.name || user.email)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="accountId" class="mb-1 block flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">`;
  Building($$payload, {
    size: 14,
    class: "mr-1 text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Related Account</label> `;
  if (urlAccountId) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<select id="accountId" class="w-full rounded-lg border border-gray-300 bg-gray-100 px-3 py-2.5 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-white" disabled><option${attr("value", urlAccountId)} selected>${escape_html(selectedAccount?.name || "Loading...")}</option></select> <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Account pre-selected from URL</p>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_1 = ensure_array_like(data.accounts);
    $$payload.out += `<select id="accountId" name="accountId" class="w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400 dark:focus:ring-blue-400"><option value="">Select account (optional)</option><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let account = each_array_1[$$index_1];
      $$payload.out += `<option${attr("value", account.id)}>${escape_html(account.name)}</option>`;
    }
    $$payload.out += `<!--]--></select>`;
  }
  $$payload.out += `<!--]--></div></div></div> <div class="border-t border-gray-200 pt-5 dark:border-gray-700"><div><label for="description" class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label> <textarea id="description" name="description" class="w-full resize-none rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 placeholder-gray-400 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:ring-blue-400" rows="3" placeholder="Enter task details and notes...">`;
  const $$body = escape_html(description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div></div></div> <div class="mt-5 flex flex-col justify-end gap-3 border-t border-gray-200 pt-5 sm:flex-row dark:border-gray-700"><button type="button" class="rounded-lg border border-gray-300 bg-white px-5 py-2.5 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-white focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-800">Cancel</button> <button type="submit" class="rounded-lg bg-blue-600 px-5 py-2.5 text-sm font-semibold text-white shadow-sm transition-colors hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white focus:outline-none dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-400 dark:focus:ring-offset-gray-800">Create Task</button></div></form></div></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
