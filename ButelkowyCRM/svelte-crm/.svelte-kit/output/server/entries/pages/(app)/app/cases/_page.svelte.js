import { G as ensure_array_like, D as escape_html, J as attr, I as attr_class, M as bind_props, C as pop, A as push } from "../../../../../chunks/index2.js";
import "../../../../../chunks/client.js";
import { B as Briefcase } from "../../../../../chunks/briefcase.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { F as Funnel } from "../../../../../chunks/funnel.js";
import { X } from "../../../../../chunks/x.js";
function _page($$payload, $$props) {
  push();
  let filteredCases, hasActiveFilters;
  let data = $$props["data"];
  let statusFilter = "";
  let assignedFilter = "";
  let accountFilter = "";
  const statusOptions = data.statusOptions;
  const assignedOptions = data.allUsers.map((u) => u.name);
  const accountOptions = data.allAccounts.map((a) => a.name);
  function statusColor(status) {
    return status === "OPEN" ? "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-700" : status === "IN_PROGRESS" ? "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-700" : "bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-600";
  }
  function priorityColor(priority) {
    return priority === "High" ? "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-700" : priority === "Medium" ? "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-700" : "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700";
  }
  filteredCases = data.cases.filter((c) => true);
  hasActiveFilters = accountFilter;
  const each_array = ensure_array_like(statusOptions);
  const each_array_1 = ensure_array_like(assignedOptions);
  const each_array_2 = ensure_array_like(accountOptions);
  $$payload.out += `<div class="min-h-screen bg-slate-50 dark:bg-slate-900"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8"><div class="flex items-center gap-3"><div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">`;
  Briefcase($$payload, {
    class: "w-6 h-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-2xl font-bold text-slate-900 dark:text-white">Cases</h1> <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">Manage customer support cases and issues</p></div></div> <a href="/app/cases/new" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white font-medium rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200 shadow-sm">`;
  Plus($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> New Case</a></div> <div class="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6 mb-6"><div class="flex items-center gap-2 mb-4">`;
  Funnel($$payload, {
    class: "w-4 h-4 text-slate-600 dark:text-slate-400"
  });
  $$payload.out += `<!----> <h3 class="text-sm font-semibold text-slate-900 dark:text-white">Filters</h3> `;
  if (hasActiveFilters) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-full">${escape_html([statusFilter, assignedFilter, accountFilter].filter(Boolean).length)} active</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"><div><label for="status" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">Status</label> <select id="status" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"><option value="">All Statuses</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let s = each_array[$$index];
    $$payload.out += `<option${attr("value", s)}>${escape_html(s.replace("_", " "))}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="assigned" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">Assigned To</label> <select id="assigned" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"><option value="">All Users</option><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let a = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", a)}>${escape_html(a)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="account" class="block text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">Account</label> <select id="account" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"><option value="">All Accounts</option><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let acc = each_array_2[$$index_2];
    $$payload.out += `<option${attr("value", acc)}>${escape_html(acc)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> `;
  if (hasActiveFilters) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-end"><button class="inline-flex items-center gap-1 px-3 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-slate-700 rounded-lg transition-colors duration-200">`;
    X($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Clear</button></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden">`;
  if (filteredCases.length) {
    $$payload.out += "<!--[-->";
    const each_array_3 = ensure_array_like(filteredCases);
    const each_array_4 = ensure_array_like(filteredCases);
    $$payload.out += `<div class="hidden lg:block overflow-x-auto"><table class="w-full"><thead class="bg-slate-50 dark:bg-slate-900/50 border-b border-slate-200 dark:border-slate-700"><tr><th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Case</th><th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Account</th><th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Assigned</th><th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Due Date</th><th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Priority</th><th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Status</th><th class="px-6 py-4 text-right text-xs font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider">Actions</th></tr></thead><tbody class="divide-y divide-slate-200 dark:divide-slate-700"><!--[-->`;
    for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
      let c = each_array_3[$$index_3];
      $$payload.out += `<tr class="hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors duration-150"><td class="px-6 py-4"><div><a${attr("href", `/app/cases/${c.id}`)} class="font-semibold text-slate-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">${escape_html(c.subject)}</a> `;
      if (c.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">${escape_html(c.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></td><td class="px-6 py-4"><span class="text-sm text-slate-900 dark:text-white">${escape_html(c.account?.name || "-")}</span></td><td class="px-6 py-4"><div class="flex items-center gap-2"><div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center"><span class="text-sm font-medium text-blue-700 dark:text-blue-300">${escape_html(c.owner?.name?.[0] || "?")}</span></div> <span class="text-sm text-slate-900 dark:text-white">${escape_html(c.owner?.name || "Unassigned")}</span></div></td><td class="px-6 py-4"><span class="text-sm text-slate-900 dark:text-white">${escape_html(c.dueDate ? new Date(c.dueDate).toLocaleDateString() : "-")}</span></td><td class="px-6 py-4"><span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${priorityColor(c.priority)}`)}>${escape_html(c.priority)}</span></td><td class="px-6 py-4"><span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusColor(c.status)}`)}>${escape_html(c.status.replace("_", " "))}</span></td><td class="px-6 py-4 text-right"><a${attr("href", `/app/cases/${c.id}`)} class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200">View</a></td></tr>`;
    }
    $$payload.out += `<!--]--></tbody></table></div> <div class="lg:hidden divide-y divide-slate-200 dark:divide-slate-700"><!--[-->`;
    for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
      let c = each_array_4[$$index_4];
      $$payload.out += `<div class="p-4 hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors duration-150"><div class="flex justify-between items-start mb-3"><a${attr("href", `/app/cases/${c.id}`)} class="font-semibold text-slate-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">${escape_html(c.subject)}</a> <span${attr_class(`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${statusColor(c.status)}`)}>${escape_html(c.status.replace("_", " "))}</span></div> `;
      if (c.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-slate-600 dark:text-slate-400 mb-3 line-clamp-2">${escape_html(c.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="grid grid-cols-2 gap-3 text-sm mb-4"><div><span class="text-slate-500 dark:text-slate-400">Account:</span> <span class="text-slate-900 dark:text-white ml-1">${escape_html(c.account?.name || "-")}</span></div> <div><span class="text-slate-500 dark:text-slate-400">Priority:</span> <span${attr_class(`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ml-1 ${priorityColor(c.priority)}`)}>${escape_html(c.priority)}</span></div> <div><span class="text-slate-500 dark:text-slate-400">Assigned:</span> <span class="text-slate-900 dark:text-white ml-1">${escape_html(c.owner?.name || "Unassigned")}</span></div> <div><span class="text-slate-500 dark:text-slate-400">Due:</span> <span class="text-slate-900 dark:text-white ml-1">${escape_html(c.dueDate ? new Date(c.dueDate).toLocaleDateString() : "-")}</span></div></div> <div class="flex justify-end"><a${attr("href", `/app/cases/${c.id}`)} class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200">View Details</a></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="p-12 text-center"><div class="w-16 h-16 mx-auto mb-4 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">`;
    Briefcase($$payload, {
      class: "w-8 h-8 text-slate-400 dark:text-slate-500"
    });
    $$payload.out += `<!----></div> <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">No cases found</h3> <p class="text-slate-500 dark:text-slate-400 mb-6">${escape_html(hasActiveFilters ? "No cases match your current filters." : "Get started by creating your first case.")}</p> `;
    if (hasActiveFilters) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-slate-700 rounded-lg transition-colors duration-200">`;
      X($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> Clear Filters</button>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<a href="/app/cases/new" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white font-medium rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200">`;
      Plus($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> Create Case</a>`;
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
