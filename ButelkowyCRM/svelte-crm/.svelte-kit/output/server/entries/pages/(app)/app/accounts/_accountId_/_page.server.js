import { e as error, f as fail } from "../../../../../../chunks/index.js";
import { p as prisma } from "../../../../../../chunks/prisma.js";
async function load({ params, url, locals }) {
  locals.user;
  const org = locals.org;
  try {
    const accountId = params.accountId;
    const account = await prisma.account.findUnique({
      where: {
        id: accountId,
        organizationId: org.id
      }
    });
    if (!account) {
      throw error(404, "Account not found");
    }
    const contactRelationships = await prisma.accountContactRelationship.findMany({
      where: {
        accountId
      },
      include: {
        contact: true
      }
    });
    const contacts = contactRelationships.map((rel) => ({
      ...rel.contact,
      isPrimary: rel.isPrimary,
      role: rel.role
    }));
    const opportunities = await prisma.opportunity.findMany({
      where: {
        accountId
      }
    });
    const comments = await prisma.comment.findMany({
      where: {
        accountId
      },
      include: {
        author: {
          select: {
            name: true,
            id: true
          }
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });
    if (url.searchParams.get("commentsOnly") === "1") {
      return new Response(JSON.stringify({ comments }), { headers: { "Content-Type": "application/json" } });
    }
    const quotes = await prisma.quote.findMany({
      where: {
        accountId
      }
    });
    const tasks = await prisma.task.findMany({
      where: {
        accountId
      },
      include: {
        owner: {
          select: {
            name: true,
            id: true
          }
        }
      }
    });
    const cases = await prisma.case.findMany({
      where: {
        accountId
      }
    });
    const users = await prisma.user.findMany({
      where: {
        organizations: {
          some: { organizationId: account.organizationId }
        }
      },
      select: { id: true, name: true, email: true }
    });
    return {
      account,
      contacts,
      opportunities,
      comments,
      quotes,
      tasks,
      cases,
      users,
      meta: {
        title: account.name,
        description: `Account details for ${account.name}`
      }
    };
  } catch (err) {
    console.error("Error loading account data:", err);
    throw error(err.status || 500, err.message || "Error loading account data");
  }
}
const actions = {
  closeAccount: async ({ request: request2, locals, params }) => {
    try {
      const user = locals.user;
      const org = locals.org;
      const { accountId } = params;
      const formData = await request2.formData();
      const closureReason = formData.get("closureReason")?.toString();
      if (!closureReason) {
        return fail(400, { success: false, message: "Please provide a reason for closing this account" });
      }
      const account = await prisma.account.findUnique({
        where: { id: accountId, organizationId: org.id },
        select: {
          id: true,
          closedAt: true,
          organizationId: true,
          ownerId: true
        }
      });
      if (!account) {
        return fail(404, { success: false, message: "Account not found" });
      }
      if (account.closedAt) {
        return fail(400, { success: false, message: "Account is already closed" });
      }
      const userOrg = await prisma.userOrganization.findFirst({
        where: {
          userId: user.id,
          organizationId: account.organizationId
        }
      });
      const hasPermission = user.id === account.ownerId || userOrg?.role === "ADMIN";
      if (!hasPermission) {
        return fail(403, { success: false, message: "Permission denied. Only account owners, sales managers, or admins can close accounts." });
      }
      const updatedAccount = await prisma.account.update({
        where: { id: accountId },
        data: {
          closedAt: /* @__PURE__ */ new Date(),
          isActive: false,
          closureReason
        }
      });
      await prisma.auditLog.create({
        data: {
          action: "UPDATE",
          entityType: "Account",
          entityId: accountId,
          description: `Account closed: ${closureReason}`,
          oldValues: { closedAt: null, closureReason: null },
          newValues: { closedAt: updatedAccount.closedAt, closureReason },
          userId: user.id,
          organizationId: account.organizationId,
          ipAddress: request2.headers.get("x-forwarded-for") || "unknown"
        }
      });
      return { success: true };
    } catch (error2) {
      console.error("Error closing account:", error2);
      return fail(500, { success: false, message: "An unexpected error occurred" });
    }
  },
  reopenAccount: async ({ locals, params }) => {
    try {
      const user = locals.user;
      const org = locals.org;
      const { accountId } = params;
      const account = await prisma.account.findUnique({
        where: { id: accountId, organizationId: org.id },
        select: {
          id: true,
          closedAt: true,
          closureReason: true,
          organizationId: true,
          ownerId: true
        }
      });
      if (!account) {
        return fail(404, { success: false, message: "Account not found" });
      }
      if (!account.closedAt) {
        return fail(400, { success: false, message: "Account is not closed" });
      }
      const userOrg = await prisma.userOrganization.findFirst({
        where: {
          userId: user.id,
          organizationId: account.organizationId
        }
      });
      const hasPermission = user.id === account.ownerId || userOrg?.role === "ADMIN";
      if (!hasPermission) {
        return fail(403, { success: false, message: "Permission denied. Only account owners, sales managers, or admins can reopen accounts." });
      }
      const oldValues = {
        closedAt: account.closedAt,
        closureReason: account.closureReason
      };
      const updatedAccount = await prisma.account.update({
        where: { id: accountId },
        data: {
          closedAt: null,
          isActive: true,
          closureReason: null
        }
      });
      await prisma.auditLog.create({
        data: {
          action: "UPDATE",
          entityType: "Account",
          entityId: accountId,
          description: `Account reopened`,
          oldValues,
          newValues: { closedAt: null, closureReason: null },
          userId: user.id,
          organizationId: account.organizationId,
          ipAddress: request.headers.get("x-forwarded-for") || "unknown"
        }
      });
      return { success: true };
    } catch (error2) {
      console.error("Error reopening account:", error2);
      return fail(500, { success: false, message: "An unexpected error occurred" });
    }
  },
  comment: async ({ request: request2, locals, params }) => {
    locals.user;
    const org = locals.org;
    const account = await prisma.account.findUnique({
      where: { id: params.accountId, organizationId: org.id },
      select: { organizationId: true, ownerId: true }
    });
    if (!account) {
      return fail(404, { error: "Account not found." });
    }
    const authorId = account.ownerId;
    const organizationId = account.organizationId;
    const form = await request2.formData();
    const body = form.get("body")?.toString().trim();
    if (!body) return fail(400, { error: "Comment cannot be empty." });
    await prisma.comment.create({
      data: {
        body,
        authorId,
        organizationId,
        accountId: params.accountId
      }
    });
    return { success: true };
  }
};
export {
  actions,
  load
};
