import { e as error } from "../../../../../chunks/index.js";
import { p as prisma } from "../../../../../chunks/prisma.js";
async function load({ locals, url, params }) {
  const org = locals.org;
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "10");
  const sort = url.searchParams.get("sort") || "name";
  const order = url.searchParams.get("order") || "asc";
  const skip = (page - 1) * limit;
  try {
    const where = { organizationId: org.id };
    const status = url.searchParams.get("status");
    if (status === "open") {
      where.closedAt = null;
      where.active = true;
    } else if (status === "closed") {
      where.closedAt = { not: null };
    }
    const accounts = await prisma.account.findMany({
      where,
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            profilePhoto: true
          }
        },
        opportunities: {
          select: {
            id: true,
            stage: true,
            amount: true
          }
        },
        relatedContacts: {
          select: {
            contact: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        },
        tasks: {
          select: {
            id: true,
            status: true
          }
        }
      },
      orderBy: {
        [sort]: order
      },
      skip,
      take: limit
    });
    const total = await prisma.account.count({ where });
    return {
      accounts: accounts.map((account) => ({
        ...account,
        isActive: account.isActive,
        // Use only the active field, ignore closedAt for display purposes
        opportunityCount: account.opportunities.length,
        contactCount: account.relatedContacts.length,
        taskCount: account.tasks.length,
        openOpportunities: account.opportunities.filter(
          (opp) => !["CLOSED_WON", "CLOSED_LOST"].includes(opp.stage)
        ).length,
        totalOpportunityValue: account.opportunities.reduce((sum, opp) => sum + (opp.amount || 0), 0),
        // Keep the arrays but transformed/simplified
        topContacts: account.relatedContacts.slice(0, 3).map((rc) => ({
          id: rc.contact.id,
          name: `${rc.contact.firstName} ${rc.contact.lastName}`
        })),
        opportunities: void 0,
        relatedContacts: void 0,
        tasks: void 0
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (err) {
    console.error("Error fetching accounts:", err);
    throw error(500, "Failed to fetch accounts");
  }
}
export {
  load
};
