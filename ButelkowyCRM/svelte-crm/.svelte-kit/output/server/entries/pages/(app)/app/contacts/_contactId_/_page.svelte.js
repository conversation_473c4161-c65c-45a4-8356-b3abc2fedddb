import { J as attr, R as stringify, D as escape_html, G as ensure_array_like, I as attr_class, M as bind_props, C as pop, A as push } from "../../../../../../chunks/index2.js";
import { A as Arrow_left } from "../../../../../../chunks/arrow-left.js";
import { S as Star } from "../../../../../../chunks/star.js";
import { U as Users } from "../../../../../../chunks/users.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
import { S as Square_pen } from "../../../../../../chunks/square-pen.js";
import { U as User } from "../../../../../../chunks/user.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../../chunks/phone.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { B as Building_2 } from "../../../../../../chunks/building-2.js";
import { M as Map_pin } from "../../../../../../chunks/map-pin.js";
import { T as Target } from "../../../../../../chunks/target.js";
import { E as External_link } from "../../../../../../chunks/external-link.js";
import { D as Dollar_sign } from "../../../../../../chunks/dollar-sign.js";
import { C as Circle_check_big } from "../../../../../../chunks/circle-check-big.js";
import { C as Circle } from "../../../../../../chunks/circle.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { contact } = data;
  const primaryAccountRel = contact.accountRelationships?.find((rel) => rel.isPrimary);
  const hasMultipleAccounts = contact.accountRelationships?.length > 1;
  function formatDate(dateStr) {
    if (!dateStr) return "N/A";
    return new Date(dateStr).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  function formatDateTime(dateStr) {
    if (!dateStr) return "N/A";
    return new Date(dateStr).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "2-digit"
    });
  }
  function formatCurrency(amount) {
    if (!amount) return "$0";
    return new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(amount);
  }
  function getStatusColor(status) {
    const colors = {
      "Completed": "text-green-600 bg-green-50 dark:bg-green-900/20",
      "In Progress": "text-blue-600 bg-blue-50 dark:bg-blue-900/20",
      "Not Started": "text-gray-600 bg-gray-50 dark:bg-gray-900/20",
      "CLOSED_WON": "text-green-600 bg-green-50 dark:bg-green-900/20",
      "CLOSED_LOST": "text-red-600 bg-red-50 dark:bg-red-900/20",
      "NEGOTIATION": "text-orange-600 bg-orange-50 dark:bg-orange-900/20",
      "PROPOSAL": "text-purple-600 bg-purple-50 dark:bg-purple-900/20"
    };
    return colors[status] || "text-gray-600 bg-gray-50 dark:bg-gray-900/20";
  }
  function getPriorityColor(priority) {
    const colors = {
      "High": "text-red-600 bg-red-50 dark:bg-red-900/20",
      "Normal": "text-blue-600 bg-blue-50 dark:bg-blue-900/20",
      "Low": "text-gray-600 bg-gray-50 dark:bg-gray-900/20"
    };
    return colors[priority] || "text-gray-600 bg-gray-50 dark:bg-gray-900/20";
  }
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between py-6"><div class="flex items-center gap-4">`;
  if (primaryAccountRel) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `/app/accounts/${stringify(primaryAccountRel.account.id)}`)} class="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 flex items-center transition-colors">`;
    Arrow_left($$payload, { class: "w-5 h-5 mr-2" });
    $$payload.out += `<!----> Back to ${escape_html(primaryAccountRel.account.name)}</a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<a href="/app/contacts" class="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 flex items-center transition-colors">`;
    Arrow_left($$payload, { class: "w-5 h-5 mr-2" });
    $$payload.out += `<!----> Back to Contacts</a>`;
  }
  $$payload.out += `<!--]--> <div class="flex items-center gap-3"><div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">${escape_html(contact.firstName?.[0])}${escape_html(contact.lastName?.[0])}</div> <div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(contact.firstName)} ${escape_html(contact.lastName)}</h1> <p class="text-gray-500 dark:text-gray-400">${escape_html(contact.title || "Contact")}</p></div> `;
  if (primaryAccountRel?.isPrimary) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full flex items-center gap-1">`;
    Star($$payload, { class: "w-3 h-3" });
    $$payload.out += `<!----> Primary</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (hasMultipleAccounts) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 rounded-full flex items-center gap-1">`;
    Users($$payload, { class: "w-3 h-3" });
    $$payload.out += `<!----> ${escape_html(contact.accountRelationships.length)} Accounts</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="flex gap-3 mt-4 sm:mt-0"><button class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors flex items-center gap-2">`;
  Plus($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Add Task</button> <a${attr("href", `/app/contacts/${stringify(contact.id)}/edit`)} class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">`;
  Square_pen($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Edit</a></div></div></div></div> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="grid grid-cols-1 lg:grid-cols-4 gap-8"><div class="lg:col-span-3 space-y-8"><div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">`;
  User($$payload, { class: "w-5 h-5" });
  $$payload.out += `<!----> Contact Information</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-4"><div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label> `;
  if (contact.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `mailto:${stringify(contact.email)}`)} class="flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:underline mt-1">`;
    Mail($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> ${escape_html(contact.email)}</a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="text-gray-900 dark:text-white mt-1">N/A</p>`;
  }
  $$payload.out += `<!--]--></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label> `;
  if (contact.phone) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `tel:${stringify(contact.phone)}`)} class="flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:underline mt-1">`;
    Phone($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> ${escape_html(contact.phone)}</a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="text-gray-900 dark:text-white mt-1">N/A</p>`;
  }
  $$payload.out += `<!--]--></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Department</label> <p class="text-gray-900 dark:text-white mt-1">${escape_html(contact.department || "N/A")}</p></div></div> <div class="space-y-4"><div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</label> <p class="text-gray-900 dark:text-white mt-1">${escape_html(contact.title || "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Owner</label> <p class="text-gray-900 dark:text-white mt-1">${escape_html(contact.owner?.name || "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</label> <p class="text-gray-900 dark:text-white mt-1 flex items-center gap-2">`;
  Calendar($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> ${escape_html(formatDate(contact.createdAt))}</p></div></div></div> `;
  if (contact.description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</label> <p class="text-gray-900 dark:text-white mt-2">${escape_html(contact.description)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  if (contact.accountRelationships && contact.accountRelationships.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(contact.accountRelationships);
    $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">`;
    Building_2($$payload, { class: "w-5 h-5" });
    $$payload.out += `<!----> Account Relationships <span class="text-sm font-normal text-gray-500 dark:text-gray-400">(${escape_html(contact.accountRelationships.length)})</span></h2> <div class="space-y-4"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let relationship = each_array[$$index];
      $$payload.out += `<div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600"><div class="flex-1"><div class="flex items-center gap-3"><a${attr("href", `/app/accounts/${stringify(relationship.account.id)}`)} class="font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 flex items-center gap-2">`;
      Building_2($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> ${escape_html(relationship.account.name)}</a> `;
      if (relationship.isPrimary) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="inline-flex px-2 py-0.5 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full flex items-center gap-1">`;
        Star($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> Primary</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="mt-1 flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">`;
      if (relationship.role) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="flex items-center gap-1">`;
        User($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> ${escape_html(relationship.role)}</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <span class="flex items-center gap-1">`;
      Calendar($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Since ${escape_html(formatDate(relationship.startDate))}</span></div> `;
      if (relationship.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="mt-2 text-sm text-gray-600 dark:text-gray-300">${escape_html(relationship.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="text-right"><span class="text-xs text-gray-500 dark:text-gray-400">${escape_html(relationship.account.type || "Account")}</span></div></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (contact.street || contact.city || contact.state || contact.country) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">`;
    Map_pin($$payload, { class: "w-5 h-5" });
    $$payload.out += `<!----> Address</h2> <div class="space-y-2 text-gray-900 dark:text-white">`;
    if (contact.street) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p>${escape_html(contact.street)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <p>${escape_html(contact.city || "")}${escape_html(contact.city && contact.state ? ", " : "")}${escape_html(contact.state || "")} ${escape_html(contact.postalCode || "")}</p> `;
    if (contact.country) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p>${escape_html(contact.country)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (contact.opportunities && contact.opportunities.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_1 = ensure_array_like(contact.opportunities);
    $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between mb-6"><h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">`;
    Target($$payload, { class: "w-5 h-5" });
    $$payload.out += `<!----> Opportunities</h2> <a${attr("href", `/app/opportunities?contact=${stringify(contact.id)}`)} class="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-1">View all `;
    External_link($$payload, { class: "w-3 h-3" });
    $$payload.out += `<!----></a></div> <div class="space-y-4"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let opp = each_array_1[$$index_1];
      $$payload.out += `<div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg"><div class="flex-1"><a${attr("href", `/app/opportunities/${stringify(opp.id)}`)} class="font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400">${escape_html(opp.name)}</a> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(opp.account?.name)}</p></div> <div class="text-right"><p class="font-medium text-gray-900 dark:text-white flex items-center gap-1">`;
      Dollar_sign($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> ${escape_html(formatCurrency(opp.amount))}</p> <span${attr_class(`inline-flex px-2 py-1 text-xs rounded-full ${stringify(getStatusColor(opp.stage))}`)}>${escape_html(opp.stage.replace("_", " "))}</span></div></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="lg:col-span-1 space-y-8">`;
  if (contact.tasks && contact.tasks.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_2 = ensure_array_like(contact.tasks);
    $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between mb-6"><h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">`;
    Circle_check_big($$payload, { class: "w-5 h-5" });
    $$payload.out += `<!----> Recent Tasks</h3> <a${attr("href", `/app/tasks?contact=${stringify(contact.id)}`)} class="text-sm text-blue-600 dark:text-blue-400 hover:underline">View all</a></div> <div class="space-y-3"><!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
      let task = each_array_2[$$index_2];
      $$payload.out += `<div class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">`;
      if (task.status === "Completed") {
        $$payload.out += "<!--[-->";
        Circle_check_big($$payload, { class: "w-4 h-4 text-green-500 mt-0.5" });
      } else {
        $$payload.out += "<!--[!-->";
        Circle($$payload, { class: "w-4 h-4 text-gray-400 mt-0.5" });
      }
      $$payload.out += `<!--]--> <div class="flex-1 min-w-0"><p class="text-sm font-medium text-gray-900 dark:text-white truncate">${escape_html(task.subject)}</p> <div class="flex items-center gap-2 mt-1"><span${attr_class(`inline-flex px-2 py-0.5 text-xs rounded ${stringify(getPriorityColor(task.priority))}`)}>${escape_html(task.priority)}</span> `;
      if (task.dueDate) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">`;
        Clock($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> ${escape_html(formatDate(task.dueDate))}</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (contact.events && contact.events.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_3 = ensure_array_like(contact.events);
    $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between mb-6"><h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">`;
    Calendar($$payload, { class: "w-5 h-5" });
    $$payload.out += `<!----> Recent Events</h3> <a${attr("href", `/app/events?contact=${stringify(contact.id)}`)} class="text-sm text-blue-600 dark:text-blue-400 hover:underline">View all</a></div> <div class="space-y-3"><!--[-->`;
    for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
      let event = each_array_3[$$index_3];
      $$payload.out += `<div class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"><p class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(event.subject)}</p> <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-1">`;
      Calendar($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> ${escape_html(formatDateTime(event.startDate))}</p> `;
      if (event.location) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">`;
        Map_pin($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> ${escape_html(event.location)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
