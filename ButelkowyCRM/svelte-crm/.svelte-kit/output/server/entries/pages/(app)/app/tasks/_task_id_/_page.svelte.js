import { I as attr_class, D as escape_html, R as stringify, J as attr, G as ensure_array_like, M as bind_props, C as pop, A as push } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
import { A as Arrow_left } from "../../../../../../chunks/arrow-left.js";
import { P as Pen_line } from "../../../../../../chunks/pen-line.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../chunks/user.js";
import { B as Building_2 } from "../../../../../../chunks/building-2.js";
import { M as Message_square } from "../../../../../../chunks/message-square.js";
import { S as Send } from "../../../../../../chunks/send.js";
function _page($$payload, $$props) {
  push();
  let task;
  let data = $$props["data"];
  let form = $$props["form"];
  let newComment = "";
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString(void 0, {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    } catch (e) {
      return dateString;
    }
  }
  task = data.task;
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="max-w-4xl mx-auto p-3 sm:p-4 lg:p-6"><div class="mb-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><button class="flex items-center justify-center w-8 h-8 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" aria-label="Back to tasks">`;
  Arrow_left($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----></button> <div><h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Task Details</h1> <p class="text-sm text-gray-600 dark:text-gray-400">View and manage task information</p></div></div> <button class="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-600 dark:bg-blue-500 text-white font-medium shadow-sm hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors text-sm">`;
  Pen_line($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span class="hidden sm:inline">Edit</span></button></div></div> `;
  if (task) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-6"><div class="p-4 border-b border-gray-100 dark:border-gray-700"><div class="flex flex-wrap items-center gap-2 mb-3"><span${attr_class(`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${stringify(task.status === "Completed" ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 border border-green-200 dark:border-green-800" : "")} ${stringify(task.status === "In Progress" ? "bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400 border border-amber-200 dark:border-amber-800" : "")} ${stringify(task.status === "Not Started" ? "bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600" : "")}`)}>${escape_html(task.status)}</span> <span${attr_class(`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${stringify(task.priority === "High" ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800" : "")} ${stringify(task.priority === "Normal" ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border border-blue-200 dark:border-blue-800" : "")} ${stringify(task.priority === "Low" ? "bg-slate-50 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-600" : "")}`)}>${escape_html(task.priority)}</span> <div class="flex items-center gap-1.5 text-xs text-gray-600 dark:text-gray-400 ml-auto">`;
    Calendar($$payload, { class: "w-3.5 h-3.5" });
    $$payload.out += `<!----> <span>Due ${escape_html(formatDate(task.dueDate))}</span></div></div> <h2 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2">${escape_html(task.subject)}</h2> `;
    if (task.description) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">${escape_html(task.description)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="text-sm text-gray-500 dark:text-gray-400 italic">No description provided</p>`;
    }
    $$payload.out += `<!--]--></div> <div class="p-4 bg-gray-50 dark:bg-gray-700/50"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="space-y-1.5"><div class="flex items-center gap-1.5 text-xs font-medium text-gray-600 dark:text-gray-400">`;
    User($$payload, { class: "w-3.5 h-3.5" });
    $$payload.out += `<!----> <span>Task Owner</span></div> <div class="flex items-center gap-2.5">`;
    if (task.owner?.profilePhoto) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<img${attr("src", task.owner.profilePhoto)}${attr("alt", task.owner.name)} class="w-8 h-8 rounded-full border border-white dark:border-gray-600 shadow-sm" referrerpolicy="no-referrer">`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 border border-white dark:border-gray-600 shadow-sm flex items-center justify-center"><span class="text-xs font-medium text-blue-700 dark:text-blue-400">${escape_html(task.owner?.name?.charAt(0) || "U")}</span></div>`;
    }
    $$payload.out += `<!--]--> <div><div class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(task.owner?.name || "Unassigned")}</div> <div class="text-xs text-gray-500 dark:text-gray-400">Owner</div></div></div></div> <div class="space-y-1.5"><div class="flex items-center gap-1.5 text-xs font-medium text-gray-600 dark:text-gray-400">`;
    Building_2($$payload, { class: "w-3.5 h-3.5" });
    $$payload.out += `<!----> <span>Related Account</span></div> <div class="flex items-center gap-2.5"><div class="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center">`;
    Building_2($$payload, {
      class: "w-4 h-4 text-gray-500 dark:text-gray-400"
    });
    $$payload.out += `<!----></div> <div><div class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(task.account?.name || "No account assigned")}</div> <div class="text-xs text-gray-500 dark:text-gray-400">Account</div></div></div></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="p-4 border-b border-gray-100 dark:border-gray-700"><div class="flex items-center gap-2">`;
    Message_square($$payload, {
      class: "w-4 h-4 text-gray-600 dark:text-gray-400"
    });
    $$payload.out += `<!----> <h2 class="text-base font-semibold text-gray-900 dark:text-white">Comments</h2> `;
    if (task.comments && task.comments.length > 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="text-xs text-gray-500 dark:text-gray-400">(${escape_html(task.comments.length)})</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div> <div class="p-4">`;
    if (form?.message) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div${attr_class(`mb-4 p-3 rounded-lg ${stringify(form.error ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800" : "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 border border-green-200 dark:border-green-800")}`)}><p class="text-xs font-medium">${escape_html(form.message)}</p></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (form?.commentBody && form.error) {
      $$payload.out += "<!--[-->";
      newComment = form.commentBody;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <div class="space-y-3 mb-6">`;
    if (task.comments && task.comments.length > 0) {
      $$payload.out += "<!--[-->";
      const each_array = ensure_array_like(task.comments);
      $$payload.out += `<!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let c = each_array[$$index];
        $$payload.out += `<div class="flex gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-100 dark:border-gray-600">`;
        if (c.author.profilePhoto) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<img${attr("src", c.author.profilePhoto)}${attr("alt", c.author.name)} class="w-8 h-8 rounded-full border border-gray-200 dark:border-gray-600 flex-shrink-0" referrerpolicy="no-referrer">`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `<div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 border border-gray-200 dark:border-gray-600 flex items-center justify-center flex-shrink-0"><span class="text-xs font-medium text-blue-700 dark:text-blue-400">${escape_html(c.author?.name?.charAt(0) || "U")}</span></div>`;
        }
        $$payload.out += `<!--]--> <div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(c.author.name)}</span> <span class="text-xs text-gray-500 dark:text-gray-400">${escape_html(new Date(c.createdAt).toLocaleString())}</span></div> <div class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">${escape_html(c.body)}</div></div></div>`;
      }
      $$payload.out += `<!--]-->`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="text-center py-6">`;
      Message_square($$payload, {
        class: "w-8 h-8 text-gray-300 dark:text-gray-600 mx-auto mb-2"
      });
      $$payload.out += `<!----> <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">No comments yet</p> <p class="text-xs text-gray-400 dark:text-gray-500">Be the first to add a comment</p></div>`;
    }
    $$payload.out += `<!--]--></div> <form method="POST" action="?/addComment" class="space-y-3"><div><label for="commentBody" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1.5">Add a comment</label> <textarea id="commentBody" name="commentBody" class="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent resize-none transition-colors" rows="2" placeholder="Share your thoughts or updates..." required>`;
    const $$body = escape_html(newComment);
    if ($$body) {
      $$payload.out += `${$$body}`;
    }
    $$payload.out += `</textarea></div> <div class="flex justify-end"><button type="submit" class="flex items-center gap-1.5 px-4 py-2 rounded-lg bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium shadow-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"${attr("disabled", !newComment.trim(), true)}>`;
    Send($$payload, { class: "w-3.5 h-3.5" });
    $$payload.out += `<!----> Add Comment</button></div></form></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { data, form });
  pop();
}
export {
  _page as default
};
