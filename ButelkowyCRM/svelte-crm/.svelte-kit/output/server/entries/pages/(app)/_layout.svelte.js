import { A as push, E as spread_props, C as pop, F as store_get, G as ensure_array_like, I as attr_class, J as attr, D as escape_html, K as unsubscribe_stores, M as bind_props, N as copy_payload, O as assign_payload } from "../../../chunks/index2.js";
/* empty css                  */
import "../../../chunks/client.js";
import { p as page } from "../../../chunks/stores.js";
import { i as imgLogo } from "../../../chunks/logo.js";
import { I as Icon } from "../../../chunks/Icon.js";
import { P as Plus } from "../../../chunks/plus.js";
import { T as Target } from "../../../chunks/target.js";
import { U as User_plus } from "../../../chunks/user-plus.js";
import { U as Users } from "../../../chunks/users.js";
import { B as Building } from "../../../chunks/building.js";
import { B as Briefcase } from "../../../chunks/briefcase.js";
import { C as Calendar } from "../../../chunks/calendar.js";
import { S as Square_check_big } from "../../../chunks/square-check-big.js";
import { C as Circle_help } from "../../../chunks/circle-help.js";
import { X } from "../../../chunks/x.js";
import { C as Chevron_down } from "../../../chunks/chevron-down.js";
import { S as Settings } from "../../../chunks/settings.js";
function Layout_dashboard($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      {
        "width": "7",
        "height": "9",
        "x": "3",
        "y": "3",
        "rx": "1"
      }
    ],
    [
      "rect",
      {
        "width": "7",
        "height": "5",
        "x": "14",
        "y": "3",
        "rx": "1"
      }
    ],
    [
      "rect",
      {
        "width": "7",
        "height": "9",
        "x": "14",
        "y": "12",
        "rx": "1"
      }
    ],
    [
      "rect",
      {
        "width": "7",
        "height": "5",
        "x": "3",
        "y": "16",
        "rx": "1"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "layout-dashboard" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function List($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "M3 12h.01" }],
    ["path", { "d": "M3 18h.01" }],
    ["path", { "d": "M3 6h.01" }],
    ["path", { "d": "M8 12h13" }],
    ["path", { "d": "M8 18h13" }],
    ["path", { "d": "M8 6h13" }]
  ];
  Icon($$payload, spread_props([
    { name: "list" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Moon($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      { "d": "M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "moon" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Sidebar($$payload, $$props) {
  push();
  var $$store_subs;
  let {
    drawerHidden = false,
    user = {},
    org_name = "BottleCRM"
  } = $$props;
  let mainSidebarUrl = store_get($$store_subs ??= {}, "$page", page).url.pathname;
  let openDropdowns = {};
  const navigationItems = [
    {
      href: "/app",
      label: "Dashboard",
      icon: Layout_dashboard,
      type: "link"
    },
    {
      key: "leads",
      label: "Leads",
      icon: Target,
      type: "dropdown",
      children: [
        {
          href: "/app/leads/open",
          label: "Open Leads",
          icon: List
        },
        {
          href: "/app/leads/new",
          label: "Create Lead",
          icon: Plus
        }
      ]
    },
    {
      key: "contacts",
      label: "Contacts",
      icon: Users,
      type: "dropdown",
      children: [
        {
          href: "/app/contacts",
          label: "All Contacts",
          icon: List
        },
        {
          href: "/app/contacts/new",
          label: "New Contact",
          icon: User_plus
        }
      ]
    },
    {
      href: "/app/accounts",
      label: "Accounts",
      icon: Building,
      type: "link"
    },
    {
      key: "opportunities",
      label: "Opportunities",
      icon: Target,
      type: "dropdown",
      children: [
        {
          href: "/app/opportunities",
          label: "All Opportunities",
          icon: List
        },
        {
          href: "/app/opportunities/new",
          label: "New Opportunity",
          icon: Plus
        }
      ]
    },
    {
      key: "cases",
      label: "Cases",
      icon: Briefcase,
      type: "dropdown",
      children: [
        {
          href: "/app/cases",
          label: "All Cases",
          icon: List
        },
        {
          href: "/app/cases/new",
          label: "New Case",
          icon: Plus
        }
      ]
    },
    {
      key: "tasks",
      label: "Tasks",
      icon: Square_check_big,
      type: "dropdown",
      children: [
        {
          href: "/app/tasks/list",
          label: "Task List",
          icon: List
        },
        {
          href: "/app/tasks/calendar",
          label: "Calendar",
          icon: Calendar
        }
      ]
    },
    {
      href: "/app/support",
      label: "Support",
      icon: Circle_help,
      type: "link"
    }
  ];
  const each_array = ensure_array_like(navigationItems);
  $$payload.out += `<aside${attr_class(`fixed inset-0 z-30 flex-none h-full w-64 lg:h-auto border-e border-gray-200 dark:border-gray-600 lg:overflow-y-visible lg:block ${drawerHidden ? "hidden" : ""}`)}><div class="flex flex-col h-full bg-white dark:bg-gray-900"><div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"><a href="/app" class="flex items-center gap-3"><img${attr("src", imgLogo)} class="h-8 w-auto" alt="BottleCRM Logo"> <span class="text-xl font-bold text-gray-900 dark:text-white tracking-tight">${escape_html(org_name)}</span></a> <button class="p-2 text-gray-500 rounded-lg lg:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-800 dark:focus:ring-gray-600 transition-colors">`;
  X($$payload, { class: "w-5 h-5" });
  $$payload.out += `<!----></button></div> <div class="flex-1 overflow-y-auto px-4 py-4"><nav class="space-y-2"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
    let item = each_array[$$index_1];
    if (item.type === "link") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<a${attr("href", item.href)}${attr_class(`flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${mainSidebarUrl === item.href ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-400" : "text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800"}`)}>`;
      item.icon($$payload, { class: "w-5 h-5" });
      $$payload.out += `<!----> <span>${escape_html(item.label)}</span></a>`;
    } else if (item.type === "dropdown") {
      $$payload.out += "<!--[1-->";
      $$payload.out += `<div class="space-y-1"><button type="button" class="flex items-center justify-between w-full px-3 py-2.5 text-sm font-medium text-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800"><div class="flex items-center gap-3">`;
      item.icon($$payload, { class: "w-5 h-5" });
      $$payload.out += `<!----> <span>${escape_html(item.label)}</span></div> `;
      Chevron_down($$payload, {
        class: `w-4 h-4 transition-transform duration-200 ${openDropdowns[item.key] ? "rotate-180" : ""}`
      });
      $$payload.out += `<!----></button> `;
      if (openDropdowns[item.key]) {
        $$payload.out += "<!--[-->";
        const each_array_1 = ensure_array_like(item.children);
        $$payload.out += `<div class="ml-8 space-y-1 border-l-2 border-gray-100 dark:border-gray-700 pl-4"><!--[-->`;
        for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
          let child = each_array_1[$$index];
          $$payload.out += `<a${attr("href", child.href)}${attr_class(`flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-all duration-200 ${mainSidebarUrl === child.href ? "bg-blue-50 text-blue-700 font-medium dark:bg-blue-900/20 dark:text-blue-400" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"}`)}>`;
          child.icon($$payload, { class: "w-4 h-4" });
          $$payload.out += `<!----> <span>${escape_html(child.label)}</span></a>`;
        }
        $$payload.out += `<!--]--></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></nav></div> <div class="p-4 border-t border-gray-200 dark:border-gray-700"><div class="flex items-center gap-3 mb-3"><img class="w-10 h-10 rounded-lg object-cover"${attr("src", user.profilePhoto)} alt="User avatar"> <div class="flex-1 min-w-0"><div class="text-sm font-medium text-gray-900 dark:text-white truncate">${escape_html(user.name)}</div> <div class="text-xs text-gray-500 dark:text-gray-400 truncate">${escape_html(user.email)}</div></div></div> <div class="flex items-center justify-between"><button class="p-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors" title="Toggle dark mode">`;
  {
    $$payload.out += "<!--[!-->";
    Moon($$payload, { class: "w-4 h-4" });
  }
  $$payload.out += `<!--]--></button> <button class="p-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors" title="User menu">`;
  Settings($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----></button></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></aside> <div${attr("hidden", drawerHidden, true)} class="fixed inset-0 z-20 bg-gray-900/50 dark:bg-gray-900/60 lg:hidden" role="presentation"></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { drawerHidden });
  pop();
}
function _layout($$payload, $$props) {
  push();
  let { data, children } = $$props;
  let drawerHidden = true;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<button${attr_class("fixed top-4 left-4 z-50 lg:hidden inline-flex items-center p-2 text-gray-500 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-800 dark:focus:ring-gray-600 transition-colors bg-white dark:bg-gray-900 shadow-md border border-gray-200 dark:border-gray-700", void 0, { "hidden": !drawerHidden })}><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button> <div class="flex min-h-screen bg-gray-50 dark:bg-gray-900">`;
    Sidebar($$payload2, {
      user: data.user,
      org_name: data.org_name,
      get drawerHidden() {
        return drawerHidden;
      },
      set drawerHidden($$value) {
        drawerHidden = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> <main class="flex-1 lg:ml-64"><div class="p-6">`;
    children($$payload2);
    $$payload2.out += `<!----></div></main></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _layout as default
};
