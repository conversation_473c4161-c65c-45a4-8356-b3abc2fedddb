import "../../../../../../chunks/index.js";
import { p as prisma } from "../../../../../../chunks/prisma.js";
async function load({ cookies, locals }) {
  locals.user;
  const org = locals.org;
  const leads = await prisma.lead.findMany({
    where: {
      organizationId: org.id,
      status: {
        in: ["NEW", "PENDING", "CONTACTED", "QUALIFIED"]
      },
      isConverted: false
    },
    include: {
      owner: {
        select: {
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      updatedAt: "desc"
    }
  });
  return {
    leads
  };
}
export {
  load
};
