import { P as head, D as escape_html, G as ensure_array_like, I as attr_class, R as stringify, M as bind_props, C as pop, A as push } from "../../../../chunks/index2.js";
import { C as Circle_alert } from "../../../../chunks/circle-alert.js";
import { U as Users } from "../../../../chunks/users.js";
import { T as Target } from "../../../../chunks/target.js";
import { B as Building } from "../../../../chunks/building.js";
import { P as Phone } from "../../../../chunks/phone.js";
import { S as Square_check_big } from "../../../../chunks/square-check-big.js";
import { D as Dollar_sign } from "../../../../chunks/dollar-sign.js";
import { T as Trending_up } from "../../../../chunks/trending-up.js";
import { C as Calendar } from "../../../../chunks/calendar.js";
import { A as Activity } from "../../../../chunks/activity.js";
function _page($$payload, $$props) {
  push();
  let metrics, recentData;
  let data = $$props["data"];
  function formatCurrency(amount) {
    return new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(amount);
  }
  function formatDate(date) {
    return new Date(date).toLocaleDateString("en-US", { month: "short", day: "numeric" });
  }
  function getStatusColor(status) {
    const colors = {
      "NEW": "bg-blue-100 text-blue-800",
      "PENDING": "bg-yellow-100 text-yellow-800",
      "CONTACTED": "bg-green-100 text-green-800",
      "QUALIFIED": "bg-purple-100 text-purple-800",
      "High": "bg-red-100 text-red-800",
      "Normal": "bg-blue-100 text-blue-800",
      "Low": "bg-gray-100 text-gray-800"
    };
    return colors[status] || "bg-gray-100 text-gray-800";
  }
  metrics = data.metrics || {};
  recentData = data.recentData || {};
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Dashboard - BottleCRM</title>`;
  });
  $$payload.out += `<div class="p-6 space-y-6"><div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1> <p class="text-gray-600 dark:text-gray-400">Welcome back! Here's what's happening with your CRM.</p></div></div> `;
  if (data.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center gap-3">`;
    Circle_alert($$payload, {
      class: "text-red-500 dark:text-red-400",
      size: 20
    });
    $$payload.out += `<!----> <span class="text-red-700 dark:text-red-300">${escape_html(data.error)}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4"><div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Leads</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(metrics.totalLeads)}</p></div> <div class="h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">`;
    Users($$payload, {
      class: "text-blue-600 dark:text-blue-400",
      size: 24
    });
    $$payload.out += `<!----></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600 dark:text-gray-400">Opportunities</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(metrics.totalOpportunities)}</p></div> <div class="h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">`;
    Target($$payload, {
      class: "text-green-600 dark:text-green-400",
      size: 24
    });
    $$payload.out += `<!----></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600 dark:text-gray-400">Accounts</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(metrics.totalAccounts)}</p></div> <div class="h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">`;
    Building($$payload, {
      class: "text-purple-600 dark:text-purple-400",
      size: 24
    });
    $$payload.out += `<!----></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600 dark:text-gray-400">Contacts</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(metrics.totalContacts)}</p></div> <div class="h-12 w-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">`;
    Phone($$payload, {
      class: "text-orange-600 dark:text-orange-400",
      size: 24
    });
    $$payload.out += `<!----></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Tasks</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(metrics.pendingTasks)}</p></div> <div class="h-12 w-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">`;
    Square_check_big($$payload, {
      class: "text-yellow-600 dark:text-yellow-400",
      size: 24
    });
    $$payload.out += `<!----></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pipeline Value</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(formatCurrency(metrics.opportunityRevenue))}</p></div> <div class="h-12 w-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center">`;
    Dollar_sign($$payload, {
      class: "text-emerald-600 dark:text-emerald-400",
      size: 24
    });
    $$payload.out += `<!----></div></div></div></div> <div class="grid grid-cols-1 xl:grid-cols-3 gap-6"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Leads</h2> `;
    Trending_up($$payload, {
      class: "text-gray-400 dark:text-gray-500",
      size: 20
    });
    $$payload.out += `<!----></div></div> <div class="p-6">`;
    if (recentData.leads?.length > 0) {
      $$payload.out += "<!--[-->";
      const each_array = ensure_array_like(recentData.leads);
      $$payload.out += `<div class="space-y-4"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let lead = each_array[$$index];
        $$payload.out += `<div class="flex items-center justify-between"><div><p class="font-medium text-gray-900 dark:text-white">${escape_html(lead.firstName)} ${escape_html(lead.lastName)}</p> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(lead.company || "No company")}</p></div> <div class="text-right"><span${attr_class(`inline-block px-2 py-1 text-xs font-medium rounded-full ${stringify(getStatusColor(lead.status))}`)}>${escape_html(lead.status)}</span> <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${escape_html(formatDate(lead.createdAt))}</p></div></div>`;
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="text-gray-500 dark:text-gray-400 text-center py-8">No recent leads</p>`;
    }
    $$payload.out += `<!--]--></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Opportunities</h2> `;
    Target($$payload, {
      class: "text-gray-400 dark:text-gray-500",
      size: 20
    });
    $$payload.out += `<!----></div></div> <div class="p-6">`;
    if (recentData.opportunities?.length > 0) {
      $$payload.out += "<!--[-->";
      const each_array_1 = ensure_array_like(recentData.opportunities);
      $$payload.out += `<div class="space-y-4"><!--[-->`;
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        let opportunity = each_array_1[$$index_1];
        $$payload.out += `<div class="flex items-center justify-between"><div><p class="font-medium text-gray-900 dark:text-white">${escape_html(opportunity.name)}</p> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(opportunity.account?.name || "No account")}</p></div> <div class="text-right">`;
        if (opportunity.amount) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<p class="font-medium text-green-600 dark:text-green-400">${escape_html(formatCurrency(opportunity.amount))}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--> <p class="text-xs text-gray-500 dark:text-gray-400">${escape_html(formatDate(opportunity.createdAt))}</p></div></div>`;
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="text-gray-500 dark:text-gray-400 text-center py-8">No recent opportunities</p>`;
    }
    $$payload.out += `<!--]--></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Upcoming Tasks</h2> `;
    Calendar($$payload, {
      class: "text-gray-400 dark:text-gray-500",
      size: 20
    });
    $$payload.out += `<!----></div></div> <div class="p-6">`;
    if (recentData.tasks?.length > 0) {
      $$payload.out += "<!--[-->";
      const each_array_2 = ensure_array_like(recentData.tasks);
      $$payload.out += `<div class="space-y-4"><!--[-->`;
      for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
        let task = each_array_2[$$index_2];
        $$payload.out += `<div class="flex items-center justify-between"><div><p class="font-medium text-gray-900 dark:text-white">${escape_html(task.subject)}</p> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(task.status)}</p></div> <div class="text-right"><span${attr_class(`inline-block px-2 py-1 text-xs font-medium rounded-full ${stringify(getStatusColor(task.priority))}`)}>${escape_html(task.priority)}</span> `;
        if (task.dueDate) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${escape_html(formatDate(task.dueDate))}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--></div></div>`;
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="text-gray-500 dark:text-gray-400 text-center py-8">No upcoming tasks</p>`;
    }
    $$payload.out += `<!--]--></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activities</h2> `;
    Activity($$payload, {
      class: "text-gray-400 dark:text-gray-500",
      size: 20
    });
    $$payload.out += `<!----></div></div> <div class="p-6">`;
    if (recentData.activities?.length > 0) {
      $$payload.out += "<!--[-->";
      const each_array_3 = ensure_array_like(recentData.activities);
      $$payload.out += `<div class="space-y-4"><!--[-->`;
      for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
        let activity = each_array_3[$$index_3];
        $$payload.out += `<div class="flex items-start gap-3"><div class="h-8 w-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">`;
        Activity($$payload, {
          class: "text-gray-500 dark:text-gray-400",
          size: 16
        });
        $$payload.out += `<!----></div> <div class="flex-1 min-w-0"><p class="text-sm text-gray-900 dark:text-white"><span class="font-medium">${escape_html(activity.user?.name || "Someone")}</span> ${escape_html(activity.description || `performed ${activity.action.toLowerCase()} on ${activity.entityType}`)}</p> <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${escape_html(formatDate(activity.timestamp))}</p></div></div>`;
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="text-gray-500 dark:text-gray-400 text-center py-8">No recent activities</p>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
