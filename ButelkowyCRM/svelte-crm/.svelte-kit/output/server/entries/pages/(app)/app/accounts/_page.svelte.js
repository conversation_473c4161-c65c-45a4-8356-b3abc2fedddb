import { E as spread_props, C as pop, A as push, F as store_get, J as attr, D as escape_html, G as ensure_array_like, I as attr_class, R as stringify, K as unsubscribe_stores, M as bind_props } from "../../../../../chunks/index2.js";
import { p as page } from "../../../../../chunks/stores.js";
import "../../../../../chunks/client.js";
import { S as Search } from "../../../../../chunks/search.js";
import { F as Funnel } from "../../../../../chunks/funnel.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { B as Building_2 } from "../../../../../chunks/building-2.js";
import { T as Trending_up } from "../../../../../chunks/trending-up.js";
import { U as Users } from "../../../../../chunks/users.js";
import { D as Dollar_sign } from "../../../../../chunks/dollar-sign.js";
import { I as Icon } from "../../../../../chunks/Icon.js";
import { C as Chevron_down } from "../../../../../chunks/chevron-down.js";
import { G as Globe } from "../../../../../chunks/globe.js";
import { P as Phone } from "../../../../../chunks/phone.js";
import { M as Map_pin } from "../../../../../chunks/map-pin.js";
import { C as Calendar } from "../../../../../chunks/calendar.js";
import { E as Eye } from "../../../../../chunks/eye.js";
import { S as Square_pen } from "../../../../../chunks/square-pen.js";
function Chevron_up($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [["path", { "d": "m18 15-6-6-6 6" }]];
  Icon($$payload, spread_props([
    { name: "chevron-up" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let data = $$props["data"];
  let tmp = data, accounts = tmp.accounts, pagination = tmp.pagination;
  let sortField = store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("sort") || "name";
  let sortOrder = store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("order") || "asc";
  let isLoading = false;
  store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("status") || "all";
  let searchQuery = store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("q") || "";
  function formatCurrency(amount) {
    if (!amount) return "-";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }
  function formatDate(date) {
    if (!date) return "-";
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric"
    }).format(new Date(date));
  }
  {
    accounts = data.accounts;
    pagination = data.pagination;
    isLoading = false;
  }
  $$payload.out += `<div class="p-6 bg-white dark:bg-gray-900 min-h-screen"><div class="mb-8"><div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"><div><h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Accounts</h1> <p class="text-gray-600 dark:text-gray-400">Manage all your customer accounts and business relationships</p></div> <div class="flex flex-col sm:flex-row gap-3"><div class="relative">`;
  Search($$payload, {
    class: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
  });
  $$payload.out += `<!----> <input type="text" placeholder="Search accounts..." class="pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[250px]"${attr("value", searchQuery)}></div> <div class="relative">`;
  Funnel($$payload, {
    class: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
  });
  $$payload.out += `<!----> <select class="pl-10 pr-8 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none min-w-[120px]"><option value="all">All Status</option><option value="open">Open</option><option value="closed">Closed</option></select></div> <a href="/app/accounts/new" class="inline-flex items-center gap-2 px-4 py-2.5 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors whitespace-nowrap">`;
  Plus($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> New Account</a></div></div></div> <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8"><div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800"><div class="flex items-center gap-3"><div class="p-2 bg-blue-600 rounded-lg">`;
  Building_2($$payload, { class: "w-5 h-5 text-white" });
  $$payload.out += `<!----></div> <div><p class="text-sm text-blue-600 dark:text-blue-400 font-medium">Total Accounts</p> <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">${escape_html(pagination.total)}</p></div></div></div> <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-lg border border-green-200 dark:border-green-800"><div class="flex items-center gap-3"><div class="p-2 bg-green-600 rounded-lg">`;
  Trending_up($$payload, { class: "w-5 h-5 text-white" });
  $$payload.out += `<!----></div> <div><p class="text-sm text-green-600 dark:text-green-400 font-medium">Active</p> <p class="text-2xl font-bold text-green-900 dark:text-green-100">${escape_html(accounts.filter((a) => a.isActive).length)}</p></div></div></div> <div class="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800"><div class="flex items-center gap-3"><div class="p-2 bg-orange-600 rounded-lg">`;
  Users($$payload, { class: "w-5 h-5 text-white" });
  $$payload.out += `<!----></div> <div><p class="text-sm text-orange-600 dark:text-orange-400 font-medium">Total Contacts</p> <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">${escape_html(accounts.reduce((sum, a) => sum + (a.contactCount || 0), 0))}</p></div></div></div> <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800"><div class="flex items-center gap-3"><div class="p-2 bg-purple-600 rounded-lg">`;
  Dollar_sign($$payload, { class: "w-5 h-5 text-white" });
  $$payload.out += `<!----></div> <div><p class="text-sm text-purple-600 dark:text-purple-400 font-medium">Opportunities</p> <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">${escape_html(accounts.reduce((sum, a) => sum + (a.opportunityCount || 0), 0))}</p></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="overflow-x-auto"><table class="w-full"><thead class="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"><tr><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"><div class="flex items-center gap-2">`;
  Building_2($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Account Name `;
  if (sortField === "name") {
    $$payload.out += "<!--[-->";
    if (sortOrder === "asc") {
      $$payload.out += "<!--[-->";
      Chevron_up($$payload, { class: "w-4 h-4" });
    } else {
      $$payload.out += "<!--[!-->";
      Chevron_down($$payload, { class: "w-4 h-4" });
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></th><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden sm:table-cell">Industry</th><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">Type</th><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden lg:table-cell">Contact Info</th><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden xl:table-cell">Revenue</th><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">Relations</th><th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden lg:table-cell">Created</th><th scope="col" class="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th></tr></thead><tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">`;
  if (isLoading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<tr><td colspan="8" class="px-6 py-16 text-center"><div class="flex flex-col items-center gap-4"><div class="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div> <p class="text-gray-500 dark:text-gray-400">Loading accounts...</p></div></td></tr>`;
  } else if (accounts.length === 0) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<tr><td colspan="8" class="px-6 py-16 text-center"><div class="flex flex-col items-center gap-4">`;
    Building_2($$payload, { class: "w-12 h-12 text-gray-400" });
    $$payload.out += `<!----> <div><p class="text-gray-500 dark:text-gray-400 text-lg font-medium">No accounts found</p> <p class="text-gray-400 dark:text-gray-500 text-sm mt-1">Get started by creating your first account</p></div> <a href="/app/accounts/new" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">`;
    Plus($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Create Account</a></div></td></tr>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(accounts);
    $$payload.out += `<!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let account = each_array[$$index];
      $$payload.out += `<tr${attr_class(`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${stringify(account.closedAt ? "opacity-60" : "")}`)}><td class="px-6 py-4"><div class="flex items-center gap-3"><div class="flex-shrink-0"><div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold">${escape_html(account.name?.[0]?.toUpperCase() || "A")}</div></div> <div class="min-w-0 flex-1"><a${attr("href", `/app/accounts/${stringify(account.id)}`)} class="block group"><p class="text-sm font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">${escape_html(account.name)}</p> `;
      if (account.isActive) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 mt-1">Active</span>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<div class="mt-1"><span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">Closed</span> `;
        if (account.closedAt) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">${escape_html(formatDate(account.closedAt))}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--></div>`;
      }
      $$payload.out += `<!--]--></a></div></div></td><td class="px-6 py-4 hidden sm:table-cell"><span class="text-sm text-gray-600 dark:text-gray-300">${escape_html(account.industry || "-")}</span></td><td class="px-6 py-4 hidden md:table-cell"><span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">${escape_html(account.type || "Customer")}</span></td><td class="px-6 py-4 hidden lg:table-cell"><div class="space-y-1">`;
      if (account.website) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-1 text-sm">`;
        Globe($$payload, { class: "w-3 h-3 text-gray-400" });
        $$payload.out += `<!----> <a${attr("href", account.website.startsWith("http") ? account.website : `https://${account.website}`)} target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline truncate max-w-[150px]">${escape_html(account.website.replace(/^https?:\/\//, ""))}</a></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (account.phone) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-300">`;
        Phone($$payload, { class: "w-3 h-3 text-gray-400" });
        $$payload.out += `<!----> <span class="truncate">${escape_html(account.phone)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (account.city || account.state) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-300">`;
        Map_pin($$payload, { class: "w-3 h-3 text-gray-400" });
        $$payload.out += `<!----> <span class="truncate">${escape_html([account.city, account.state].filter(Boolean).join(", "))}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></td><td class="px-6 py-4 hidden xl:table-cell"><div class="text-sm">`;
      if (account.annualRevenue) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="font-medium text-gray-900 dark:text-white">${escape_html(formatCurrency(account.annualRevenue))}</span> <p class="text-xs text-gray-500">Annual Revenue</p>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<span class="text-gray-400">-</span>`;
      }
      $$payload.out += `<!--]--></div></td><td class="px-6 py-4 hidden md:table-cell"><div class="flex items-center gap-4"><div class="flex items-center gap-1">`;
      Users($$payload, { class: "w-4 h-4 text-gray-400" });
      $$payload.out += `<!----> <span class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(account.contactCount || 0)}</span></div> <div class="flex items-center gap-1">`;
      Trending_up($$payload, { class: "w-4 h-4 text-gray-400" });
      $$payload.out += `<!----> <span class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(account.opportunityCount || 0)}</span></div></div></td><td class="px-6 py-4 hidden lg:table-cell"><div class="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-300">`;
      Calendar($$payload, { class: "w-3 h-3 text-gray-400" });
      $$payload.out += `<!----> <span>${escape_html(formatDate(account.createdAt))}</span></div></td><td class="px-6 py-4 text-right"><div class="flex items-center justify-end gap-2"><a${attr("href", `/app/accounts/${stringify(account.id)}`)} class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" title="View Account">`;
      Eye($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></a> <a${attr("href", `/app/opportunities/new?accountId=${stringify(account.id)}`)} class="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" title="Add Opportunity">`;
      Plus($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></a> <a${attr("href", `/app/accounts/${stringify(account.id)}/edit`)} class="p-2 text-gray-400 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" title="Edit Account">`;
      Square_pen($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></a></div></td></tr>`;
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></tbody></table></div></div> `;
  if (pagination.totalPages > 1) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex flex-col sm:flex-row items-center justify-between pt-6 gap-4"><div class="text-sm text-gray-700 dark:text-gray-300">Showing <span class="font-medium">${escape_html((pagination.page - 1) * pagination.limit + 1)}</span> to <span class="font-medium">${escape_html(Math.min(pagination.page * pagination.limit, pagination.total))}</span> of <span class="font-medium">${escape_html(pagination.total)}</span> accounts</div> <div class="flex items-center gap-2"><button class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"${attr("disabled", pagination.page === 1, true)}>First</button> <button class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"${attr("disabled", pagination.page === 1, true)}>Previous</button> <span class="px-4 py-2 text-sm font-medium text-gray-900 dark:text-white bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">${escape_html(pagination.page)} of ${escape_html(pagination.totalPages)}</span> <button class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"${attr("disabled", pagination.page === pagination.totalPages, true)}>Next</button> <button class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"${attr("disabled", pagination.page === pagination.totalPages, true)}>Last</button></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
