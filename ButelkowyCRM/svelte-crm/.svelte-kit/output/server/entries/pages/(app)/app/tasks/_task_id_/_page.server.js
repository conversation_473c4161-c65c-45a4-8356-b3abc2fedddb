import { p as prisma } from "../../../../../../chunks/prisma.js";
import { f as fail } from "../../../../../../chunks/index.js";
async function load({ params, locals }) {
  const { task_id } = params;
  const org = locals.org;
  const user = locals.user;
  const taskToUpdate = await prisma.task.findUnique({
    where: {
      id: params.task_id,
      organizationId: org.id
    }
  });
  if (!taskToUpdate) {
    return fail(404, { message: "Task not found or you do not have permission to view it." });
  }
  const userExistsInOrg = await prisma.userOrganization.findUnique({
    where: {
      userId_organizationId: {
        userId: user.id,
        organizationId: org.id
      }
    }
  });
  if (!userExistsInOrg) {
    return fail(400, { fieldError: ["ownerId", "User is not part of this organization."] });
  }
  const task = await prisma.task.findUniqueOrThrow({
    where: { id: task_id },
    include: {
      owner: {
        select: { id: true, name: true, profilePhoto: true }
      },
      account: {
        select: { id: true, name: true }
      },
      // You can include other relations like contact, lead, opportunity, case if needed
      // contact: { select: { id: true, firstName: true, lastName: true } },
      comments: {
        include: {
          author: {
            select: { id: true, name: true, profilePhoto: true }
          }
        },
        orderBy: {
          createdAt: "asc"
        }
      }
    }
  });
  const users = await prisma.user.findMany({
    select: { id: true, name: true, profilePhoto: true }
    // Added profilePhoto for comment author
  });
  const accounts = await prisma.account.findMany({
    select: { id: true, name: true }
  });
  if (task.dueDate) {
    task.dueDate = new Date(task.dueDate).toISOString().split("T")[0];
  }
  const loggedInUser = locals.user ? {
    id: locals.user.id,
    name: locals.user.name,
    profilePhoto: locals.user.profilePhoto
    // organizationId: locals.user.organizationId // If available and needed directly
  } : null;
  return {
    task,
    users,
    accounts,
    loggedInUser
  };
}
const actions = {
  addComment: async ({ request, params, locals }) => {
    const org = locals.org;
    const formData = await request.formData();
    const commentBody = formData.get("commentBody")?.toString();
    const { task_id } = params;
    const userId = locals.user.id;
    const taskToUpdate = await prisma.task.findUnique({
      where: {
        id: params.task_id,
        organizationId: org.id
      }
    });
    if (!taskToUpdate) {
      return fail(404, { message: "Task not found or you do not have permission to edit it." });
    }
    const userExistsInOrg = await prisma.userOrganization.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId: org.id
        }
      }
    });
    if (!userExistsInOrg) {
      return fail(400, { fieldError: ["ownerId", "User is not part of this organization."] });
    }
    if (!commentBody || commentBody.trim() === "") {
      return fail(400, { error: true, message: "Comment body cannot be empty.", commentBody });
    }
    try {
      const task = await prisma.task.findUnique({
        where: { id: task_id },
        select: { organizationId: true }
      });
      if (!task) {
        return fail(404, { error: true, message: "Task not found." });
      }
      if (!task.organizationId) {
        return fail(500, { error: true, message: "Task is not associated with an organization." });
      }
      await prisma.comment.create({
        data: {
          body: commentBody,
          authorId: userId,
          taskId: task_id,
          organizationId: task.organizationId
          // Use task's organizationId
        }
      });
      return { success: true, message: "Comment added successfully." };
    } catch (error) {
      console.error("Error adding comment:", error);
      return fail(500, { error: true, message: "Failed to add comment." });
    }
  }
};
export {
  actions,
  load
};
