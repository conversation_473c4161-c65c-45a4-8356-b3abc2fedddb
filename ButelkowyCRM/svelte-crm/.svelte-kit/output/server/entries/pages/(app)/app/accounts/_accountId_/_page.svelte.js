import { E as spread_props, C as pop, A as push, D as escape_html, I as attr_class, J as attr, R as stringify, G as ensure_array_like, M as bind_props } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
import { A as Arrow_left } from "../../../../../../chunks/arrow-left.js";
import { L as Lock_open } from "../../../../../../chunks/lock-open.js";
import { S as Square_pen } from "../../../../../../chunks/square-pen.js";
import { L as Lock } from "../../../../../../chunks/lock.js";
import { G as Globe } from "../../../../../../chunks/globe.js";
import { E as External_link } from "../../../../../../chunks/external-link.js";
import { P as Phone } from "../../../../../../chunks/phone.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { M as Map_pin } from "../../../../../../chunks/map-pin.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { U as Users } from "../../../../../../chunks/users.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
import { T as Target } from "../../../../../../chunks/target.js";
import { D as Dollar_sign } from "../../../../../../chunks/dollar-sign.js";
import { S as Square_check_big } from "../../../../../../chunks/square-check-big.js";
import { F as Folder_open } from "../../../../../../chunks/folder-open.js";
function Triangle_alert($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"
      }
    ],
    ["path", { "d": "M12 9v4" }],
    ["path", { "d": "M12 17h.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "triangle-alert" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let form = $$props["form"];
  Array.isArray(data.users) ? data.users : [];
  const {
    account,
    contacts,
    opportunities,
    quotes,
    tasks,
    cases
  } = data;
  let comments = data.comments;
  function formatDate(dateStr) {
    if (!dateStr) return "N/A";
    return new Date(dateStr).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  function formatCurrency(value) {
    if (!value) return "$0";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0
    }).format(value);
  }
  {
    if (form?.success === false) {
      form.message;
    }
  }
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between py-6"><div class="flex items-center space-x-4"><a href="/app/accounts" class="inline-flex items-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">`;
  Arrow_left($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> Back to Accounts</a> <div class="border-l border-gray-300 dark:border-gray-600 pl-4"><h1 class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(account.name)}</h1> <div class="flex items-center mt-1 space-x-2"><span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${account?.isActive ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"}`)}>${escape_html(account.isActive ? "Active" : "Inactive")}</span> `;
  if (account.type) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">${escape_html(account.type)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div class="flex items-center space-x-3">`;
  if (account.closedAt) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<form method="POST" action="?/reopenAccount"><button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">`;
    Lock_open($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> Reopen Account</button></form>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<a${attr("href", `/app/accounts/${stringify(account.id)}/edit`)} class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">`;
    Square_pen($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> Edit</a> <button class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-lg transition-colors">`;
    Lock($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> Close Account</button>`;
  }
  $$payload.out += `<!--]--></div></div></div></div> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-2 space-y-8"><div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Account Information</h2></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-4"><div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.name || "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Industry</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.industry || "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Website</label> `;
  if (account.website) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", account.website.startsWith("http") ? account.website : `https://${account.website}`)} target="_blank" rel="noopener noreferrer" class="mt-1 inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline">`;
    Globe($$payload, { class: "w-4 h-4 mr-1" });
    $$payload.out += `<!----> ${escape_html(account.website)} `;
    External_link($$payload, { class: "w-3 h-3 ml-1" });
    $$payload.out += `<!----></a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="mt-1 text-sm text-gray-900 dark:text-white">N/A</p>`;
  }
  $$payload.out += `<!--]--></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label> `;
  if (account.phone) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `tel:${account.phone}`)} class="mt-1 inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline">`;
    Phone($$payload, { class: "w-4 h-4 mr-1" });
    $$payload.out += `<!----> ${escape_html(account.phone)}</a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="mt-1 text-sm text-gray-900 dark:text-white">N/A</p>`;
  }
  $$payload.out += `<!--]--></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label> `;
  if (account.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `mailto:${account.email}`)} class="mt-1 inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline">`;
    Mail($$payload, { class: "w-4 h-4 mr-1" });
    $$payload.out += `<!----> ${escape_html(account.email)}</a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="mt-1 text-sm text-gray-900 dark:text-white">N/A</p>`;
  }
  $$payload.out += `<!--]--></div></div> <div class="space-y-4"><div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Annual Revenue</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.annualRevenue ? formatCurrency(account.annualRevenue) : "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Employees</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.numberOfEmployees ? account.numberOfEmployees.toLocaleString() : "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Ownership</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.accountOwnership || "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Rating</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.rating || "N/A")}</p></div> <div><label class="text-sm font-medium text-gray-500 dark:text-gray-400">SIC Code</label> <p class="mt-1 text-sm text-gray-900 dark:text-white">${escape_html(account.sicCode || "N/A")}</p></div></div></div> `;
  if (account.street || account.city || account.state || account.country) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</label> <div class="mt-1 flex items-start text-sm text-gray-900 dark:text-white">`;
    Map_pin($$payload, { class: "w-4 h-4 mr-2 mt-0.5 text-gray-400" });
    $$payload.out += `<!----> <address class="not-italic">${escape_html(account.street || "")}<br> ${escape_html(account.city || "")}${escape_html(account.city && account.state ? ", " : "")}${escape_html(account.state || "")} ${escape_html(account.postalCode || "")}<br> ${escape_html(account.country || "")}</address></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (account.description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"><label class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</label> <p class="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line">${escape_html(account.description)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (account.closedAt) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"><div class="rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4"><div class="flex">`;
    Triangle_alert($$payload, {
      class: "w-5 h-5 text-red-500 mr-3 flex-shrink-0"
    });
    $$payload.out += `<!----> <div><p class="font-medium text-red-800 dark:text-red-200">This account was closed on ${escape_html(formatDate(account.closedAt))}.</p> <p class="text-red-700 dark:text-red-300 mt-1">Reason: ${escape_html(account.closureReason || "No reason provided")}</p></div></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-4 text-sm"><div><label class="text-gray-500 dark:text-gray-400">Created</label> <p class="text-gray-900 dark:text-white">${escape_html(formatDate(account.createdAt))}</p></div> <div><label class="text-gray-500 dark:text-gray-400">Last Updated</label> <p class="text-gray-900 dark:text-white">${escape_html(formatDate(account.updatedAt))}</p></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"><div class="border-b border-gray-200 dark:border-gray-700"><nav class="flex space-x-8 px-6" aria-label="Tabs"><button${attr_class(`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${"border-blue-500 text-blue-600 dark:text-blue-400"}`)}>Contacts (${escape_html(contacts.length)})</button> <button${attr_class(`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`)}>Opportunities (${escape_html(opportunities.length)})</button> <button${attr_class(`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`)}>Tasks (${escape_html(tasks.length)})</button> <button${attr_class(`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`)}>Cases (${escape_html(cases.length)})</button> <button${attr_class(`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`)}>Notes (${escape_html(comments.length)})</button></nav></div> <div class="p-6">`;
  {
    $$payload.out += "<!--[-->";
    if (contacts.length === 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="text-center py-12">`;
      Users($$payload, { class: "mx-auto h-12 w-12 text-gray-400" });
      $$payload.out += `<!----> <p class="mt-2 text-gray-500 dark:text-gray-400">No contacts found for this account</p> <a${attr("href", `/app/contacts/new?accountId=${stringify(account.id)}`)} class="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">`;
      Plus($$payload, { class: "w-4 h-4 mr-2" });
      $$payload.out += `<!----> Add Contact</a></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      const each_array = ensure_array_like(contacts);
      $$payload.out += `<div class="overflow-x-auto"><table class="min-w-full"><thead><tr class="border-b border-gray-200 dark:border-gray-700"><th class="pb-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th><th class="pb-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th><th class="pb-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">Email</th><th class="pb-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden lg:table-cell">Phone</th><th class="pb-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Role</th><th class="pb-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th></tr></thead><tbody class="divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let contact = each_array[$$index];
        $$payload.out += `<tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50"><td class="py-4 font-medium text-gray-900 dark:text-white"><a${attr("href", `/app/contacts/${stringify(contact.id)}`)} class="hover:text-blue-600 dark:hover:text-blue-400 hover:underline">${escape_html(contact.firstName)} ${escape_html(contact.lastName)}</a> `;
        if (contact.isPrimary) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">Primary</span>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--></td><td class="py-4 text-gray-900 dark:text-white">${escape_html(contact.title || "N/A")}</td><td class="py-4 text-gray-900 dark:text-white hidden md:table-cell">`;
        if (contact.email) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<a${attr("href", `mailto:${stringify(contact.email)}`)} class="text-blue-600 dark:text-blue-400 hover:underline">${escape_html(contact.email)}</a>`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `N/A`;
        }
        $$payload.out += `<!--]--></td><td class="py-4 text-gray-900 dark:text-white hidden lg:table-cell">`;
        if (contact.phone) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<a${attr("href", `tel:${stringify(contact.phone)}`)} class="text-blue-600 dark:text-blue-400 hover:underline">${escape_html(contact.phone)}</a>`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `N/A`;
        }
        $$payload.out += `<!--]--></td><td class="py-4 text-gray-900 dark:text-white">${escape_html(contact.role || "N/A")}</td><td class="py-4 text-right"><a${attr("href", `/app/contacts/${stringify(contact.id)}`)} class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">View</a></td></tr>`;
      }
      $$payload.out += `<!--]--></tbody></table></div>`;
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div class="space-y-6"><div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Overview</h2></div> <div class="p-6 space-y-6"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-500 dark:text-gray-400">Contacts</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(contacts.length)}</p></div> `;
  Users($$payload, { class: "w-8 h-8 text-blue-500" });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-500 dark:text-gray-400">Opportunities</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(opportunities.length)}</p></div> `;
  Target($$payload, { class: "w-8 h-8 text-green-500" });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pipeline Value</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(formatCurrency(opportunities.reduce((sum, opp) => sum + (opp.amount || 0), 0)))}</p></div> `;
  Dollar_sign($$payload, { class: "w-8 h-8 text-yellow-500" });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-500 dark:text-gray-400">Open Cases</p> <p class="text-2xl font-bold text-gray-900 dark:text-white">${escape_html(cases.filter((c) => c.status !== "CLOSED").length)}</p></div> `;
  Triangle_alert($$payload, { class: "w-8 h-8 text-red-500" });
  $$payload.out += `<!----></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h2></div> <div class="p-6 space-y-3"><a${attr("href", `/app/contacts/new?accountId=${stringify(account.id)}`)} class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">`;
  Users($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Add Contact</a> <a${attr("href", `/app/opportunities/new?accountId=${stringify(account.id)}`)} class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">`;
  Target($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Add Opportunity</a> <a${attr("href", `/app/tasks/new?accountId=${stringify(account.id)}`)} class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors">`;
  Square_check_big($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Add Task</a> <a${attr("href", `/app/cases/new?accountId=${stringify(account.id)}`)} class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors">`;
  Folder_open($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Open Case</a></div></div></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data, form });
  pop();
}
export {
  _page as default
};
