import { G as ensure_array_like, J as attr, D as escape_html, M as bind_props, C as pop, A as push } from "../../../../../../../chunks/index2.js";
import { A as Arrow_left } from "../../../../../../../chunks/arrow-left.js";
import { B as Building } from "../../../../../../../chunks/building.js";
import { D as Dollar_sign } from "../../../../../../../chunks/dollar-sign.js";
import { T as Trending_up } from "../../../../../../../chunks/trending-up.js";
import { T as Target } from "../../../../../../../chunks/target.js";
import { C as Calendar } from "../../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../../chunks/user.js";
import { F as File_text } from "../../../../../../../chunks/file-text.js";
import { S as Save } from "../../../../../../../chunks/save.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let opportunity = { ...data.opportunity };
  let isSubmitting = false;
  let closeDateStr = opportunity.closeDate ? new Date(opportunity.closeDate).toISOString().slice(0, 10) : "";
  const leadSources = [
    { value: "WEB", label: "Web" },
    {
      value: "PHONE_INQUIRY",
      label: "Phone Inquiry"
    },
    {
      value: "PARTNER_REFERRAL",
      label: "Partner Referral"
    },
    { value: "COLD_CALL", label: "Cold Call" },
    { value: "TRADE_SHOW", label: "Trade Show" },
    {
      value: "EMPLOYEE_REFERRAL",
      label: "Employee Referral"
    },
    {
      value: "ADVERTISEMENT",
      label: "Advertisement"
    },
    { value: "OTHER", label: "Other" }
  ];
  const forecastCategories = [
    { value: "Pipeline", label: "Pipeline" },
    { value: "Best Case", label: "Best Case" },
    { value: "Commit", label: "Commit" },
    { value: "Closed", label: "Closed" }
  ];
  opportunity.closeDate = closeDateStr;
  const each_array = ensure_array_like(leadSources);
  const each_array_1 = ensure_array_like(forecastCategories);
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="mb-8"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><a${attr("href", `/app/opportunities/${opportunity.id}`)} class="inline-flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">`;
  Arrow_left($$payload, { class: "w-5 h-5" });
  $$payload.out += `<!----></a> <div><h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Opportunity</h1> <p class="text-gray-600 dark:text-gray-400 mt-1">Update opportunity details and track progress</p></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"><form class="p-6 sm:p-8"><div class="mb-8"><h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">`;
  Building($$payload, {
    class: "w-5 h-5 mr-2 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Basic Information</h2> <div class="grid grid-cols-1 lg:grid-cols-2 gap-6"><div class="lg:col-span-2"><label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Opportunity Name *</label> <input id="name" name="name" type="text"${attr("value", opportunity.name)} required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors" placeholder="Enter opportunity name"></div> <div><label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Dollar_sign($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Amount</label> <input id="amount" name="amount" type="number" step="0.01" min="0"${attr("value", opportunity.amount)} class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors" placeholder="0.00"></div> <div><label for="expectedRevenue" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Trending_up($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Expected Revenue</label> <input id="expectedRevenue" name="expectedRevenue" type="number" step="0.01" min="0"${attr("value", opportunity.expectedRevenue)} class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors" placeholder="0.00"></div> <div><label for="stage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Target($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Stage *</label> <select id="stage" name="stage" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors"><option value="PROSPECTING">Prospecting</option><option value="QUALIFICATION">Qualification</option><option value="PROPOSAL">Proposal</option><option value="NEGOTIATION">Negotiation</option><option value="CLOSED_WON">Closed Won</option><option value="CLOSED_LOST">Closed Lost</option></select></div> <div><label for="probability" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Probability (%)</label> <input id="probability" name="probability" type="number" min="0" max="100"${attr("value", opportunity.probability)} class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors" placeholder="0"></div> <div><label for="closeDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Calendar($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Close Date</label> <input id="closeDate" name="closeDate" type="date"${attr("value", closeDateStr)} class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors"></div> <div><label for="leadSource" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  User($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Lead Source</label> <select id="leadSource" name="leadSource" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors"><option value="">Select source...</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let source = each_array[$$index];
    $$payload.out += `<option${attr("value", source.value)}>${escape_html(source.label)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="forecastCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Forecast Category</label> <select id="forecastCategory" name="forecastCategory" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors"><option value="">Select category...</option><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let category = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", category.value)}>${escape_html(category.label)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label> <input id="type" name="type" type="text"${attr("value", opportunity.type)} class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors" placeholder="e.g., New Business, Existing Business"></div> <div class="lg:col-span-2"><label for="nextStep" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Next Step</label> <input id="nextStep" name="nextStep" type="text"${attr("value", opportunity.nextStep)} class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors" placeholder="What's the next action to take?"></div> <div class="lg:col-span-2"><label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  File_text($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Description</label> <textarea id="description" name="description" rows="4" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors resize-none" placeholder="Provide additional details about this opportunity...">`;
  const $$body = escape_html(opportunity.description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700"><a${attr("href", `/app/opportunities/${opportunity.id}`)} class="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-medium">Cancel</a> <button type="submit"${attr("disabled", isSubmitting, true)} class="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors font-medium flex items-center">`;
  {
    $$payload.out += "<!--[!-->";
    Save($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> Save Changes`;
  }
  $$payload.out += `<!--]--></button></div></form></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
