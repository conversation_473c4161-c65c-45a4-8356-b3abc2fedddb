import { J as attr, D as escape_html, I as attr_class, R as stringify, $ as attr_style, M as bind_props, C as pop, A as push } from "../../../../../../chunks/index2.js";
import { A as Arrow_left } from "../../../../../../chunks/arrow-left.js";
import { S as Square_pen } from "../../../../../../chunks/square-pen.js";
import { T as Trash_2 } from "../../../../../../chunks/trash-2.js";
import { P as Percent } from "../../../../../../chunks/percent.js";
import { D as Dollar_sign } from "../../../../../../chunks/dollar-sign.js";
import { T as Target } from "../../../../../../chunks/target.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { A as Activity } from "../../../../../../chunks/activity.js";
import { F as File_text } from "../../../../../../chunks/file-text.js";
import { T as Trending_up } from "../../../../../../chunks/trending-up.js";
import { B as Building } from "../../../../../../chunks/building.js";
import { U as User } from "../../../../../../chunks/user.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let opportunity = data.opportunity;
  let account = data.account;
  let owner = data.owner;
  const stageColors = {
    "PROSPECTING": "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
    "QUALIFICATION": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    "PROPOSAL": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
    "NEGOTIATION": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
    "CLOSED_WON": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    "CLOSED_LOST": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
  };
  const getStageColor = (stage) => stageColors[stage] || "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
  const formatCurrency = (amount) => {
    return amount ? `$${amount.toLocaleString()}` : "N/A";
  };
  const formatDate = (date) => {
    return date ? new Date(date).toLocaleDateString() : "N/A";
  };
  const formatDateTime = (date) => {
    return date ? new Date(date).toLocaleString() : "N/A";
  };
  const getStageProgress = (stage) => {
    const stages = [
      "PROSPECTING",
      "QUALIFICATION",
      "PROPOSAL",
      "NEGOTIATION",
      "CLOSED_WON"
    ];
    const index = stages.indexOf(stage);
    return index >= 0 ? (index + 1) / stages.length * 100 : 0;
  };
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"><div class="mb-8"><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"><div class="flex items-center gap-3"><a${attr("href", account ? `/app/accounts/${account.id}` : "/app/accounts")} class="inline-flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">`;
  Arrow_left($$payload, { size: 16 });
  $$payload.out += `<!----> Back to Account</a></div> <div class="flex gap-3"><a${attr("href", `/app/opportunities/${opportunity.id}/edit`)} class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">`;
  Square_pen($$payload, { size: 16 });
  $$payload.out += `<!----> Edit</a> <a${attr("href", `/app/opportunities/${opportunity.id}/delete`)} class="inline-flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">`;
  Trash_2($$payload, { size: 16 });
  $$payload.out += `<!----> Delete</a></div></div> <div class="mt-4"><h1 class="text-3xl font-bold text-gray-900 dark:text-white">${escape_html(opportunity.name)}</h1> <div class="mt-2 flex items-center gap-3"><span${attr_class(`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${stringify(getStageColor(opportunity.stage))}`, "svelte-90f5gh")}>${escape_html(opportunity.stage.replace("_", " "))}</span> `;
  if (opportunity.probability) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">`;
    Percent($$payload, { size: 14 });
    $$payload.out += `<!----> ${escape_html(opportunity.probability)}% probability</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> `;
  if (opportunity.stage !== "CLOSED_LOST") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-4"><div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"><span>Progress</span> <span>${escape_html(Math.round(getStageProgress(opportunity.stage)))}%</span></div> <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full transition-all duration-300"${attr_style(`width: ${stringify(getStageProgress(opportunity.stage))}%`)}></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="grid grid-cols-1 lg:grid-cols-3 gap-6"><div class="lg:col-span-2 space-y-6"><div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
  Dollar_sign($$payload, { size: 20, class: "text-green-600" });
  $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Financial Details</h2></div> <div class="grid grid-cols-1 sm:grid-cols-2 gap-4"><div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"><div class="text-sm text-gray-500 dark:text-gray-400">Amount</div> <div class="text-xl font-bold text-gray-900 dark:text-white">${escape_html(formatCurrency(opportunity.amount))}</div></div> <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"><div class="text-sm text-gray-500 dark:text-gray-400">Expected Revenue</div> <div class="text-xl font-bold text-gray-900 dark:text-white">${escape_html(formatCurrency(opportunity.expectedRevenue))}</div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
  Target($$payload, { size: 20, class: "text-blue-600" });
  $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Opportunity Information</h2></div> <div class="grid grid-cols-1 sm:grid-cols-2 gap-6"><div><div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Type</div> <div class="text-gray-900 dark:text-white">${escape_html(opportunity.type || "Not specified")}</div></div> <div><div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Lead Source</div> <div class="text-gray-900 dark:text-white">${escape_html(opportunity.leadSource || "Not specified")}</div></div> <div><div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Forecast Category</div> <div class="text-gray-900 dark:text-white">${escape_html(opportunity.forecastCategory || "Not specified")}</div></div> <div><div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Close Date</div> <div class="flex items-center gap-1 text-gray-900 dark:text-white">`;
  Calendar($$payload, { size: 14 });
  $$payload.out += `<!----> ${escape_html(formatDate(opportunity.closeDate))}</div></div></div></div> `;
  if (opportunity.nextStep) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
    Activity($$payload, { size: 20, class: "text-orange-600" });
    $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Next Steps</h2></div> <div class="text-gray-700 dark:text-gray-300">${escape_html(opportunity.nextStep)}</div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (opportunity.description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
    File_text($$payload, { size: 20, class: "text-gray-600" });
    $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Description</h2></div> <div class="text-gray-700 dark:text-gray-300 whitespace-pre-line">${escape_html(opportunity.description)}</div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="space-y-6"><div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
  Trending_up($$payload, { size: 20, class: "text-purple-600" });
  $$payload.out += `<!----> <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Key Metrics</h3></div> <div class="space-y-4"><div class="flex items-center justify-between"><span class="text-sm text-gray-500 dark:text-gray-400">Probability</span> <span class="font-semibold text-gray-900 dark:text-white">${escape_html(opportunity.probability ? `${opportunity.probability}%` : "N/A")}</span></div> <div class="flex items-center justify-between"><span class="text-sm text-gray-500 dark:text-gray-400">Days to Close</span> <span class="font-semibold text-gray-900 dark:text-white">${escape_html(opportunity.closeDate ? Math.ceil((new Date(opportunity.closeDate) - /* @__PURE__ */ new Date()) / (1e3 * 60 * 60 * 24)) : "N/A")}</span></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
  Building($$payload, { size: 20, class: "text-blue-600" });
  $$payload.out += `<!----> <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Related Records</h3></div> <div class="space-y-4"><div><div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Account</div> `;
  if (account) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `/app/accounts/${account.id}`)} class="text-blue-600 dark:text-blue-400 hover:underline font-medium">${escape_html(account.name)}</a>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="text-gray-500 dark:text-gray-400">No account</span>`;
  }
  $$payload.out += `<!--]--></div> <div><div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Owner</div> <div class="flex items-center gap-2">`;
  User($$payload, { size: 16, class: "text-gray-400" });
  $$payload.out += `<!----> <span class="text-gray-900 dark:text-white">${escape_html(owner?.name ?? "Unassigned")}</span></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-4">`;
  Clock($$payload, { size: 20, class: "text-gray-600" });
  $$payload.out += `<!----> <h3 class="text-lg font-semibold text-gray-900 dark:text-white">System Information</h3></div> <div class="space-y-3"><div><div class="text-sm text-gray-500 dark:text-gray-400">Created</div> <div class="text-sm text-gray-900 dark:text-white">${escape_html(formatDateTime(opportunity.createdAt))}</div></div> <div><div class="text-sm text-gray-500 dark:text-gray-400">Last Updated</div> <div class="text-sm text-gray-900 dark:text-white">${escape_html(formatDateTime(opportunity.updatedAt))}</div></div></div></div></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
