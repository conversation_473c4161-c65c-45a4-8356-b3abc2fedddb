import { A as push, E as spread_props, C as pop, G as ensure_array_like, J as attr, D as escape_html, R as stringify, I as attr_class, M as bind_props } from "../../../../../../chunks/index2.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
import { C as Circle_check } from "../../../../../../chunks/circle-check.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../chunks/user.js";
import { B as Building_2 } from "../../../../../../chunks/building-2.js";
import { P as Pen_line } from "../../../../../../chunks/pen-line.js";
import { T as Trash_2 } from "../../../../../../chunks/trash-2.js";
import { C as Circle_alert } from "../../../../../../chunks/circle-alert.js";
import { C as Circle_x } from "../../../../../../chunks/circle-x.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
function Circle_play($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "10" }
    ],
    [
      "polygon",
      { "points": "10 8 16 12 10 16 10 8" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "circle-play" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Pause($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      {
        "x": "14",
        "y": "4",
        "width": "4",
        "height": "16",
        "rx": "1"
      }
    ],
    [
      "rect",
      {
        "x": "6",
        "y": "4",
        "width": "4",
        "height": "16",
        "rx": "1"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "pause" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric"
    };
    return new Date(dateString).toLocaleDateString(void 0, options);
  }
  function getStatusIcon(status) {
    switch (status) {
      case "Completed":
        return Circle_check;
      case "In Progress":
        return Circle_play;
      case "Not Started":
        return Pause;
      case "Waiting on someone else":
        return Clock;
      case "Deferred":
        return Circle_x;
      default:
        return Circle_alert;
    }
  }
  function getPriorityIcon(priority) {
    switch (priority) {
      case "High":
        return Circle_alert;
      case "Normal":
        return Clock;
      case "Low":
        return Clock;
      default:
        return Clock;
    }
  }
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="mb-8"><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"><div><h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Tasks</h1> <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Manage and track your team's tasks</p></div> <a href="/app/tasks/new" class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-medium px-4 py-2 rounded-lg shadow-sm transition-colors duration-200">`;
  Plus($$payload, { size: 20 });
  $$payload.out += `<!----> New Task</a></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">`;
  if (data.tasks.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-16"><div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">`;
    Circle_check($$payload, {
      size: 32,
      class: "text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----></div> <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No tasks yet</h3> <p class="text-gray-500 dark:text-gray-400 mb-6">Get started by creating your first task</p> <a href="/app/tasks/new" class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-medium px-4 py-2 rounded-lg shadow-sm transition-colors duration-200">`;
    Plus($$payload, { size: 20 });
    $$payload.out += `<!----> Create Task</a></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(data.tasks);
    const each_array_1 = ensure_array_like(data.tasks);
    $$payload.out += `<div class="hidden md:block overflow-x-auto"><table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"><thead class="bg-gray-50 dark:bg-gray-700"><tr><th class="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Task</th><th class="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th><th class="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Priority</th><th class="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Due Date</th><th class="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Owner</th><th class="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Account</th><th class="px-6 py-4 text-right text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th></tr></thead><tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let task = each_array[$$index];
      $$payload.out += `<tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"><td class="px-6 py-4"><div class="flex items-start"><div class="min-w-0 flex-1"><a${attr("href", `/app/tasks/${stringify(task.id)}`)} class="text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">${escape_html(task.subject)}</a> `;
      if (task.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs">${escape_html(task.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div></td><td class="px-6 py-4"><div class="flex items-center gap-2"><!---->`;
      getStatusIcon(task.status)?.($$payload, {
        size: 16,
        class: task.status === "Completed" ? "text-green-500 dark:text-green-400" : task.status === "In Progress" ? "text-yellow-500 dark:text-yellow-400" : task.status === "Not Started" ? "text-gray-400 dark:text-gray-500" : task.status === "Waiting on someone else" ? "text-purple-500 dark:text-purple-400" : task.status === "Deferred" ? "text-pink-500 dark:text-pink-400" : "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stringify(task.status === "Completed" ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300" : "")} ${stringify(task.status === "In Progress" ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300" : "")} ${stringify(task.status === "Not Started" ? "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300" : "")} ${stringify(task.status === "Waiting on someone else" ? "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300" : "")} ${stringify(task.status === "Deferred" ? "bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300" : "")} `)}>${escape_html(task.status || "N/A")}</span></div></td><td class="px-6 py-4"><div class="flex items-center gap-2"><!---->`;
      getPriorityIcon(task.priority)?.($$payload, {
        size: 16,
        class: task.priority === "High" ? "text-red-500 dark:text-red-400" : task.priority === "Normal" ? "text-blue-500 dark:text-blue-400" : task.priority === "Low" ? "text-gray-400 dark:text-gray-500" : "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stringify(task.priority === "High" ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300" : "")} ${stringify(task.priority === "Normal" ? "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300" : "")} ${stringify(task.priority === "Low" ? "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300" : "")} `)}>${escape_html(task.priority || "Normal")}</span></div></td><td class="px-6 py-4"><div class="flex items-center gap-2 text-sm text-gray-900 dark:text-gray-100">`;
      Calendar($$payload, {
        size: 16,
        class: "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> ${escape_html(formatDate(task.dueDate))}</div></td><td class="px-6 py-4"><div class="flex items-center gap-2">`;
      User($$payload, {
        size: 16,
        class: "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span class="text-sm text-gray-900 dark:text-gray-100">${escape_html(task.owner?.name || "Unassigned")}</span></div></td><td class="px-6 py-4"><div class="flex items-center gap-2">`;
      Building_2($$payload, {
        size: 16,
        class: "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span class="text-sm text-gray-900 dark:text-gray-100">${escape_html(task.account?.name || "N/A")}</span></div></td><td class="px-6 py-4 text-right"><div class="flex items-center justify-end gap-2"><a${attr("href", `/app/tasks/${stringify(task.id)}/edit`)} class="inline-flex items-center gap-1 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors p-1" aria-label="Edit Task">`;
      Pen_line($$payload, { size: 16 });
      $$payload.out += `<!----></a> <button class="inline-flex items-center gap-1 text-gray-400 dark:text-gray-500 p-1 cursor-not-allowed" disabled title="Delete (functionality to be implemented)">`;
      Trash_2($$payload, { size: 16 });
      $$payload.out += `<!----></button></div></td></tr>`;
    }
    $$payload.out += `<!--]--></tbody></table></div> <div class="md:hidden"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let task = each_array_1[$$index_1];
      $$payload.out += `<div class="border-b border-gray-200 dark:border-gray-700 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"><div class="flex items-start justify-between mb-3"><div class="min-w-0 flex-1"><a${attr("href", `/app/tasks/${stringify(task.id)}`)} class="text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors block">${escape_html(task.subject)}</a> `;
      if (task.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">${escape_html(task.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="flex items-center gap-2 ml-4"><a${attr("href", `/app/tasks/${stringify(task.id)}/edit`)} class="text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 transition-colors p-1">`;
      Pen_line($$payload, { size: 16 });
      $$payload.out += `<!----></a> <button class="text-gray-300 dark:text-gray-600 p-1 cursor-not-allowed" disabled>`;
      Trash_2($$payload, { size: 16 });
      $$payload.out += `<!----></button></div></div> <div class="flex flex-wrap gap-2 mb-3"><div class="flex items-center gap-1"><!---->`;
      getStatusIcon(task.status)?.($$payload, {
        size: 14,
        class: task.status === "Completed" ? "text-green-500 dark:text-green-400" : task.status === "In Progress" ? "text-yellow-500 dark:text-yellow-400" : task.status === "Not Started" ? "text-gray-400 dark:text-gray-500" : task.status === "Waiting on someone else" ? "text-purple-500 dark:text-purple-400" : task.status === "Deferred" ? "text-pink-500 dark:text-pink-400" : "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span${attr_class(`text-xs px-2 py-1 rounded-full font-medium ${stringify(task.status === "Completed" ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300" : "")} ${stringify(task.status === "In Progress" ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300" : "")} ${stringify(task.status === "Not Started" ? "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300" : "")} ${stringify(task.status === "Waiting on someone else" ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300" : "")} ${stringify(task.status === "Deferred" ? "bg-pink-100 dark:bg-pink-900/30 text-pink-700 dark:text-pink-300" : "")} `)}>${escape_html(task.status || "N/A")}</span></div> <div class="flex items-center gap-1"><!---->`;
      getPriorityIcon(task.priority)?.($$payload, {
        size: 14,
        class: task.priority === "High" ? "text-red-500 dark:text-red-400" : task.priority === "Normal" ? "text-blue-500 dark:text-blue-400" : task.priority === "Low" ? "text-gray-400 dark:text-gray-500" : "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span${attr_class(`text-xs px-2 py-1 rounded-full font-medium ${stringify(task.priority === "High" ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300" : "")} ${stringify(task.priority === "Normal" ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300" : "")} ${stringify(task.priority === "Low" ? "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300" : "")} `)}>${escape_html(task.priority || "Normal")}</span></div></div> <div class="grid grid-cols-1 gap-2 text-sm"><div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">`;
      Calendar($$payload, {
        size: 14,
        class: "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span>Due: ${escape_html(formatDate(task.dueDate))}</span></div> <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">`;
      User($$payload, {
        size: 14,
        class: "text-gray-400 dark:text-gray-500"
      });
      $$payload.out += `<!----> <span>${escape_html(task.owner?.name || "Unassigned")}</span></div> `;
      if (task.account?.name) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">`;
        Building_2($$payload, {
          size: 14,
          class: "text-gray-400 dark:text-gray-500"
        });
        $$payload.out += `<!----> <span>${escape_html(task.account.name)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
