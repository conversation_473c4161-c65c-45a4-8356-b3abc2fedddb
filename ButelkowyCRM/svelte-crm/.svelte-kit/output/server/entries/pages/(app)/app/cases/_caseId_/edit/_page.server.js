import { p as prisma } from "../../../../../../../chunks/prisma.js";
import { e as error, f as fail, r as redirect } from "../../../../../../../chunks/index.js";
async function load({ params, locals }) {
  const org = locals.org;
  const caseId = params.caseId;
  const caseItem = await prisma.case.findUnique({
    where: { id: caseId, organizationId: org.id },
    include: {
      owner: { select: { id: true, name: true } },
      account: { select: { id: true, name: true } }
    }
  });
  if (!caseItem) throw error(404, "Case not found");
  const [users, accounts] = await Promise.all([
    prisma.user.findMany({ select: { id: true, name: true } }),
    prisma.account.findMany({ select: { id: true, name: true } })
  ]);
  return { caseItem, users, accounts };
}
const actions = {
  update: async ({ request, params, locals }) => {
    const org = locals.org;
    const form = await request.formData();
    const subject = form.get("title")?.toString().trim();
    const description = form.get("description")?.toString().trim();
    const accountId = form.get("accountId")?.toString();
    const dueDateRaw = form.get("dueDate");
    const dueDate = dueDateRaw ? new Date(dueDateRaw.toString()) : null;
    const priority = form.get("priority")?.toString() || "Medium";
    const ownerId = form.get("assignedId")?.toString();
    if (!subject || !accountId || !ownerId) {
      return fail(400, { error: "Missing required fields." });
    }
    const caseExists = await prisma.case.findFirst({
      where: { id: params.caseId, organizationId: org.id }
    });
    if (!caseExists) {
      return fail(404, { error: "Case not found or does not belong to this organization." });
    }
    try {
      await prisma.case.update({
        where: { id: params.caseId },
        data: { subject, description, accountId, dueDate, priority, ownerId }
      });
      return { success: true };
    } catch (error2) {
      return fail(500, { error: "Failed to update case." });
    }
  },
  close: async ({ params, locals }) => {
    const org = locals.org;
    const caseExists = await prisma.case.findFirst({
      where: { id: params.caseId, organizationId: org.id }
    });
    if (!caseExists) {
      return fail(404, { error: "Case not found or does not belong to this organization." });
    }
    await prisma.case.update({
      where: { id: params.caseId },
      data: { status: "CLOSED", closedAt: /* @__PURE__ */ new Date() }
    });
    throw redirect(303, `/app/cases/${params.caseId}`);
  },
  reopen: async ({ params, locals }) => {
    const org = locals.org;
    const caseExists = await prisma.case.findFirst({
      where: { id: params.caseId, organizationId: org.id }
    });
    if (!caseExists) {
      return fail(404, { error: "Case not found or does not belong to this organization." });
    }
    await prisma.case.update({
      where: { id: params.caseId },
      data: { status: "OPEN", closedAt: null }
    });
    throw redirect(303, `/app/cases/${params.caseId}`);
  }
};
export {
  actions,
  load
};
