import { G as ensure_array_like, D as escape_html, J as attr, I as attr_class, R as stringify, M as bind_props, C as pop, A as push } from "../../../../../chunks/index2.js";
/* empty css                        */
import { L as Log_out } from "../../../../../chunks/log-out.js";
import { B as Building_2 } from "../../../../../chunks/building-2.js";
import { G as Globe } from "../../../../../chunks/globe.js";
import { U as Users } from "../../../../../chunks/users.js";
import { S as Square_pen } from "../../../../../chunks/square-pen.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { U as User } from "../../../../../chunks/user.js";
import { H as Headphones } from "../../../../../chunks/headphones.js";
import { B as Briefcase } from "../../../../../chunks/briefcase.js";
import { S as Shield } from "../../../../../chunks/shield.js";
import { C as Check } from "../../../../../chunks/check.js";
import { X } from "../../../../../chunks/x.js";
import { T as Trash_2 } from "../../../../../chunks/trash-2.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let org = data.organization;
  ({
    name: org.name,
    domain: org.domain || "",
    description: org.description || ""
  });
  let loggedInUserId = data.user?.id;
  let users = Array.isArray(data.users) ? data.users.map((u) => ({
    id: u.user.id,
    name: u.user.name || u.user.email,
    email: u.user.email,
    role: u.role,
    joined: u.joinedAt ? typeof u.joinedAt === "string" ? u.joinedAt.slice(0, 10) : new Date(u.joinedAt).toISOString().slice(0, 10) : "",
    avatar: u.user.profilePhoto || "",
    isSelf: loggedInUserId === u.user.id,
    editingRole: false
  })) : [];
  const roleIcons = {
    ADMIN: Shield,
    USER: User,
    SALES_REP: Briefcase,
    SUPPORT_REP: Headphones,
    READ_ONLY: User
  };
  const roleColors = {
    ADMIN: "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800",
    USER: "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800",
    SALES_REP: "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800",
    SUPPORT_REP: "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800",
    READ_ONLY: "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600"
  };
  const each_array = ensure_array_like(users);
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8"><div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8"><div class="mb-8 flex items-center justify-between"><div><h1 class="text-3xl font-bold text-gray-900 dark:text-white">Organization Settings</h1> <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your organization and team members</p></div> <a href="/logout" class="inline-flex items-center gap-2 rounded-lg bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm ring-1 ring-gray-200 dark:ring-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-red-600 dark:hover:text-red-400 transition-colors">`;
  Log_out($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> Logout</a></div> <div class="mb-8 overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-sm ring-1 ring-gray-200 dark:ring-gray-700"><div class="px-6 py-5"><div class="flex items-start justify-between"><div class="flex items-center gap-3"><div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">`;
  Building_2($$payload, {
    class: "h-6 w-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h2 class="text-xl font-semibold text-gray-900 dark:text-white">${escape_html(org.name)}</h2> <div class="mt-1 flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">`;
  if (org.domain) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="flex items-center gap-1">`;
    Globe($$payload, { class: "h-4 w-4" });
    $$payload.out += `<!----> ${escape_html(org.domain)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <span class="flex items-center gap-1">`;
  Users($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> ${escape_html(users.length)} member${escape_html(users.length !== 1 ? "s" : "")}</span></div></div></div> `;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button type="button" class="rounded-lg p-2 text-gray-400 dark:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-600 dark:hover:text-gray-300" aria-label="Edit organization">`;
    Square_pen($$payload, { class: "h-5 w-5" });
    $$payload.out += `<!----></button>`;
  }
  $$payload.out += `<!--]--></div> `;
  if (org.description && true) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-4 text-gray-600 dark:text-gray-300">${escape_html(org.description)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-sm ring-1 ring-gray-200 dark:ring-gray-700"><div class="px-6 py-5"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">`;
  Users($$payload, {
    class: "h-5 w-5 text-green-600 dark:text-green-400"
  });
  $$payload.out += `<!----></div> <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Team Members</h3></div></div> <div class="mt-6 rounded-lg bg-gray-50 dark:bg-gray-700/50 p-4"><h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Add New Member</h4> <form method="POST" action="?/add_user" class="flex flex-col gap-3 sm:flex-row sm:items-end"><div class="flex-1"><label for="add-user-email" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label> <input id="add-user-email" name="email" type="email" required class="block w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm shadow-sm focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 placeholder-gray-400 dark:placeholder-gray-500" placeholder="<EMAIL>"></div> <div class="sm:w-32"><label for="add-user-role" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Role</label> <select id="add-user-role" name="role" class="block w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm shadow-sm focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"><option value="USER">User</option><option value="ADMIN">Admin</option></select></div> <button type="submit" class="inline-flex items-center gap-2 rounded-lg bg-blue-600 dark:bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 dark:hover:bg-blue-700">`;
  Plus($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> Add Member</button></form></div> <div class="mt-6 overflow-hidden"><div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"><thead class="bg-gray-50 dark:bg-gray-700/50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Member</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Role</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Joined</th><th class="relative px-6 py-3"><span class="sr-only">Actions</span></th></tr></thead><tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800"><!--[-->`;
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    let user = each_array[i];
    $$payload.out += `<tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center">`;
    if (user.avatar) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<img${attr("src", user.avatar)}${attr("alt", user.name)} class="h-10 w-10 rounded-full object-cover">`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-600">`;
      User($$payload, {
        class: "h-5 w-5 text-gray-500 dark:text-gray-400"
      });
      $$payload.out += `<!----></div>`;
    }
    $$payload.out += `<!--]--> <div class="ml-4"><div class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(user.name)} `;
    if (user.isSelf) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="ml-2 inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-300">You</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> <div class="text-sm text-gray-500 dark:text-gray-400">${escape_html(user.email)}</div></div></div></td><td class="px-6 py-4 whitespace-nowrap">`;
    if (user.isSelf) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span${attr_class(`inline-flex items-center gap-1.5 rounded-full border px-3 py-1 text-xs font-medium ${stringify(roleColors[user.role])}`)}><!---->`;
      (roleIcons[user.role] || User)?.($$payload, { class: "h-3.5 w-3.5" });
      $$payload.out += `<!----> ${escape_html(user.role)}</span>`;
    } else {
      $$payload.out += "<!--[!-->";
      if (user.editingRole) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<form method="POST" action="?/edit_role" class="flex items-center gap-2"><input type="hidden" name="user_id"${attr("value", user.id)}> <select name="role" class="rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-xs focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"><option value="USER"${attr("selected", user.role === "USER", true)}>User</option><option value="ADMIN"${attr("selected", user.role === "ADMIN", true)}>Admin</option><option value="SALES_REP"${attr("selected", user.role === "SALES_REP", true)}>Sales Rep</option><option value="SUPPORT_REP"${attr("selected", user.role === "SUPPORT_REP", true)}>Support Rep</option><option value="READ_ONLY"${attr("selected", user.role === "READ_ONLY", true)}>Read Only</option></select> <button type="submit" class="rounded-lg bg-green-600 dark:bg-green-600 p-1.5 text-white hover:bg-green-700 dark:hover:bg-green-700" title="Save">`;
        Check($$payload, { class: "h-3.5 w-3.5" });
        $$payload.out += `<!----></button> <button type="button" class="rounded-lg bg-gray-600 dark:bg-gray-600 p-1.5 text-white hover:bg-gray-700 dark:hover:bg-gray-700" title="Cancel">`;
        X($$payload, { class: "h-3.5 w-3.5" });
        $$payload.out += `<!----></button></form>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<button type="button"${attr_class(`inline-flex items-center gap-1.5 rounded-full border px-3 py-1 text-xs font-medium transition-colors ${stringify(roleColors[user.role])} hover:bg-opacity-80 dark:hover:bg-opacity-80`)} title="Click to edit role"><!---->`;
        (roleIcons[user.role] || User)?.($$payload, { class: "h-3.5 w-3.5" });
        $$payload.out += `<!----> ${escape_html(user.role)} `;
        Square_pen($$payload, { class: "h-3 w-3" });
        $$payload.out += `<!----></button>`;
      }
      $$payload.out += `<!--]-->`;
    }
    $$payload.out += `<!--]--></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${escape_html(user.joined)}</td><td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">`;
    if (user.isSelf) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="text-gray-300 dark:text-gray-600 cursor-not-allowed">—</span>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<form method="POST" action="?/remove_user" class="inline"><input type="hidden" name="user_id"${attr("value", user.id)}> <button type="submit" class="rounded-lg p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300" title="Remove user">`;
      Trash_2($$payload, { class: "h-4 w-4" });
      $$payload.out += `<!----></button></form>`;
    }
    $$payload.out += `<!--]--></td></tr>`;
  }
  $$payload.out += `<!--]--></tbody></table></div></div></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
