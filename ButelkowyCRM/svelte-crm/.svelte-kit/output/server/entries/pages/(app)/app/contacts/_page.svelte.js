import { P as head, D as escape_html, J as attr, G as ensure_array_like, I as attr_class, R as stringify, C as pop, A as push } from "../../../../../chunks/index2.js";
import "../../../../../chunks/client.js";
import { U as Users } from "../../../../../chunks/users.js";
import { F as Funnel } from "../../../../../chunks/funnel.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { S as Search } from "../../../../../chunks/search.js";
import { U as User } from "../../../../../chunks/user.js";
import { B as Building } from "../../../../../chunks/building.js";
import { M as Mail } from "../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../chunks/phone.js";
import { C as Calendar } from "../../../../../chunks/calendar.js";
import { E as Eye } from "../../../../../chunks/eye.js";
import { S as Square_pen } from "../../../../../chunks/square-pen.js";
import { C as Chevron_left } from "../../../../../chunks/chevron-left.js";
import { C as Chevron_right } from "../../../../../chunks/chevron-right.js";
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let searchQuery = data.search || "";
  data.ownerId || "";
  function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
  }
  function formatPhone(phone) {
    if (!phone) return "";
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Contacts - BottleCRM</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"><div class="px-4 sm:px-6 lg:px-8 py-6"><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"><div class="flex items-center gap-3"><div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">`;
  Users($$payload, {
    class: "w-6 h-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Contacts</h1> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(data.totalCount)} total contacts</p></div></div> <div class="flex items-center gap-3"><button class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500">`;
  Funnel($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Filters</button> <a href="/app/contacts/new" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">`;
  Plus($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Add Contact</a></div></div> <div class="mt-6"><div class="relative">`;
  Search($$payload, {
    class: "absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
  });
  $$payload.out += `<!----> <input type="text"${attr("value", searchQuery)} placeholder="Search contacts by name, email, phone, title..." class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"> `;
  if (searchQuery) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">×</button>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="px-4 sm:px-6 lg:px-8 py-6">`;
  if (data.contacts.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-12">`;
    Users($$payload, { class: "mx-auto w-12 h-12 text-gray-400" });
    $$payload.out += `<!----> <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No contacts found</h3> <p class="mt-2 text-gray-500 dark:text-gray-400">${escape_html(data.search ? "Try adjusting your search criteria." : "Get started by creating your first contact.")}</p> `;
    if (!data.search) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<a href="/app/contacts/new" class="mt-4 inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">`;
      Plus($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> Add Contact</a>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_1 = ensure_array_like(data.contacts);
    const each_array_2 = ensure_array_like(data.contacts);
    $$payload.out += `<div class="hidden lg:block bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden"><table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"><thead class="bg-gray-50 dark:bg-gray-700"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Contact</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title &amp; Department</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Contact Info</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Owner</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Activity</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Created</th><th class="relative px-6 py-3"><span class="sr-only">Actions</span></th></tr></thead><tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let contact = each_array_1[$$index_1];
      $$payload.out += `<tr class="hover:bg-gray-50 dark:hover:bg-gray-700"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div class="flex-shrink-0 w-10 h-10"><div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">`;
      User($$payload, {
        class: "w-5 h-5 text-blue-600 dark:text-blue-400"
      });
      $$payload.out += `<!----></div></div> <div class="ml-4"><div class="text-sm font-medium text-gray-900 dark:text-white"><button class="hover:text-blue-600 dark:hover:text-blue-400 hover:underline text-left">${escape_html(contact.firstName)} ${escape_html(contact.lastName)}</button></div> `;
      if (contact.relatedAccounts.length > 0) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">`;
        Building($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> ${escape_html(contact.relatedAccounts[0].account.name)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div></td><td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-gray-900 dark:text-white">${escape_html(contact.title || "—")}</div> <div class="text-sm text-gray-500 dark:text-gray-400">${escape_html(contact.department || "—")}</div></td><td class="px-6 py-4 whitespace-nowrap">`;
      if (contact.email) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="text-sm text-gray-900 dark:text-white flex items-center gap-1">`;
        Mail($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> ${escape_html(contact.email)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (contact.phone) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">`;
        Phone($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> ${escape_html(formatPhone(contact.phone))}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></td><td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-gray-900 dark:text-white">${escape_html(contact.owner.name || contact.owner.email)}</div></td><td class="px-6 py-4 whitespace-nowrap"><div class="flex gap-2 text-xs">`;
      if (contact._count.tasks > 0) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">${escape_html(contact._count.tasks)} tasks</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (contact._count.opportunities > 0) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">${escape_html(contact._count.opportunities)} opps</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><div class="flex items-center gap-1">`;
      Calendar($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> ${escape_html(formatDate(contact.createdAt))}</div></td></tr>`;
    }
    $$payload.out += `<!--]--></tbody></table></div> <div class="lg:hidden space-y-4"><!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
      let contact = each_array_2[$$index_2];
      $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4"><div class="flex items-start justify-between"><button class="flex items-center space-x-3 text-left hover:opacity-75"><div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">`;
      User($$payload, {
        class: "w-5 h-5 text-blue-600 dark:text-blue-400"
      });
      $$payload.out += `<!----></div> <div><h3 class="text-sm font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400">${escape_html(contact.firstName)} ${escape_html(contact.lastName)}</h3> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(contact.title || "No title")}</p></div></button> <button class="p-1.5 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">`;
      Eye($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></button></div> <div class="mt-3 space-y-2">`;
      if (contact.email) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">`;
        Mail($$payload, { class: "w-4 h-4" });
        $$payload.out += `<!----> ${escape_html(contact.email)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (contact.phone) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">`;
        Phone($$payload, { class: "w-4 h-4" });
        $$payload.out += `<!----> ${escape_html(formatPhone(contact.phone))}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (contact.relatedAccounts.length > 0) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">`;
        Building($$payload, { class: "w-4 h-4" });
        $$payload.out += `<!----> ${escape_html(contact.relatedAccounts[0].account.name)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="mt-3 flex justify-between items-center"><div class="flex gap-2">`;
      if (contact._count.tasks > 0) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">${escape_html(contact._count.tasks)} tasks</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (contact._count.opportunities > 0) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">${escape_html(contact._count.opportunities)} opps</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="flex gap-2"><button class="p-1.5 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">`;
      Eye($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></button> <button class="p-1.5 text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400">`;
      Square_pen($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></button></div></div></div>`;
    }
    $$payload.out += `<!--]--></div> `;
    if (data.totalPages > 1) {
      $$payload.out += "<!--[-->";
      const each_array_3 = ensure_array_like(Array.from({ length: Math.min(5, data.totalPages) }, (_, i) => {
        const start = Math.max(1, data.currentPage - 2);
        return start + i;
      }));
      $$payload.out += `<div class="mt-6 flex items-center justify-between"><div class="text-sm text-gray-700 dark:text-gray-300">Showing ${escape_html((data.currentPage - 1) * data.limit + 1)} to ${escape_html(Math.min(data.currentPage * data.limit, data.totalCount))} of ${escape_html(data.totalCount)} contacts</div> <div class="flex items-center gap-2"><button${attr("disabled", data.currentPage === 1, true)} class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed">`;
      Chevron_left($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></button> <!--[-->`;
      for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
        let pageNum = each_array_3[$$index_3];
        if (pageNum <= data.totalPages) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<button${attr_class(`px-3 py-1 text-sm ${stringify(pageNum === data.currentPage ? "bg-blue-600 text-white" : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white")} rounded`)}>${escape_html(pageNum)}</button>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]-->`;
      }
      $$payload.out += `<!--]--> <button${attr("disabled", data.currentPage === data.totalPages, true)} class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed">`;
      Chevron_right($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----></button></div></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></div></div>`;
  pop();
}
export {
  _page as default
};
