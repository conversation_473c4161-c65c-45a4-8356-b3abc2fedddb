import { D as escape_html, J as attr, G as ensure_array_like, R as stringify, I as attr_class, M as bind_props, C as pop, A as push } from "../../../../../../chunks/index2.js";
import { formatDistanceToNow } from "date-fns";
import { U as User } from "../../../../../../chunks/user.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
import { S as Search } from "../../../../../../chunks/search.js";
import { F as Funnel } from "../../../../../../chunks/funnel.js";
import { C as Chevron_down } from "../../../../../../chunks/chevron-down.js";
import { B as Building_2 } from "../../../../../../chunks/building-2.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../../chunks/phone.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { E as Eye } from "../../../../../../chunks/eye.js";
import { C as Circle_alert } from "../../../../../../chunks/circle-alert.js";
import { C as Circle_check } from "../../../../../../chunks/circle-check.js";
import { C as Circle_x } from "../../../../../../chunks/circle-x.js";
import { T as Trending_up } from "../../../../../../chunks/trending-up.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
import { S as Star } from "../../../../../../chunks/star.js";
function _page($$payload, $$props) {
  push();
  let filteredLeads;
  let data = $$props["data"];
  const { leads } = data;
  let searchQuery = "";
  let statusFilter = "ALL";
  let sourceFilter = "ALL";
  let ratingFilter = "ALL";
  let sortBy = "createdAt";
  function getFullName(lead) {
    return `${lead.firstName} ${lead.lastName}`.trim();
  }
  function getStatusConfig(status) {
    switch (status) {
      case "NEW":
        return {
          color: "bg-blue-100 text-blue-800 border-blue-200",
          icon: Star
        };
      case "PENDING":
        return {
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          icon: Clock
        };
      case "CONTACTED":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          icon: Circle_check
        };
      case "QUALIFIED":
        return {
          color: "bg-indigo-100 text-indigo-800 border-indigo-200",
          icon: Trending_up
        };
      case "UNQUALIFIED":
        return {
          color: "bg-red-100 text-red-800 border-red-200",
          icon: Circle_x
        };
      case "CONVERTED":
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          icon: Circle_check
        };
      default:
        return {
          color: "bg-blue-100 text-blue-800 border-blue-200",
          icon: Circle_alert
        };
    }
  }
  function getRatingConfig(rating) {
    switch (rating) {
      case "Hot":
        return { color: "text-red-600", dots: 3 };
      case "Warm":
        return { color: "text-orange-500", dots: 2 };
      case "Cold":
        return { color: "text-blue-500", dots: 1 };
      default:
        return { color: "text-gray-400", dots: 0 };
    }
  }
  function formatDate(dateString) {
    if (!dateString) return "-";
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  }
  filteredLeads = leads.filter((lead) => {
    const matchesSearch = searchQuery === "";
    const matchesStatus = statusFilter === "ALL";
    const matchesSource = sourceFilter === "ALL";
    const matchesRating = ratingFilter === "ALL";
    return matchesSearch && matchesStatus && matchesSource && matchesRating;
  }).sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    {
      return aValue < bValue ? 1 : -1;
    }
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"><div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4"><div class="flex items-center gap-3"><div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">`;
  User($$payload, {
    class: "w-6 h-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100">Open Leads</h1> <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">${escape_html(filteredLeads.length)} of ${escape_html(leads.length)} leads</p></div></div> <a href="/app/leads/new" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-700 transition-colors duration-200 font-medium">`;
  Plus($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> New Lead</a></div></div></header> <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6"><div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex flex-col sm:flex-row gap-4 mb-4"><div class="flex-1 relative">`;
  Search($$payload, {
    class: "absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----> <input type="text" placeholder="Search by name, company, or email..."${attr("value", searchQuery)} class="w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"></div> <button class="inline-flex items-center gap-2 px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">`;
  Funnel($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Filters `;
  {
    $$payload.out += "<!--[!-->";
    Chevron_down($$payload, { class: "w-4 h-4" });
  }
  $$payload.out += `<!--]--></button></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <main class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 pb-8">`;
  if (filteredLeads.length === 0) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="text-center py-16 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"><div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">📭</div> <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No leads found</h3> <p class="text-gray-500 dark:text-gray-400 mb-6">Try adjusting your search criteria or create a new lead.</p> <a href="/app/leads/new" class="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 dark:bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-700 transition-colors font-medium">`;
    Plus($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Create New Lead</a></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_3 = ensure_array_like(filteredLeads);
    const each_array_5 = ensure_array_like(filteredLeads);
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="hidden xl:block"><div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"><thead class="bg-gray-50 dark:bg-gray-700"><tr><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-48">Lead</th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 w-40"><div class="flex items-center gap-1">Company `;
    {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-48">Contact</th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32">Source</th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">Rating</th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32">Status</th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 w-32"><div class="flex items-center gap-1">Created `;
    {
      $$payload.out += "<!--[-->";
      {
        $$payload.out += "<!--[!-->";
        Chevron_down($$payload, { class: "w-4 h-4" });
      }
      $$payload.out += `<!--]-->`;
    }
    $$payload.out += `<!--]--></div></th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32">Owner</th><th class="px-4 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">Actions</th></tr></thead><tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`;
    for (let i = 0, $$length = each_array_3.length; i < $$length; i++) {
      let lead = each_array_3[i];
      const statusConfig = getStatusConfig(lead.status);
      const ratingConfig = getRatingConfig(lead.rating);
      $$payload.out += `<tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"><td class="px-4 py-4"><div class="flex items-center gap-3"><div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-full flex items-center justify-center text-white font-medium text-sm flex-shrink-0">${escape_html(lead.firstName.charAt(0))}${escape_html(lead.lastName.charAt(0))}</div> <div class="min-w-0"><a${attr("href", `/app/leads/${stringify(lead.id)}`)} class="text-gray-900 dark:text-gray-100 font-medium hover:text-blue-600 dark:hover:text-blue-400 transition-colors block truncate">${escape_html(getFullName(lead))}</a> `;
      if (lead.title) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-gray-500 dark:text-gray-400 truncate">${escape_html(lead.title)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div></td><td class="px-4 py-4">`;
      if (lead.company) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 min-w-0">`;
        Building_2($$payload, {
          class: "w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0"
        });
        $$payload.out += `<!----> <span class="text-gray-900 dark:text-gray-100 truncate">${escape_html(lead.company)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<span class="text-gray-400 dark:text-gray-500">-</span>`;
      }
      $$payload.out += `<!--]--></td><td class="px-4 py-4"><div class="space-y-1">`;
      if (lead.email) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<a${attr("href", `mailto:${stringify(lead.email)}`)} class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors min-w-0">`;
        Mail($$payload, { class: "w-4 h-4 flex-shrink-0" });
        $$payload.out += `<!----> <span class="truncate">${escape_html(lead.email)}</span></a>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (lead.phone) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<a${attr("href", `tel:${stringify(lead.phone)}`)} class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">`;
        Phone($$payload, { class: "w-4 h-4 flex-shrink-0" });
        $$payload.out += `<!----> <span class="whitespace-nowrap">${escape_html(lead.phone)}</span></a>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (!lead.email && !lead.phone) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="text-gray-400 dark:text-gray-500">-</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></td><td class="px-4 py-4">`;
      if (lead.leadSource) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="text-sm text-gray-600 dark:text-gray-300 capitalize truncate block">${escape_html(lead.leadSource.replace("_", " ").toLowerCase())}</span>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<span class="text-gray-400 dark:text-gray-500">-</span>`;
      }
      $$payload.out += `<!--]--></td><td class="px-4 py-4">`;
      if (lead.rating) {
        $$payload.out += "<!--[-->";
        const each_array_4 = ensure_array_like(Array(ratingConfig.dots));
        $$payload.out += `<div class="flex items-center gap-1"><!--[-->`;
        for (let i2 = 0, $$length2 = each_array_4.length; i2 < $$length2; i2++) {
          each_array_4[i2];
          $$payload.out += `<div${attr_class(`w-2 h-2 rounded-full ${stringify(ratingConfig.color.replace("text-", "bg-"))} flex-shrink-0`)}></div>`;
        }
        $$payload.out += `<!--]--> <span${attr_class(`text-sm ${stringify(ratingConfig.color)} font-medium ml-1 whitespace-nowrap`)}>${escape_html(lead.rating)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<span class="text-gray-400 dark:text-gray-500">-</span>`;
      }
      $$payload.out += `<!--]--></td><td class="px-4 py-4"><div class="flex items-center gap-2"><!---->`;
      statusConfig.icon?.($$payload, {
        class: `w-4 h-4 ${stringify(statusConfig.color.split(" ")[1])} flex-shrink-0`
      });
      $$payload.out += `<!----> <span${attr_class(`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${stringify(statusConfig.color)} whitespace-nowrap`)}>${escape_html(lead.status)}</span></div></td><td class="px-4 py-4"><div class="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">`;
      Calendar($$payload, { class: "w-4 h-4 flex-shrink-0" });
      $$payload.out += `<!----> <span class="whitespace-nowrap">${escape_html(formatDate(lead.createdAt))}</span></div></td><td class="px-4 py-4">`;
      if (lead.owner?.name) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 min-w-0"><div class="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 text-gray-700 dark:text-gray-200">${escape_html(lead.owner.name.charAt(0))}</div> <span class="text-sm text-gray-600 dark:text-gray-300 truncate">${escape_html(lead.owner.name)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<span class="text-gray-400 dark:text-gray-500">-</span>`;
      }
      $$payload.out += `<!--]--></td><td class="px-4 py-4"><a${attr("href", `/app/leads/${stringify(lead.id)}`)} class="inline-flex items-center gap-1 px-3 py-1.5 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors whitespace-nowrap">`;
      Eye($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> View</a></td></tr>`;
    }
    $$payload.out += `<!--]--></tbody></table></div></div> <div class="xl:hidden divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`;
    for (let i = 0, $$length = each_array_5.length; i < $$length; i++) {
      let lead = each_array_5[i];
      const statusConfig = getStatusConfig(lead.status);
      const ratingConfig = getRatingConfig(lead.rating);
      $$payload.out += `<div class="p-6 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"><div class="flex items-start justify-between mb-4"><div class="flex items-center gap-3"><div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-full flex items-center justify-center text-white font-medium">${escape_html(lead.firstName.charAt(0))}${escape_html(lead.lastName.charAt(0))}</div> <div><a${attr("href", `/app/leads/${stringify(lead.id)}`)} class="text-lg font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">${escape_html(getFullName(lead))}</a> `;
      if (lead.title) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(lead.title)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div> <div class="flex items-center gap-2"><!---->`;
      statusConfig.icon?.($$payload, {
        class: `w-4 h-4 ${stringify(statusConfig.color.split(" ")[1])}`
      });
      $$payload.out += `<!----> <span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${stringify(statusConfig.color)}`)}>${escape_html(lead.status)}</span></div></div> <div class="grid grid-cols-1 gap-3">`;
      if (lead.company) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2">`;
        Building_2($$payload, {
          class: "w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0"
        });
        $$payload.out += `<!----> <span class="text-gray-700 dark:text-gray-200">${escape_html(lead.company)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (lead.email) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<a${attr("href", `mailto:${stringify(lead.email)}`)} class="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">`;
        Mail($$payload, { class: "w-4 h-4 flex-shrink-0" });
        $$payload.out += `<!----> <span class="truncate">${escape_html(lead.email)}</span></a>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (lead.phone) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<a${attr("href", `tel:${stringify(lead.phone)}`)} class="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">`;
        Phone($$payload, { class: "w-4 h-4 flex-shrink-0" });
        $$payload.out += `<!----> <span>${escape_html(lead.phone)}</span></a>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="flex items-center justify-between text-sm"><div class="flex items-center gap-2 text-gray-500 dark:text-gray-400">`;
      Calendar($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> ${escape_html(formatDate(lead.createdAt))}</div> `;
      if (lead.rating) {
        $$payload.out += "<!--[-->";
        const each_array_6 = ensure_array_like(Array(ratingConfig.dots));
        $$payload.out += `<div class="flex items-center gap-1"><!--[-->`;
        for (let i2 = 0, $$length2 = each_array_6.length; i2 < $$length2; i2++) {
          each_array_6[i2];
          $$payload.out += `<div${attr_class(`w-2 h-2 rounded-full ${stringify(ratingConfig.color.replace("text-", "bg-"))}`)}></div>`;
        }
        $$payload.out += `<!--]--> <span${attr_class(`text-sm ${stringify(ratingConfig.color)} font-medium ml-1`)}>${escape_html(lead.rating)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> `;
      if (lead.owner?.name) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">`;
        User($$payload, { class: "w-4 h-4" });
        $$payload.out += `<!----> <span>Owned by ${escape_html(lead.owner.name)}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"><a${attr("href", `/app/leads/${stringify(lead.id)}`)} class="inline-flex items-center gap-2 px-4 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors font-medium">`;
      Eye($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> View Details</a></div></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  }
  $$payload.out += `<!--]--></main></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
