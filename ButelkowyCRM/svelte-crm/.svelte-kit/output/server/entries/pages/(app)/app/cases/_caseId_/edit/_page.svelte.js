import { G as ensure_array_like, J as attr, D as escape_html, M as bind_props, C as pop, A as push } from "../../../../../../../chunks/index2.js";
import "../../../../../../../chunks/client.js";
import { X } from "../../../../../../../chunks/x.js";
import { F as File_text } from "../../../../../../../chunks/file-text.js";
import { B as Building } from "../../../../../../../chunks/building.js";
import { C as Calendar } from "../../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../../chunks/user.js";
import { F as Flag } from "../../../../../../../chunks/flag.js";
import { S as Save } from "../../../../../../../chunks/save.js";
import { L as Lock_open } from "../../../../../../../chunks/lock-open.js";
import { L as Lock } from "../../../../../../../chunks/lock.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let title = data.caseItem.subject;
  let description = data.caseItem.description || "";
  data.caseItem.accountId;
  let dueDate = "";
  if (data.caseItem.dueDate) {
    if (typeof data.caseItem.dueDate === "string") {
      dueDate = data.caseItem.dueDate.split("T")[0];
    } else if (data.caseItem.dueDate instanceof Date) {
      dueDate = data.caseItem.dueDate.toISOString().split("T")[0];
    }
  }
  data.caseItem.ownerId;
  data.caseItem.priority || "Medium";
  const each_array = ensure_array_like(data.accounts);
  const each_array_1 = ensure_array_like(data.users);
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8"><div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8"><div class="mb-8"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Case</h1> <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Update case details and assignment</p></div> <div class="flex items-center gap-3"><button class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors">`;
  X($$payload, { size: "16" });
  $$payload.out += `<!----> Cancel</button></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><form method="POST" action="?/update"><div class="p-6 space-y-6"><div class="space-y-2"><label for="title" class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">`;
  File_text($$payload, {
    size: "16",
    class: "text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Case Title <span class="text-red-500 dark:text-red-400">*</span></label> <input id="title" type="text" name="title"${attr("value", title)} required class="w-full px-3 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400" placeholder="Enter case title"></div> <div class="space-y-2"><label for="description" class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">`;
  File_text($$payload, {
    size: "16",
    class: "text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Description</label> <textarea id="description" name="description" rows="4" class="w-full px-3 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-500 transition-colors resize-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400" placeholder="Describe the case details...">`;
  const $$body = escape_html(description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> <div class="space-y-2"><label for="accountId" class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">`;
  Building($$payload, {
    size: "16",
    class: "text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Account <span class="text-red-500 dark:text-red-400">*</span></label> <select id="accountId" name="accountId" required class="w-full px-3 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><option value="">Select an account...</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let acc = each_array[$$index];
    $$payload.out += `<option${attr("value", acc.id)}>${escape_html(acc.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-2"><label for="dueDate" class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">`;
  Calendar($$payload, {
    size: "16",
    class: "text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Due Date</label> <input id="dueDate" type="date" name="dueDate"${attr("value", dueDate)} class="w-full px-3 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></div> <div class="space-y-2"><label for="assignedId" class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">`;
  User($$payload, {
    size: "16",
    class: "text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Assign To <span class="text-red-500 dark:text-red-400">*</span></label> <select id="assignedId" name="assignedId" required class="w-full px-3 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><option value="">Select a user...</option><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let u = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", u.id)}>${escape_html(u.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div> <div class="space-y-2"><label for="priority" class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">`;
  Flag($$payload, {
    size: "16",
    class: "text-gray-500 dark:text-gray-400"
  });
  $$payload.out += `<!----> Priority</label> <select id="priority" name="priority" class="w-full px-3 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white"><option value="High">High</option><option value="Medium">Medium</option><option value="Low">Low</option></select></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row gap-3"><button type="submit"${attr("disabled", data.caseItem.status === "CLOSED", true)} class="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-white bg-blue-600 dark:bg-blue-600 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">`;
  Save($$payload, { size: "16" });
  $$payload.out += `<!----> ${escape_html(data.caseItem.status === "CLOSED" ? "Case is Closed" : "Save Changes")}</button> `;
  if (data.caseItem.status === "CLOSED") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button type="button" class="inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">`;
    Lock_open($$payload, { size: "16" });
    $$payload.out += `<!----> Reopen Case</button>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<button type="button" class="inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-amber-700 dark:text-amber-300 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg hover:bg-amber-100 dark:hover:bg-amber-900/30 focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">`;
    Lock($$payload, { size: "16" });
    $$payload.out += `<!----> Close Case</button>`;
  }
  $$payload.out += `<!--]--></div></form></div> <form id="close-case-form" method="POST" action="?/close" style="display: none;"></form> <form id="reopen-case-form" method="POST" action="?/reopen" style="display: none;"></form> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
