import { G as ensure_array_like, D as escape_html, J as attr, I as attr_class, M as bind_props, C as pop, A as push, R as stringify } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
import { T as Target } from "../../../../../../chunks/target.js";
import { C as Circle_alert } from "../../../../../../chunks/circle-alert.js";
import { B as Briefcase } from "../../../../../../chunks/briefcase.js";
import { B as Building } from "../../../../../../chunks/building.js";
import { G as Globe } from "../../../../../../chunks/globe.js";
import { D as Dollar_sign } from "../../../../../../chunks/dollar-sign.js";
import { P as Percent } from "../../../../../../chunks/percent.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../chunks/user.js";
import { P as Phone } from "../../../../../../chunks/phone.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { M as Map_pin } from "../../../../../../chunks/map-pin.js";
import { X } from "../../../../../../chunks/x.js";
import { S as Save } from "../../../../../../chunks/save.js";
function _page($$payload, $$props) {
  push();
  let form = $$props["form"];
  let data = $$props["data"];
  let formData = {
    lead_title: "",
    opportunity_amount: "",
    website: "",
    probability: "",
    first_name: "",
    last_name: "",
    company: "",
    title: "",
    phone: "",
    email: "",
    address_line: "",
    city: "",
    state: "",
    postcode: "",
    country: "",
    description: "",
    // Additional fields for better lead management
    linkedin_url: "",
    pain_points: "",
    last_contacted: "",
    next_follow_up: ""
  };
  let errors = {};
  let isSubmitting = false;
  const each_array = ensure_array_like(data.data.source);
  const each_array_1 = ensure_array_like(data.data.industries);
  const each_array_2 = ensure_array_like(data.data.status);
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center justify-between"><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">`;
  Target($$payload, {
    class: "w-6 h-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Create New Lead</h1> <p class="text-gray-600 dark:text-gray-300 mt-1">Capture lead information and start building relationships</p></div></div></div></div> `;
  if (form?.error) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"><div class="flex items-center">`;
    Circle_alert($$payload, {
      class: "w-5 h-5 text-red-400 dark:text-red-400 mr-2"
    });
    $$payload.out += `<!----> <span class="font-medium text-red-800 dark:text-red-200">Error:</span> <span class="ml-1 text-red-700 dark:text-red-300">${escape_html(form.error)}</span></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" class="space-y-6"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">`;
  Briefcase($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Lead Information</h2></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="md:col-span-2"><label for="lead_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Lead Title *</label> <input id="lead_title" name="lead_title" type="text"${attr("value", formData.lead_title)} placeholder="Enter lead title" required${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.lead_title ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.lead_title) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.lead_title)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Building($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Company</label> <input id="company" name="company" type="text"${attr("value", formData.company)} placeholder="Company name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="source" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Lead Source</label> <select id="source" name="source" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"><option value="">Select source</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let [value, label] = each_array[$$index];
    $$payload.out += `<option${attr("value", value)}>${escape_html(label)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Industry</label> <select id="industry" name="industry" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"><option value="">Select industry</option><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let [value, label] = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", value)}>${escape_html(label)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label> <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let [value, label] = each_array_2[$$index_2];
    $$payload.out += `<option${attr("value", value)}>${escape_html(label)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rating</label> <select id="rating" name="rating" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"><option value="">Select rating</option><option value="HOT">🔥 Hot</option><option value="WARM">🟡 Warm</option><option value="COLD">🟦 Cold</option></select></div> <div><label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Globe($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Website</label> <input id="website" name="website" type="url"${attr("value", formData.website)} placeholder="https://company.com"${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.website ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.website) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.website)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="opportunity_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Dollar_sign($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Opportunity Amount</label> <input type="number" id="opportunity_amount" name="opportunity_amount"${attr("value", formData.opportunity_amount)} placeholder="0" min="0" step="0.01" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="probability" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Percent($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Probability (%)</label> <input id="probability" name="probability" type="number" min="0" max="100"${attr("value", formData.probability)} placeholder="50"${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.probability ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.probability) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.probability)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="budget_range" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Budget Range</label> <select id="budget_range" name="budget_range" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"><option value="">Select budget range</option><option value="under_10k">Under $10K</option><option value="10k_50k">$10K - $50K</option><option value="50k_100k">$50K - $100K</option><option value="100k_500k">$100K - $500K</option><option value="500k_plus">$500K+</option></select></div> <div><label for="decision_timeframe" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Calendar($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Decision Timeframe</label> <select id="decision_timeframe" name="decision_timeframe" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"><option value="">Select timeframe</option><option value="immediate">Immediate (&lt; 1 month)</option><option value="short_term">Short term (1-3 months)</option><option value="medium_term">Medium term (3-6 months)</option><option value="long_term">Long term (6+ months)</option></select></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">`;
  User($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Contact Information</h2></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</label> <input id="first_name" name="first_name" type="text"${attr("value", formData.first_name)} placeholder="First name" required${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.first_name ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.first_name) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.first_name)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name *</label> <input id="last_name" name="last_name" type="text"${attr("value", formData.last_name)} placeholder="Last name" required${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.last_name ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.last_name) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.last_name)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Job Title</label> <input id="title" name="title" type="text"${attr("value", formData.title)} placeholder="Job title" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Phone($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Phone</label> <input id="phone" name="phone" type="tel"${attr("value", formData.phone)} placeholder="+****************"${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.phone ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.phone) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.phone)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="md:col-span-2"><label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Mail($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Email *</label> <input id="email" type="email" name="email"${attr("value", formData.email)} placeholder="<EMAIL>" required${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.email ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.email)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="md:col-span-2"><label for="linkedin_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">LinkedIn Profile</label> <input id="linkedin_url" name="linkedin_url" type="url"${attr("value", formData.linkedin_url)} placeholder="https://linkedin.com/in/username"${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors ${stringify(errors.linkedin_url ? "border-red-500 dark:border-red-400 ring-1 ring-red-500 dark:ring-red-400" : "")}`)}> `;
  if (errors.linkedin_url) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-red-500 dark:text-red-400 text-sm mt-1">${escape_html(errors.linkedin_url)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">`;
  Map_pin($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> Address Information</h2></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="md:col-span-2"><label for="address_line" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line</label> <input id="address_line" name="address_line" type="text"${attr("value", formData.address_line)} placeholder="Street address" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City</label> <input id="city" name="city" type="text"${attr("value", formData.city)} placeholder="City" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">State</label> <input id="state" name="state" type="text"${attr("value", formData.state)} placeholder="State" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="postcode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Postal Code</label> <input id="postcode" name="postcode" type="text"${attr("value", formData.postcode)} placeholder="Postal code" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country</label> <input id="country" name="country" type="text"${attr("value", formData.country)} placeholder="Country" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-semibold text-gray-900 dark:text-white">Additional Details</h2></div> <div class="p-6 space-y-6"><div><label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label> <textarea id="description" name="description" placeholder="Additional notes about this lead..." rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors resize-vertical">`;
  const $$body = escape_html(formData.description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> <div><label for="pain_points" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Pain Points</label> <textarea id="pain_points" name="pain_points" placeholder="What challenges is the lead facing?" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors resize-vertical">`;
  const $$body_1 = escape_html(formData.pain_points);
  if ($$body_1) {
    $$payload.out += `${$$body_1}`;
  }
  $$payload.out += `</textarea></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="last_contacted" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Contacted</label> <input id="last_contacted" name="last_contacted" type="date"${attr("value", formData.last_contacted)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div> <div><label for="next_follow_up" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Next Follow-up</label> <input id="next_follow_up" name="next_follow_up" type="date"${attr("value", formData.next_follow_up)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"><div class="px-6 py-4"><div class="flex justify-end gap-4"><button type="button"${attr("disabled", isSubmitting, true)} class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2">`;
  X($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Cancel</button> <button type="submit"${attr("disabled", isSubmitting, true)} class="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2">`;
  {
    $$payload.out += "<!--[!-->";
    Save($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Create Lead`;
  }
  $$payload.out += `<!--]--></button></div></div></div></form></div></div>`;
  bind_props($$props, { form, data });
  pop();
}
export {
  _page as default
};
