import { G as ensure_array_like, J as attr, D as escape_html, R as stringify, M as bind_props, C as pop, A as push } from "../../../../../chunks/index2.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  $$payload.out += `<div class="board-bg svelte-sh3i1b"><h1 class="svelte-sh3i1b">Boards</h1> <div class="board-list svelte-sh3i1b">`;
  if (data?.boards?.length) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(data.boards);
    $$payload.out += `<!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let board = each_array[$$index];
      $$payload.out += `<a class="board-card svelte-sh3i1b"${attr("href", `/app/tasks/board/${stringify(board.id)}`)}><div class="board-title svelte-sh3i1b">${escape_html(board.name)}</div></a>`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="no-boards svelte-sh3i1b">No boards found.</p>`;
  }
  $$payload.out += `<!--]--> <form class="board-card board-create svelte-sh3i1b" method="POST" action="?/create"><div class="plus-icon svelte-sh3i1b">+</div> <input name="name" placeholder="New board name" required class="svelte-sh3i1b"> <button type="submit" class="svelte-sh3i1b">Create Board</button></form></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
