import { A as push, E as spread_props, C as pop, D as escape_html, J as attr, R as stringify, G as ensure_array_like, I as attr_class, M as bind_props } from "../../../../../../chunks/index2.js";
import { B as Briefcase } from "../../../../../../chunks/briefcase.js";
import { P as Pen_line } from "../../../../../../chunks/pen-line.js";
import { B as Building } from "../../../../../../chunks/building.js";
import { U as User } from "../../../../../../chunks/user.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
import { M as Message_circle } from "../../../../../../chunks/message-circle.js";
import { S as Send } from "../../../../../../chunks/send.js";
import { C as Circle_alert } from "../../../../../../chunks/circle-alert.js";
import { C as Circle_check_big } from "../../../../../../chunks/circle-check-big.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
function Rotate_ccw($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"
      }
    ],
    ["path", { "d": "M3 3v5h5" }]
  ];
  Icon($$payload, spread_props([
    { name: "rotate-ccw" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let comment = "";
  function getPriorityColor(priority) {
    switch (priority) {
      case "High":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800";
      case "Medium":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800";
      case "Low":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800";
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700";
    }
  }
  function getStatusColor(status) {
    switch (status) {
      case "OPEN":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800";
      case "IN_PROGRESS":
        return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800";
      case "CLOSED":
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700";
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700";
    }
  }
  function getStatusIcon(status) {
    switch (status) {
      case "OPEN":
        return Circle_alert;
      case "IN_PROGRESS":
        return Rotate_ccw;
      case "CLOSED":
        return Circle_check_big;
      default:
        return Circle_alert;
    }
  }
  $$payload.out += `<section class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="container mx-auto p-4 max-w-5xl"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"><div class="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4"><div class="flex items-start gap-4"><div class="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">`;
  Briefcase($$payload, {
    class: "w-6 h-6 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">${escape_html(data.caseItem.subject)}</h1> <p class="text-gray-600 dark:text-gray-400 text-sm">Case #${escape_html(data.caseItem.caseNumber)}</p></div></div> <div class="flex gap-3"><a${attr("href", `/app/cases/${stringify(data.caseItem.id)}/edit`)} class="inline-flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">`;
  Pen_line($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Edit</a></div></div></div> <div class="grid grid-cols-1 lg:grid-cols-3 gap-6"><div class="lg:col-span-2 space-y-6"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Case Information</h2> `;
  if (data.caseItem.description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6"><h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</h3> <p class="text-gray-600 dark:text-gray-400 leading-relaxed">${escape_html(data.caseItem.description)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-4"><div class="flex items-center gap-3">`;
  Building($$payload, {
    class: "w-4 h-4 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----> <div><p class="text-sm font-medium text-gray-700 dark:text-gray-300">Account</p> <p class="text-gray-900 dark:text-white">${escape_html(data.caseItem.account?.name || "Not assigned")}</p></div></div> <div class="flex items-center gap-3">`;
  User($$payload, {
    class: "w-4 h-4 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----> <div><p class="text-sm font-medium text-gray-700 dark:text-gray-300">Assigned to</p> <p class="text-gray-900 dark:text-white">${escape_html(data.caseItem.owner?.name || "Unassigned")}</p></div></div></div> <div class="space-y-4">`;
  if (data.caseItem.dueDate) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-3">`;
    Calendar($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <div><p class="text-sm font-medium text-gray-700 dark:text-gray-300">Due Date</p> <p class="text-gray-900 dark:text-white">${escape_html(new Date(data.caseItem.dueDate).toLocaleDateString())}</p></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex items-center gap-3">`;
  Clock($$payload, {
    class: "w-4 h-4 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----> <div><p class="text-sm font-medium text-gray-700 dark:text-gray-300">Created</p> <p class="text-gray-900 dark:text-white">${escape_html(new Date(data.caseItem.createdAt).toLocaleDateString())}</p></div></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><div class="flex items-center gap-2 mb-6">`;
  Message_circle($$payload, {
    class: "w-5 h-5 text-gray-600 dark:text-gray-400"
  });
  $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Comments</h2> <span class="text-sm text-gray-500 dark:text-gray-400">(${escape_html(data.caseItem.comments?.length || 0)})</span></div> <div class="space-y-4 mb-6">`;
  if (data.caseItem.comments && data.caseItem.comments.length) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(data.caseItem.comments);
    $$payload.out += `<!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let c = each_array[$$index];
      $$payload.out += `<div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600"><div class="flex items-start gap-3"><div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0"><span class="text-blue-700 dark:text-blue-300 font-semibold text-sm">${escape_html(c.author?.name?.[0]?.toUpperCase() || "U")}</span></div> <div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><p class="font-medium text-gray-900 dark:text-white">${escape_html(c.author?.name || "Unknown User")}</p> <span class="text-gray-400 dark:text-gray-500">•</span> <p class="text-sm text-gray-500 dark:text-gray-400">${escape_html(new Date(c.createdAt).toLocaleDateString())}</p></div> <p class="text-gray-700 dark:text-gray-300 leading-relaxed">${escape_html(c.body)}</p></div></div></div>`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="text-center py-8 text-gray-500 dark:text-gray-400">`;
    Message_circle($$payload, {
      class: "w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600"
    });
    $$payload.out += `<!----> <p>No comments yet. Be the first to add one!</p></div>`;
  }
  $$payload.out += `<!--]--></div> <form method="POST" action="?/comment" class="border-t border-gray-200 dark:border-gray-600 pt-4"><div class="flex gap-3"><input type="text" name="body"${attr("value", comment)} placeholder="Write a comment..." required class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"> <button type="submit" class="px-6 py-3 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center gap-2">`;
  Send($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Post</button></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></form></div></div> <div class="space-y-6"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status &amp; Priority</h3> <div class="space-y-4"><div><p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</p> <div class="flex items-center gap-2"><!---->`;
  getStatusIcon(data.caseItem.status)?.($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span${attr_class(`px-3 py-1 rounded-full text-sm font-medium border ${stringify(getStatusColor(data.caseItem.status))}`)}>${escape_html(data.caseItem.status.replace("_", " "))}</span></div></div> <div><p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</p> <span${attr_class(`px-3 py-1 rounded-full text-sm font-medium border ${stringify(getPriorityColor(data.caseItem.priority))}`)}>${escape_html(data.caseItem.priority)}</span></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Activity Timeline</h3> <div class="space-y-4"><div class="flex items-start gap-3"><div class="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mt-2 flex-shrink-0"></div> <div class="flex-1"><p class="text-sm font-medium text-gray-900 dark:text-white">Case Created</p> <p class="text-xs text-gray-500 dark:text-gray-400">${escape_html(new Date(data.caseItem.createdAt).toLocaleDateString())}</p></div></div> `;
  if (data.caseItem.updatedAt && data.caseItem.updatedAt !== data.caseItem.createdAt) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-start gap-3"><div class="w-2 h-2 bg-yellow-500 dark:bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div> <div class="flex-1"><p class="text-sm font-medium text-gray-900 dark:text-white">Last Updated</p> <p class="text-xs text-gray-500 dark:text-gray-400">${escape_html(new Date(data.caseItem.updatedAt).toLocaleDateString())}</p></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (data.caseItem.closedAt) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-start gap-3"><div class="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mt-2 flex-shrink-0"></div> <div class="flex-1"><p class="text-sm font-medium text-gray-900 dark:text-white">Case Closed</p> <p class="text-xs text-gray-500 dark:text-gray-400">${escape_html(new Date(data.caseItem.closedAt).toLocaleDateString())}</p></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div></div></section>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
