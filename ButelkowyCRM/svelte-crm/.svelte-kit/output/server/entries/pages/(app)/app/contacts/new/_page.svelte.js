import { F as store_get, P as head, J as attr, D as escape_html, G as ensure_array_like, I as attr_class, R as stringify, K as unsubscribe_stores, C as pop, A as push } from "../../../../../../chunks/index2.js";
import "../../../../../../chunks/client.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { A as Arrow_left } from "../../../../../../chunks/arrow-left.js";
import { B as Building } from "../../../../../../chunks/building.js";
import { U as User } from "../../../../../../chunks/user.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../../chunks/phone.js";
import { M as Map_pin } from "../../../../../../chunks/map-pin.js";
import { F as File_text } from "../../../../../../chunks/file-text.js";
import { S as Save } from "../../../../../../chunks/save.js";
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data, form } = $$props;
  let isSubmitting = false;
  const accountId = store_get($$store_subs ??= {}, "$page", page).url.searchParams.get("accountId");
  let formValues = {
    firstName: form?.values?.firstName || "",
    lastName: form?.values?.lastName || "",
    email: form?.values?.email || "",
    phone: form?.values?.phone || "",
    title: form?.values?.title || "",
    department: form?.values?.department || "",
    street: form?.values?.street || "",
    city: form?.values?.city || "",
    state: form?.values?.state || "",
    postalCode: form?.values?.postalCode || "",
    country: form?.values?.country || "",
    description: form?.values?.description || "",
    organizationId: form?.values?.organizationId || (data.organizations?.[0]?.id || ""),
    accountId: form?.values?.accountId || accountId || "",
    isPrimary: form?.values?.isPrimary || false,
    role: form?.values?.role || ""
  };
  const errors = form?.errors || {};
  const selectedAccount = accountId ? data.accounts?.find((acc) => acc.id === accountId) : null;
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>New Contact - BottleCRM</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><a${attr("href", accountId ? `/app/accounts/${accountId}` : "/app/contacts")} class="inline-flex items-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">`;
  Arrow_left($$payload, { class: "w-5 h-5 mr-2" });
  $$payload.out += `<!----> ${escape_html(accountId ? "Back to Account" : "Back to Contacts")}</a> <div class="border-l border-gray-300 dark:border-gray-600 pl-4"><h1 class="text-2xl font-semibold text-gray-900 dark:text-white">New Contact</h1> <p class="text-sm text-gray-500 dark:text-gray-400">`;
  if (selectedAccount) {
    $$payload.out += "<!--[-->";
    $$payload.out += `Add a new contact to ${escape_html(selectedAccount.name)}`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `Add a new contact to your CRM`;
  }
  $$payload.out += `<!--]--></p></div></div></div></div></div> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">`;
  if (errors.general) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"><p class="text-red-800 dark:text-red-200">${escape_html(errors.general)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (selectedAccount) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"><div class="flex items-center">`;
    Building($$payload, { class: "w-5 h-5 text-blue-500 mr-2" });
    $$payload.out += `<!----> <p class="text-blue-800 dark:text-blue-200">This contact will be added to <strong>${escape_html(selectedAccount.name)}</strong></p></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" action="?/create" class="space-y-8">`;
  if (accountId) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<input type="hidden" name="accountId"${attr("value", accountId)}>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center">`;
  User($$payload, { class: "w-5 h-5 text-blue-500 mr-2" });
  $$payload.out += `<!----> <h2 class="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h2></div></div> <div class="p-6 space-y-6">`;
  if (!accountId) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(data.organizations);
    $$payload.out += `<div><label for="organizationId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Organization</label> <select id="organizationId" name="organizationId" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"><option value="">Select an organization</option><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let org = each_array[$$index];
      $$payload.out += `<option${attr("value", org.id)}>${escape_html(org.name)}</option>`;
    }
    $$payload.out += `<!--]--></select></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (!accountId && data.accounts?.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_1 = ensure_array_like(data.accounts);
    $$payload.out += `<div><label for="accountSelect" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Account (Optional)</label> <select id="accountSelect" name="accountId" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"><option value="">Select an account (optional)</option><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let account = each_array_1[$$index_1];
      $$payload.out += `<option${attr("value", account.id)}>${escape_html(account.name)}</option>`;
    }
    $$payload.out += `<!--]--></select></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="firstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</label> <input type="text" id="firstName" name="firstName"${attr("value", formValues.firstName)} required${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${stringify(errors.firstName ? "border-red-300 dark:border-red-600" : "")}`)} placeholder="Enter first name"> `;
  if (errors.firstName) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(errors.firstName)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="lastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name *</label> <input type="text" id="lastName" name="lastName"${attr("value", formValues.lastName)} required${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${stringify(errors.lastName ? "border-red-300 dark:border-red-600" : "")}`)} placeholder="Enter last name"> `;
  if (errors.lastName) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(errors.lastName)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Mail($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Email</label> <input type="email" id="email" name="email"${attr("value", formValues.email)}${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${stringify(errors.email ? "border-red-300 dark:border-red-600" : "")}`)} placeholder="<EMAIL>"> `;
  if (errors.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(errors.email)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Phone($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Phone</label> <input type="tel" id="phone" name="phone"${attr("value", formValues.phone)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="+****************"></div></div></div></div> `;
  if (selectedAccount || formValues.accountId) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center">`;
    Building($$payload, { class: "w-5 h-5 text-blue-500 mr-2" });
    $$payload.out += `<!----> <h2 class="text-lg font-medium text-gray-900 dark:text-white">Account Relationship</h2></div></div> <div class="p-6 space-y-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role</label> <input type="text" id="role" name="role"${attr("value", formValues.role)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Decision Maker, Influencer"></div> <div class="flex items-center pt-8"><input type="checkbox" id="isPrimary" name="isPrimary"${attr("checked", formValues.isPrimary, true)} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"> <label for="isPrimary" class="ml-2 block text-sm text-gray-900 dark:text-white">Primary Contact</label></div></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center">`;
  Building($$payload, { class: "w-5 h-5 text-blue-500 mr-2" });
  $$payload.out += `<!----> <h2 class="text-lg font-medium text-gray-900 dark:text-white">Professional Information</h2></div></div> <div class="p-6 space-y-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Job Title</label> <input type="text" id="title" name="title"${attr("value", formValues.title)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Sales Manager"></div> <div><label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label> <input type="text" id="department" name="department"${attr("value", formValues.department)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Sales"></div></div></div></div> <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center">`;
  Map_pin($$payload, { class: "w-5 h-5 text-blue-500 mr-2" });
  $$payload.out += `<!----> <h2 class="text-lg font-medium text-gray-900 dark:text-white">Address Information</h2></div></div> <div class="p-6 space-y-6"><div><label for="street" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Street Address</label> <input type="text" id="street" name="street"${attr("value", formValues.street)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="123 Main Street"></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div><label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City</label> <input type="text" id="city" name="city"${attr("value", formValues.city)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="New York"></div> <div><label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">State/Province</label> <input type="text" id="state" name="state"${attr("value", formValues.state)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="NY"></div> <div><label for="postalCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Postal Code</label> <input type="text" id="postalCode" name="postalCode"${attr("value", formValues.postalCode)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="10001"></div> <div><label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country</label> <input type="text" id="country" name="country"${attr("value", formValues.country)} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="United States"></div></div></div></div> <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center">`;
  File_text($$payload, { class: "w-5 h-5 text-blue-500 mr-2" });
  $$payload.out += `<!----> <h2 class="text-lg font-medium text-gray-900 dark:text-white">Additional Information</h2></div></div> <div class="p-6"><div><label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label> <textarea id="description" name="description" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Add any additional notes or description about this contact...">`;
  const $$body = escape_html(formValues.description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div></div></div> <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700"><a${attr("href", accountId ? `/app/accounts/${accountId}` : "/app/contacts")} class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">Cancel</a> <button type="submit"${attr("disabled", isSubmitting, true)} class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">`;
  {
    $$payload.out += "<!--[!-->";
    Save($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> Create Contact`;
  }
  $$payload.out += `<!--]--></button></div></form></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
