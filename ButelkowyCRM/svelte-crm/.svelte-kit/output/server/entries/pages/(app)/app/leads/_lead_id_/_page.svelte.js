import { E as spread_props, C as pop, A as push, D as escape_html, I as attr_class, R as stringify, J as attr, G as ensure_array_like, M as bind_props } from "../../../../../../chunks/index2.js";
import { i as invalidateAll } from "../../../../../../chunks/client.js";
import { C as Circle_check_big } from "../../../../../../chunks/circle-check-big.js";
import { X } from "../../../../../../chunks/x.js";
import { C as Chevron_right } from "../../../../../../chunks/chevron-right.js";
import { B as Building } from "../../../../../../chunks/building.js";
import { B as Briefcase } from "../../../../../../chunks/briefcase.js";
import { M as Mail } from "../../../../../../chunks/mail.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { P as Phone } from "../../../../../../chunks/phone.js";
import { S as Send } from "../../../../../../chunks/send.js";
import { P as Pen_line } from "../../../../../../chunks/pen-line.js";
import { A as Activity } from "../../../../../../chunks/activity.js";
import { T as Target } from "../../../../../../chunks/target.js";
import { A as Award } from "../../../../../../chunks/award.js";
import { S as Star } from "../../../../../../chunks/star.js";
import { D as Dollar_sign } from "../../../../../../chunks/dollar-sign.js";
import { U as User } from "../../../../../../chunks/user.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { M as Map_pin } from "../../../../../../chunks/map-pin.js";
import { M as Message_square } from "../../../../../../chunks/message-square.js";
import { P as Plus } from "../../../../../../chunks/plus.js";
import { U as Users } from "../../../../../../chunks/users.js";
import { E as External_link } from "../../../../../../chunks/external-link.js";
import { T as Trending_up } from "../../../../../../chunks/trending-up.js";
import { h as html } from "../../../../../../chunks/html.js";
function Copy($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "rect",
      {
        "width": "14",
        "height": "14",
        "x": "8",
        "y": "8",
        "rx": "2",
        "ry": "2"
      }
    ],
    [
      "path",
      {
        "d": "M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "copy" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Loader_circle($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "path",
      { "d": "M21 12a9 9 0 1 1-6.219-8.56" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "loader-circle" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let form = $$props["form"];
  const { lead } = data;
  let newComment = "";
  let isSubmittingComment = false;
  let isConverting = false;
  let showToast = false;
  let toastMessage = "";
  let toastType = "success";
  function getFullName(lead2) {
    return `${lead2.firstName} ${lead2.lastName}`.trim();
  }
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  function formatDateShort(dateString) {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  function getStatusColor(status) {
    switch (status) {
      case "NEW":
        return "bg-blue-50 text-blue-700 border-blue-200 ring-blue-600/20";
      case "PENDING":
        return "bg-amber-50 text-amber-700 border-amber-200 ring-amber-600/20";
      case "CONTACTED":
        return "bg-emerald-50 text-emerald-700 border-emerald-200 ring-emerald-600/20";
      case "QUALIFIED":
        return "bg-purple-50 text-purple-700 border-purple-200 ring-purple-600/20";
      case "UNQUALIFIED":
        return "bg-red-50 text-red-700 border-red-200 ring-red-600/20";
      case "CONVERTED":
        return "bg-gray-50 text-gray-700 border-gray-200 ring-gray-600/20";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 ring-gray-600/20";
    }
  }
  function getLeadSourceDisplay(source) {
    if (!source) return "Unknown";
    return source.replace("_", " ").toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  }
  function getInitials(lead2) {
    const first = lead2.firstName?.[0] || "";
    const last = lead2.lastName?.[0] || "";
    return (first + last).toUpperCase();
  }
  if (form?.status === "success") {
    toastMessage = form.message || "Action completed successfully!";
    toastType = "success";
    showToast = true;
    invalidateAll();
    isConverting = false;
    isSubmittingComment = false;
    if (form.commentAdded) {
      newComment = "";
    }
  } else if (form?.status === "error") {
    toastMessage = form.message || "An error occurred.";
    toastType = "error";
    showToast = true;
    isConverting = false;
    isSubmittingComment = false;
  }
  if (showToast) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="fixed top-4 right-4 z-50 max-w-md"><div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-4 flex items-center gap-3"><div class="flex-shrink-0">`;
    if (toastType === "success") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">`;
      Circle_check_big($$payload, {
        class: "w-4 h-4 text-green-600 dark:text-green-400"
      });
      $$payload.out += `<!----></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">`;
      X($$payload, {
        class: "w-4 h-4 text-red-600 dark:text-red-400"
      });
      $$payload.out += `<!----></div>`;
    }
    $$payload.out += `<!--]--></div> <p class="text-sm text-gray-900 dark:text-gray-100 font-medium flex-1">${escape_html(toastMessage)}</p> <button class="flex-shrink-0 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">`;
    X($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----></button></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="min-h-screen bg-gray-50 dark:bg-gray-900"><main class="container mx-auto px-4 py-8 max-w-7xl"><nav aria-label="breadcrumb" class="mb-8"><ol class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400"><li><a href="/app/leads" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium">Leads</a></li> <li>`;
  Chevron_right($$payload, {
    class: "h-4 w-4 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----></li> <li class="font-medium text-gray-900 dark:text-gray-100 truncate max-w-xs">${escape_html(getFullName(lead))}</li></ol></nav> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-2 space-y-8"><div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden"><div class="p-8"><div class="flex flex-col sm:flex-row items-start gap-6"><div class="flex-shrink-0"><div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-2xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">${escape_html(getInitials(lead))}</span></div></div> <div class="flex-1 min-w-0"><h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-3">${escape_html(getFullName(lead))}</h1> <div class="flex flex-wrap items-center gap-3 mb-4"><span${attr_class(`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${stringify(getStatusColor(lead.status))} ring-1 ring-inset`)}>${escape_html(lead.status)}</span> `;
  if (lead.company) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-2 text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 px-3 py-1 rounded-full">`;
    Building($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> <span class="font-medium">${escape_html(lead.company)}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.title) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-2 text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 px-3 py-1 rounded-full">`;
    Briefcase($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> <span>${escape_html(lead.title)}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">`;
  if (lead.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-xl group hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"><div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">`;
    Mail($$payload, {
      class: "w-5 h-5 text-blue-600 dark:text-blue-400"
    });
    $$payload.out += `<!----></div> <div class="flex-1 min-w-0"><p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Email</p> <a${attr("href", `mailto:${stringify(lead.email)}`)} class="text-sm text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium truncate block">${escape_html(lead.email)}</a></div> <button class="opacity-0 group-hover:opacity-100 p-1 hover:bg-white dark:hover:bg-gray-500 rounded-lg transition-all">`;
    Copy($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----></button></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.phone) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-xl group hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"><div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">`;
    Phone($$payload, {
      class: "w-5 h-5 text-green-600 dark:text-green-400"
    });
    $$payload.out += `<!----></div> <div class="flex-1 min-w-0"><p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Phone</p> <a${attr("href", `tel:${stringify(lead.phone)}`)} class="text-sm text-gray-900 dark:text-gray-100 hover:text-green-600 dark:hover:text-green-400 transition-colors font-medium">${escape_html(lead.phone)}</a></div> <button class="opacity-0 group-hover:opacity-100 p-1 hover:bg-white dark:hover:bg-gray-500 rounded-lg transition-all">`;
    Copy($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----></button></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="flex flex-wrap gap-3">`;
  if (lead.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `mailto:${stringify(lead.email)}`)} class="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-sm font-medium rounded-xl hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">`;
    Send($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Send Email</a>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.phone) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a${attr("href", `tel:${stringify(lead.phone)}`)} class="inline-flex items-center gap-2 px-4 py-2 bg-green-50 dark:bg-green-900 text-green-700 dark:text-green-300 text-sm font-medium rounded-xl hover:bg-green-100 dark:hover:bg-green-800 transition-colors">`;
    Phone($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Call</a>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="flex flex-col gap-3">`;
  if (lead.status !== "CONVERTED") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<form method="POST" action="?/convert"><button type="submit"${attr("disabled", isConverting, true)} class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 text-white text-sm font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-800 dark:hover:to-blue-900 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg hover:shadow-xl">`;
    if (isConverting) {
      $$payload.out += "<!--[-->";
      Loader_circle($$payload, { class: "w-4 h-4 animate-spin" });
      $$payload.out += `<!----> Converting...`;
    } else {
      $$payload.out += "<!--[!-->";
      Circle_check_big($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> Convert Lead`;
    }
    $$payload.out += `<!--]--></button></form>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <a${attr("href", `/app/leads/${stringify(lead.id)}/edit`)} class="inline-flex items-center gap-2 px-6 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 text-sm font-semibold rounded-xl hover:bg-gray-50 dark:hover:bg-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-all shadow-sm">`;
  Pen_line($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Edit Lead</a></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden"><div class="p-8"><h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-3"><div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">`;
  Activity($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> Lead Information</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">`;
  if (lead.leadSource) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="space-y-2"><div class="flex items-center gap-2">`;
    Target($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <label class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Lead Source</label></div> <p class="text-sm text-gray-900 dark:text-gray-100 font-medium bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg">${escape_html(getLeadSourceDisplay(lead.leadSource))}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.industry) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="space-y-2"><div class="flex items-center gap-2">`;
    Briefcase($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <label class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Industry</label></div> <p class="text-sm text-gray-900 dark:text-gray-100 font-medium bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg capitalize">${escape_html(lead.industry)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.rating) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(Array(parseInt(lead.rating) || 0));
    const each_array_1 = ensure_array_like(Array(5 - (parseInt(lead.rating) || 0)));
    $$payload.out += `<div class="space-y-2"><div class="flex items-center gap-2">`;
    Award($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <label class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Rating</label></div> <div class="flex items-center gap-2 bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg"><!--[-->`;
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      each_array[i];
      Star($$payload, { class: "w-4 h-4 text-yellow-400 fill-current" });
    }
    $$payload.out += `<!--]--> <!--[-->`;
    for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
      each_array_1[i];
      Star($$payload, {
        class: "w-4 h-4 text-gray-300 dark:text-gray-600 fill-current"
      });
    }
    $$payload.out += `<!--]--> <span class="text-sm font-medium text-gray-700 dark:text-gray-300 ml-1">${escape_html(lead.rating)}/5</span></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.annualRevenue) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="space-y-2"><div class="flex items-center gap-2">`;
    Dollar_sign($$payload, {
      class: "w-4 h-4 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <label class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Annual Revenue</label></div> <p class="text-sm text-gray-900 dark:text-gray-100 font-medium bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg">$${escape_html(lead.annualRevenue.toLocaleString())}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="space-y-2"><div class="flex items-center gap-2">`;
  User($$payload, {
    class: "w-4 h-4 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----> <label class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Lead Owner</label></div> <p class="text-sm text-gray-900 dark:text-gray-100 font-medium bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg">${escape_html(lead.owner?.name || "Unassigned")}</p></div> <div class="space-y-2"><div class="flex items-center gap-2">`;
  Calendar($$payload, {
    class: "w-4 h-4 text-gray-400 dark:text-gray-500"
  });
  $$payload.out += `<!----> <label class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Created</label></div> <p class="text-sm text-gray-900 dark:text-gray-100 font-medium bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg">${escape_html(formatDateShort(lead.createdAt))}</p></div></div> `;
  if (lead.address) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700"><div class="flex items-center gap-2 mb-3">`;
    Map_pin($$payload, {
      class: "w-5 h-5 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <label class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Address</label></div> <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl"><p class="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-line leading-relaxed">${escape_html(lead.address)}</p></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700"><div class="flex items-center gap-2 mb-3">`;
    Message_square($$payload, {
      class: "w-5 h-5 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----> <label class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Description</label></div> <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl"><div class="prose prose-sm max-w-none text-gray-700 dark:text-gray-300">${html(lead.description)}</div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (lead.isConverted) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700"><div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-xl p-4"><div class="flex items-center gap-3 mb-2"><div class="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-lg flex items-center justify-center">`;
    Circle_check_big($$payload, {
      class: "w-5 h-5 text-green-600 dark:text-green-400"
    });
    $$payload.out += `<!----></div> <h3 class="text-sm font-semibold text-green-800 dark:text-green-300">Lead Converted</h3></div> `;
    if (lead.convertedAt) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="text-sm text-green-700 dark:text-green-400">Converted on ${escape_html(formatDate(lead.convertedAt))}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div> <div class="space-y-8"><div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center gap-3"><div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">`;
  Message_square($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> Activity &amp; Notes</h2></div> <div class="p-6"><form method="POST" action="?/addComment" class="mb-6"><div class="space-y-4"><textarea name="comment" placeholder="Add a note or log activity..." rows="4" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none">`;
  const $$body = escape_html(newComment);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea> <div class="flex justify-end"><button type="submit"${attr("disabled", !newComment.trim() || isSubmittingComment, true)} class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white text-sm font-semibold rounded-xl hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all">`;
  if (isSubmittingComment) {
    $$payload.out += "<!--[-->";
    Loader_circle($$payload, { class: "w-4 h-4 animate-spin" });
    $$payload.out += `<!----> Adding...`;
  } else {
    $$payload.out += "<!--[!-->";
    Plus($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Add Note`;
  }
  $$payload.out += `<!--]--></button></div></div></form> <div class="space-y-4">`;
  if (lead.comments && lead.comments.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_2 = ensure_array_like(lead.comments);
    $$payload.out += `<!--[-->`;
    for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
      let comment = each_array_2[i];
      $$payload.out += `<div class="flex gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl"><div class="flex-shrink-0"><div class="w-10 h-10 rounded-xl bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 flex items-center justify-center">`;
      User($$payload, {
        class: "w-5 h-5 text-gray-600 dark:text-gray-400"
      });
      $$payload.out += `<!----></div></div> <div class="flex-1 min-w-0"><div class="flex items-center gap-3 mb-2"><p class="text-sm font-semibold text-gray-900 dark:text-gray-100">${escape_html(comment.author?.name || "Unknown User")}</p> <p class="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-600 px-2 py-1 rounded-md">${escape_html(formatDate(comment.createdAt))}</p></div> <p class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line leading-relaxed">${escape_html(comment.body)}</p></div></div>`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="text-center py-12"><div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-2xl flex items-center justify-center">`;
    Message_square($$payload, {
      class: "w-8 h-8 text-gray-400 dark:text-gray-500"
    });
    $$payload.out += `<!----></div> <p class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-1">No activity yet</p> <p class="text-xs text-gray-500 dark:text-gray-400">Be the first to add a note or log an interaction.</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></div> `;
  if (lead.convertedContactId && lead.contact) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center gap-3"><div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">`;
    Users($$payload, {
      class: "w-5 h-5 text-green-600 dark:text-green-400"
    });
    $$payload.out += `<!----></div> Related Contact</h2></div> <div class="p-6"><div class="space-y-4"><div class="flex items-center gap-3"><div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 rounded-xl flex items-center justify-center">`;
    User($$payload, { class: "w-6 h-6 text-white" });
    $$payload.out += `<!----></div> <div><p class="font-semibold text-gray-900 dark:text-gray-100">${escape_html(lead.contact.firstName)} ${escape_html(lead.contact.lastName)}</p> <p class="text-xs text-gray-500 dark:text-gray-400">Contact</p></div></div> `;
    if (lead.contact.email) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">`;
      Mail($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> <span>${escape_html(lead.contact.email)}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (lead.contact.phone) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">`;
      Phone($$payload, { class: "w-4 h-4" });
      $$payload.out += `<!----> <span>${escape_html(lead.contact.phone)}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <a${attr("href", `/app/contacts/${stringify(lead.contact.id)}`)} class="inline-flex items-center justify-center w-full px-4 py-3 bg-green-50 dark:bg-green-900 text-green-700 dark:text-green-300 text-sm font-semibold rounded-xl hover:bg-green-100 dark:hover:bg-green-800 transition-colors">`;
    External_link($$payload, { class: "w-4 h-4 mr-2" });
    $$payload.out += `<!----> View Contact</a></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden"><div class="p-6 border-b border-gray-200 dark:border-gray-700"><h2 class="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center gap-3"><div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">`;
  Trending_up($$payload, {
    class: "w-5 h-5 text-purple-600 dark:text-purple-400"
  });
  $$payload.out += `<!----></div> Quick Stats</h2></div> <div class="p-6 space-y-4"><div class="flex justify-between items-center"><span class="text-sm text-gray-600 dark:text-gray-300">Comments</span> <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">${escape_html(lead.comments?.length || 0)}</span></div> <div class="flex justify-between items-center"><span class="text-sm text-gray-600 dark:text-gray-300">Days Since Created</span> <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">${escape_html(Math.floor((/* @__PURE__ */ new Date() - new Date(lead.createdAt)) / (1e3 * 60 * 60 * 24)))}</span></div> `;
  if (lead.convertedAt) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex justify-between items-center"><span class="text-sm text-gray-600 dark:text-gray-300">Days to Convert</span> <span class="text-sm font-semibold text-green-600 dark:text-green-400">${escape_html(Math.floor((new Date(lead.convertedAt) - new Date(lead.createdAt)) / (1e3 * 60 * 60 * 24)))}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div></main></div>`;
  bind_props($$props, { data, form });
  pop();
}
export {
  _page as default
};
