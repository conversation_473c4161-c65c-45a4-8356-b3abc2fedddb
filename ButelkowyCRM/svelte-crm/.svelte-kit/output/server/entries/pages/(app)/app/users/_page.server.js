import { p as prisma } from "../../../../../chunks/prisma.js";
import { f as fail } from "../../../../../chunks/index.js";
async function load({ params, locals }) {
  const org_id = locals.org.id;
  const user = locals.user;
  const userOrg = await prisma.userOrganization.findFirst({
    where: {
      userId: user.id,
      organizationId: org_id,
      role: "ADMIN"
    }
  });
  console.log("User Organization:", userOrg);
  if (!userOrg) {
    return {
      error: {
        name: "You do not have permission to access this organization"
      }
    };
  }
  const organization = await prisma.organization.findUnique({
    where: {
      id: org_id
      // Changed from params.org_id
    }
  });
  const users = await prisma.userOrganization.findMany({
    where: {
      organizationId: org_id
    },
    include: {
      user: true
    }
  });
  return { organization, users, user: { id: user.id } };
}
const actions = {
  update: async ({ request, params, locals }) => {
    const org_id = locals.org.id;
    const user = locals.user;
    const userOrg = await prisma.userOrganization.findFirst({
      where: {
        userId: user.id,
        organizationId: org_id,
        role: "ADMIN"
      }
    });
    if (!userOrg) return fail(403, { error: "Forbidden" });
    const formData = await request.formData();
    const name = formData.get("name")?.toString().trim();
    const domain = formData.get("domain")?.toString().trim();
    const description = formData.get("description")?.toString().trim();
    if (!name) return fail(400, { error: "Name is required" });
    try {
      await prisma.organization.update({
        where: { id: org_id },
        data: {
          name,
          domain,
          description
        }
      });
      if (name) {
        if (locals.org) {
          locals.org.name = name;
        }
        locals.org_name = name;
      }
      return { success: true };
    } catch (err) {
      return fail(500, { error: "Failed to update organization" });
    }
  },
  add_user: async ({ request, params, locals }) => {
    const org_id = locals.org.id;
    const user = locals.user;
    const userOrg = await prisma.userOrganization.findFirst({
      where: {
        userId: user.id,
        organizationId: org_id,
        role: "ADMIN"
      }
    });
    if (!userOrg) return fail(403, { error: "Forbidden" });
    const formData = await request.formData();
    const email = formData.get("email")?.toString().trim().toLowerCase();
    const role = formData.get("role")?.toString();
    if (!email || !role) return fail(400, { error: "Email and role are required" });
    const foundUser = await prisma.user.findUnique({ where: { email } });
    if (!foundUser) return fail(404, { error: "No user found with that email" });
    const already = await prisma.userOrganization.findFirst({
      where: { userId: foundUser.id, organizationId: org_id }
    });
    if (already) return fail(400, { error: "User already in organization" });
    await prisma.userOrganization.create({
      data: {
        userId: foundUser.id,
        organizationId: org_id,
        role
      }
    });
    return { success: true };
  },
  edit_role: async ({ request, params, locals }) => {
    const org_id = locals.org.id;
    const user = locals.user;
    const userOrg = await prisma.userOrganization.findFirst({
      where: {
        userId: user.id,
        organizationId: org_id,
        role: "ADMIN"
      }
    });
    if (!userOrg) return fail(403, { error: "Forbidden" });
    const formData = await request.formData();
    const user_id = formData.get("user_id")?.toString();
    const role = formData.get("role")?.toString();
    if (!user_id || !role) return fail(400, { error: "User and role are required" });
    if (user_id === user.id) return fail(400, { error: "You cannot change your own role" });
    if (role !== "ADMIN") {
      const adminCount = await prisma.userOrganization.count({
        where: {
          organizationId: org_id,
          role: "ADMIN"
        }
      });
      const target = await prisma.userOrganization.findUnique({
        where: { userId_organizationId: { userId: user_id, organizationId: org_id } }
      });
      if (target && target.role === "ADMIN" && adminCount === 1) {
        return fail(400, { error: "Organization must have at least one admin" });
      }
    }
    await prisma.userOrganization.update({
      where: { userId_organizationId: { userId: user_id, organizationId: org_id } },
      data: { role }
    });
    return { success: true };
  },
  remove_user: async ({ request, params, locals }) => {
    const org_id = locals.org.id;
    const user = locals.user;
    const userOrg = await prisma.userOrganization.findFirst({
      where: {
        userId: user.id,
        organizationId: org_id,
        role: "ADMIN"
      }
    });
    if (!userOrg) return fail(403, { error: "Forbidden" });
    const formData = await request.formData();
    const user_id = formData.get("user_id")?.toString();
    if (!user_id) return fail(400, { error: "User is required" });
    if (user_id === user.id) return fail(400, { error: "You cannot remove yourself" });
    const target = await prisma.userOrganization.findUnique({
      where: { userId_organizationId: { userId: user_id, organizationId: org_id } }
    });
    if (target && target.role === "ADMIN") {
      const adminCount = await prisma.userOrganization.count({
        where: {
          organizationId: org_id,
          role: "ADMIN"
        }
      });
      if (adminCount === 1) {
        return fail(400, { error: "Organization must have at least one admin" });
      }
    }
    await prisma.userOrganization.delete({
      where: { userId_organizationId: { userId: user_id, organizationId: org_id } }
    });
    return { success: true };
  }
};
export {
  actions,
  load
};
