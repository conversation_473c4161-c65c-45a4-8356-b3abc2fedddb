import { G as ensure_array_like, D as escape_html, J as attr, I as attr_class, M as bind_props, C as pop, A as push, R as stringify } from "../../../../../../../chunks/index2.js";
import "../../../../../../../chunks/client.js";
import { A as Arrow_left } from "../../../../../../../chunks/arrow-left.js";
import { U as User } from "../../../../../../../chunks/user.js";
import { M as Mail } from "../../../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../../../chunks/phone.js";
import { B as Building } from "../../../../../../../chunks/building.js";
import { T as Target } from "../../../../../../../chunks/target.js";
import { S as Star } from "../../../../../../../chunks/star.js";
import { D as Dollar_sign } from "../../../../../../../chunks/dollar-sign.js";
import { X } from "../../../../../../../chunks/x.js";
import { S as Save } from "../../../../../../../chunks/save.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let { lead, users } = data;
  let isSubmitting = false;
  let errors = {};
  const statusOptions = [
    {
      value: "NEW",
      name: "New",
      color: "bg-blue-100 text-blue-800"
    },
    {
      value: "PENDING",
      name: "Pending",
      color: "bg-yellow-100 text-yellow-800"
    },
    {
      value: "CONTACTED",
      name: "Contacted",
      color: "bg-purple-100 text-purple-800"
    },
    {
      value: "QUALIFIED",
      name: "Qualified",
      color: "bg-green-100 text-green-800"
    },
    {
      value: "UNQUALIFIED",
      name: "Unqualified",
      color: "bg-red-100 text-red-800"
    },
    {
      value: "CONVERTED",
      name: "Converted",
      color: "bg-emerald-100 text-emerald-800"
    }
  ];
  const sourceOptions = [
    { value: "WEB", name: "Website" },
    { value: "PHONE_INQUIRY", name: "Phone Inquiry" },
    {
      value: "PARTNER_REFERRAL",
      name: "Partner Referral"
    },
    { value: "COLD_CALL", name: "Cold Call" },
    { value: "TRADE_SHOW", name: "Trade Show" },
    {
      value: "EMPLOYEE_REFERRAL",
      name: "Employee Referral"
    },
    { value: "ADVERTISEMENT", name: "Advertisement" },
    { value: "OTHER", name: "Other" }
  ];
  const ratingOptions = [
    {
      value: "Hot",
      name: "Hot 🔥",
      color: "text-red-600"
    },
    {
      value: "Warm",
      name: "Warm 🌡️",
      color: "text-orange-600"
    },
    {
      value: "Cold",
      name: "Cold ❄️",
      color: "text-blue-600"
    }
  ];
  const industryOptions = [
    { value: "Technology", name: "Technology" },
    { value: "Finance", name: "Finance" },
    { value: "Healthcare", name: "Healthcare" },
    { value: "Education", name: "Education" },
    { value: "Manufacturing", name: "Manufacturing" },
    { value: "Retail", name: "Retail" },
    { value: "Real Estate", name: "Real Estate" },
    { value: "Consulting", name: "Consulting" },
    { value: "Marketing", name: "Marketing" },
    { value: "Legal", name: "Legal" },
    { value: "Construction", name: "Construction" },
    {
      value: "Transportation",
      name: "Transportation"
    },
    { value: "Hospitality", name: "Hospitality" },
    { value: "Entertainment", name: "Entertainment" },
    { value: "Other", name: "Other" }
  ];
  const each_array = ensure_array_like(industryOptions);
  const each_array_1 = ensure_array_like(statusOptions);
  const each_array_2 = ensure_array_like(sourceOptions);
  const each_array_3 = ensure_array_like(ratingOptions);
  const each_array_4 = ensure_array_like(users);
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><header class="sticky top-0 z-20 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16"><div class="flex items-center space-x-4"><button class="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">`;
  Arrow_left($$payload, {
    class: "w-5 h-5 text-gray-600 dark:text-gray-300"
  });
  $$payload.out += `<!----></button> <div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Lead</h1> <p class="text-sm text-gray-500 dark:text-gray-400">Editing ${escape_html(lead.firstName)} ${escape_html(lead.lastName)}</p></div></div></div></div></header> <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">`;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <form method="POST" class="space-y-8"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"><div class="flex items-center space-x-2">`;
  User($$payload, {
    class: "w-5 h-5 text-gray-600 dark:text-gray-300"
  });
  $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Personal Information</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="firstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name <span class="text-red-500 dark:text-red-400">*</span></label> <input id="firstName" name="firstName" type="text" required${attr("value", lead.firstName)}${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all ${stringify(errors.firstName ? "border-red-500 dark:border-red-400 ring-2 ring-red-200 dark:ring-red-800" : "")}`)} placeholder="Enter first name"> `;
  if (errors.firstName) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(errors.firstName)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="lastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name <span class="text-red-500 dark:text-red-400">*</span></label> <input id="lastName" name="lastName" type="text" required${attr("value", lead.lastName)}${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all ${stringify(errors.lastName ? "border-red-500 dark:border-red-400 ring-2 ring-red-200 dark:ring-red-800" : "")}`)} placeholder="Enter last name"> `;
  if (errors.lastName) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(errors.lastName)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Mail($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Email Address</label> <input id="email" name="email" type="email"${attr("value", lead.email || "")}${attr_class(`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all ${stringify(errors.email ? "border-red-500 dark:border-red-400 ring-2 ring-red-200 dark:ring-red-800" : "")}`)} placeholder="<EMAIL>"> `;
  if (errors.email) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-1 text-sm text-red-600 dark:text-red-400">${escape_html(errors.email)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Phone($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Phone Number</label> <input id="phone" name="phone" type="tel"${attr("value", lead.phone || "")} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all" placeholder="+****************"></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"><div class="flex items-center space-x-2">`;
  Building($$payload, {
    class: "w-5 h-5 text-gray-600 dark:text-gray-300"
  });
  $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Company Information</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Company Name</label> <input id="company" name="company" type="text"${attr("value", lead.company || "")} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all" placeholder="Enter company name"></div> <div><label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Job Title</label> <input id="title" name="title" type="text"${attr("value", lead.title || "")} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all" placeholder="Enter job title"></div> <div class="md:col-span-2"><label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Industry</label> <select id="industry" name="industry" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all"><option value="">Select Industry</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let option = each_array[$$index];
    $$payload.out += `<option${attr("value", option.value)}${attr("selected", lead.industry === option.value, true)}>${escape_html(option.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"><div class="flex items-center space-x-2">`;
  Target($$payload, {
    class: "w-5 h-5 text-gray-600 dark:text-gray-300"
  });
  $$payload.out += `<!----> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Lead Details</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"><div><label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label> <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let option = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", option.value)}${attr("selected", lead.status === option.value, true)}>${escape_html(option.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="leadSource" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Lead Source</label> <select id="leadSource" name="leadSource" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all"><option value="">Select Source</option><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let option = each_array_2[$$index_2];
    $$payload.out += `<option${attr("value", option.value)}${attr("selected", lead.leadSource === option.value, true)}>${escape_html(option.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Star($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Rating</label> <select id="rating" name="rating" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all"><option value="">Select Rating</option><!--[-->`;
  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
    let option = each_array_3[$$index_3];
    $$payload.out += `<option${attr("value", option.value)}${attr("selected", lead.rating === option.value, true)}>${escape_html(option.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6"><div><label for="ownerId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Lead Owner</label> <select id="ownerId" name="ownerId" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all"><!--[-->`;
  for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
    let user = each_array_4[$$index_4];
    $$payload.out += `<option${attr("value", user.id)}${attr("selected", lead.ownerId === user.id, true)}>${escape_html(user.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <div><label for="annualRevenue" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">`;
  Dollar_sign($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Annual Revenue (Optional)</label> <input id="annualRevenue" name="annualRevenue" type="number" step="0.01"${attr("value", lead.annualRevenue || "")} class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all" placeholder="Enter annual revenue"></div></div> <div><label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description / Notes</label> <textarea id="description" name="description" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all resize-vertical" placeholder="Add notes, requirements, or any additional information about this lead...">`;
  const $$body = escape_html(lead.description || "");
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div></div></div> <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700"><button type="button" class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all">`;
  X($$payload, { class: "w-4 h-4 mr-2" });
  $$payload.out += `<!----> Cancel</button> <button type="submit"${attr("disabled", isSubmitting, true)} class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-blue-500 dark:focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all">`;
  {
    $$payload.out += "<!--[!-->";
    Save($$payload, { class: "w-4 h-4 mr-2" });
  }
  $$payload.out += `<!--]--> Save Changes</button></div></form></main></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
