import { E as spread_props, C as pop, A as push, G as ensure_array_like, P as head, D as escape_html, J as attr, I as attr_class, R as stringify } from "../../../../../chunks/index2.js";
import "../../../../../chunks/client.js";
import { P as Plus } from "../../../../../chunks/plus.js";
import { T as Target } from "../../../../../chunks/target.js";
import { D as Dollar_sign } from "../../../../../chunks/dollar-sign.js";
import { T as Trending_up } from "../../../../../chunks/trending-up.js";
import { C as Circle_check_big } from "../../../../../chunks/circle-check-big.js";
import { S as Search } from "../../../../../chunks/search.js";
import { F as Funnel } from "../../../../../chunks/funnel.js";
import { I as Icon } from "../../../../../chunks/Icon.js";
import { C as Circle_x } from "../../../../../chunks/circle-x.js";
import { U as Users } from "../../../../../chunks/users.js";
import { S as Square_pen } from "../../../../../chunks/square-pen.js";
import { B as Building_2 } from "../../../../../chunks/building-2.js";
import { C as Calendar } from "../../../../../chunks/calendar.js";
import { U as User } from "../../../../../chunks/user.js";
import { C as Clock } from "../../../../../chunks/clock.js";
import { E as Eye } from "../../../../../chunks/eye.js";
import { T as Trash_2 } from "../../../../../chunks/trash-2.js";
function Arrow_up_narrow_wide($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    ["path", { "d": "m3 8 4-4 4 4" }],
    ["path", { "d": "M7 4v16" }],
    ["path", { "d": "M11 12h4" }],
    ["path", { "d": "M11 16h7" }],
    ["path", { "d": "M11 20h10" }]
  ];
  Icon($$payload, spread_props([
    { name: "arrow-up-narrow-wide" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let searchTerm = "";
  const stageConfig = {
    PROSPECTING: {
      label: "Prospecting",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      icon: Target
    },
    QUALIFICATION: {
      label: "Qualification",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      icon: Search
    },
    PROPOSAL: {
      label: "Proposal",
      color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      icon: Square_pen
    },
    NEGOTIATION: {
      label: "Negotiation",
      color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
      icon: Users
    },
    CLOSED_WON: {
      label: "Closed Won",
      color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      icon: Circle_check_big
    },
    CLOSED_LOST: {
      label: "Closed Lost",
      color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      icon: Circle_x
    }
  };
  function getFilteredOpportunities() {
    if (!data?.opportunities || !Array.isArray(data.opportunities)) {
      return [];
    }
    let filtered = [...data.opportunities];
    return filtered;
  }
  const filteredOpportunities = getFilteredOpportunities();
  function formatCurrency(amount) {
    if (!amount) return "-";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }
  function formatDate(date) {
    if (!date) return "-";
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric"
    });
  }
  const each_array = ensure_array_like(Object.entries(stageConfig));
  const each_array_1 = ensure_array_like(filteredOpportunities);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Opportunities - BottleCRM</title>`;
  });
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900"><div class="bg-white dark:bg-gray-800 shadow"><div class="px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16"><div class="flex items-center"><h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Opportunities</h1></div> <div class="flex items-center space-x-4"><a href="/app/opportunities/new" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">`;
  Plus($$payload, { class: "mr-2 h-4 w-4" });
  $$payload.out += `<!----> New Opportunity</a></div></div></div></div> <div class="px-4 sm:px-6 lg:px-8 py-6"><div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8"><div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0">`;
  Target($$payload, { class: "h-6 w-6 text-gray-400" });
  $$payload.out += `<!----></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Opportunities</dt> <dd class="text-lg font-medium text-gray-900 dark:text-white">${escape_html(data.stats.total)}</dd></dl></div></div></div></div> <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0">`;
  Dollar_sign($$payload, { class: "h-6 w-6 text-gray-400" });
  $$payload.out += `<!----></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Value</dt> <dd class="text-lg font-medium text-gray-900 dark:text-white">${escape_html(formatCurrency(data.stats.totalValue))}</dd></dl></div></div></div></div> <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0">`;
  Trending_up($$payload, { class: "h-6 w-6 text-green-400" });
  $$payload.out += `<!----></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Pipeline Value</dt> <dd class="text-lg font-medium text-gray-900 dark:text-white">${escape_html(formatCurrency(data.stats.pipeline))}</dd></dl></div></div></div></div> <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"><div class="p-5"><div class="flex items-center"><div class="flex-shrink-0">`;
  Circle_check_big($$payload, { class: "h-6 w-6 text-green-400" });
  $$payload.out += `<!----></div> <div class="ml-5 w-0 flex-1"><dl><dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Won Value</dt> <dd class="text-lg font-medium text-gray-900 dark:text-white">${escape_html(formatCurrency(data.stats.wonValue))}</dd></dl></div></div></div></div></div> <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6"><div class="p-6"><div class="flex flex-col sm:flex-row gap-4"><div class="flex-1"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">`;
  Search($$payload, { class: "h-5 w-5 text-gray-400" });
  $$payload.out += `<!----></div> <input type="text"${attr("value", searchTerm)} placeholder="Search opportunities, accounts, or owners..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"></div></div> <div class="sm:w-48"><select class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"><option value="all">All Stages</option><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let [stage, config] = each_array[$$index];
    $$payload.out += `<option${attr("value", stage)}>${escape_html(config.label)}</option>`;
  }
  $$payload.out += `<!--]--></select></div> <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">`;
  Funnel($$payload, { class: "mr-2 h-4 w-4" });
  $$payload.out += `<!----> Filters</button></div></div></div> <div class="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg"><div class="min-w-full overflow-x-auto"><table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"><thead class="bg-gray-50 dark:bg-gray-700"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"><div class="flex items-center space-x-1"><span>Opportunity</span> `;
  Arrow_up_narrow_wide($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----></div></th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"><div class="flex items-center space-x-1"><span>Account</span> `;
  Arrow_up_narrow_wide($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----></div></th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Stage</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"><div class="flex items-center space-x-1"><span>Amount</span> `;
  Arrow_up_narrow_wide($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----></div></th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"><div class="flex items-center space-x-1"><span>Close Date</span> `;
  Arrow_up_narrow_wide($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----></div></th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"><div class="flex items-center space-x-1"><span>Owner</span> `;
  Arrow_up_narrow_wide($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----></div></th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Activities</th><th class="relative px-6 py-3"><span class="sr-only">Actions</span></th></tr></thead><tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let opportunity = each_array_1[$$index_1];
    const config = stageConfig[opportunity.stage] || stageConfig.PROSPECTING;
    $$payload.out += `<tr class="hover:bg-gray-50 dark:hover:bg-gray-700"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div><div class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(opportunity.name || "Unnamed Opportunity")}</div> `;
    if (opportunity.type) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="text-sm text-gray-500 dark:text-gray-400">${escape_html(opportunity.type)}</div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div></td><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center">`;
    Building_2($$payload, { class: "h-4 w-4 text-gray-400 mr-2" });
    $$payload.out += `<!----> <div><div class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(opportunity.account?.name || "No Account")}</div> `;
    if (opportunity.account?.type) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="text-sm text-gray-500 dark:text-gray-400">${escape_html(opportunity.account.type)}</div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div></td><td class="px-6 py-4 whitespace-nowrap"><span${attr_class(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${stringify(config.color)}`)}><!---->`;
    config.icon?.($$payload, { class: "mr-1 h-3 w-3" });
    $$payload.out += `<!----> ${escape_html(config.label)}</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${escape_html(formatCurrency(opportunity.amount))} `;
    if (opportunity.probability) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="text-xs text-gray-500 dark:text-gray-400">${escape_html(opportunity.probability)}% probability</div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><div class="flex items-center">`;
    Calendar($$payload, { class: "h-4 w-4 text-gray-400 mr-2" });
    $$payload.out += `<!----> ${escape_html(formatDate(opportunity.closeDate))}</div></td><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center">`;
    User($$payload, { class: "h-4 w-4 text-gray-400 mr-2" });
    $$payload.out += `<!----> <div class="text-sm font-medium text-gray-900 dark:text-white">${escape_html(opportunity.owner?.name || opportunity.owner?.email || "No Owner")}</div></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><div class="flex items-center space-x-4">`;
    if (opportunity._count?.tasks > 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="flex items-center">`;
      Clock($$payload, { class: "h-4 w-4 mr-1" });
      $$payload.out += `<!----> ${escape_html(opportunity._count.tasks)}</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (opportunity._count?.events > 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="flex items-center">`;
      Calendar($$payload, { class: "h-4 w-4 mr-1" });
      $$payload.out += `<!----> ${escape_html(opportunity._count.events)}</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></td><td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"><div class="flex items-center justify-end space-x-2"><button type="button" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" title="View">`;
    Eye($$payload, { class: "h-4 w-4" });
    $$payload.out += `<!----></button> <a${attr("href", `/app/opportunities/${stringify(opportunity.id)}/edit`)} class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" title="Edit">`;
    Square_pen($$payload, { class: "h-4 w-4" });
    $$payload.out += `<!----></a> <button type="button" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="Delete">`;
    Trash_2($$payload, { class: "h-4 w-4" });
    $$payload.out += `<!----></button></div></td></tr>`;
  }
  $$payload.out += `<!--]--></tbody></table></div> `;
  if (filteredOpportunities.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-center py-12">`;
    Target($$payload, { class: "mx-auto h-12 w-12 text-gray-400" });
    $$payload.out += `<!----> <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No opportunities</h3> <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">${escape_html("Get started by creating a new opportunity.")}</p> `;
    {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="mt-6"><a href="/app/opportunities/new" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">`;
      Plus($$payload, { class: "mr-2 h-4 w-4" });
      $$payload.out += `<!----> New Opportunity</a></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div>`;
  pop();
}
export {
  _page as default
};
