import { G as ensure_array_like, D as escape_html, J as attr, M as bind_props, C as pop, A as push } from "../../../../../../../chunks/index2.js";
import "../../../../../../../chunks/client.js";
import { C as Circle_check_big } from "../../../../../../../chunks/circle-check-big.js";
import { X } from "../../../../../../../chunks/x.js";
import { C as Circle_alert } from "../../../../../../../chunks/circle-alert.js";
import { C as Calendar } from "../../../../../../../chunks/calendar.js";
import { U as User } from "../../../../../../../chunks/user.js";
import { B as Building } from "../../../../../../../chunks/building.js";
import { S as Save } from "../../../../../../../chunks/save.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let form = $$props["form"];
  let task = { ...data.task };
  task.dueDate = task.dueDate ? task.dueDate.split("T")[0] : "";
  const users = data.users;
  const accounts = data.accounts;
  const each_array = ensure_array_like(users);
  const each_array_1 = ensure_array_like(accounts);
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8"><div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"><div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 mb-6"><div class="px-6 py-4 border-b border-gray-100 dark:border-gray-700"><div class="flex items-center justify-between"><div class="flex items-center space-x-3"><div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">`;
  Circle_check_big($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-xl font-semibold text-gray-900 dark:text-white">Edit Task</h1> <p class="text-sm text-gray-500 dark:text-gray-400">Update task details and settings</p></div></div> <button type="button" class="w-10 h-10 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center justify-center transition-colors duration-200" aria-label="Close">`;
  X($$payload, {
    class: "w-5 h-5 text-gray-600 dark:text-gray-300"
  });
  $$payload.out += `<!----></button></div></div></div> <form method="POST" action="?/update" class="space-y-6">`;
  if (form?.message || form?.fieldError) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-red-200 dark:border-red-800"><div class="p-6"><div class="flex items-start space-x-3">`;
    Circle_alert($$payload, {
      class: "w-5 h-5 text-red-500 dark:text-red-400 mt-0.5 flex-shrink-0"
    });
    $$payload.out += `<!----> <div class="flex-1">`;
    if (form?.message) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="text-red-700 dark:text-red-300 font-medium">${escape_html(form.message)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (form?.fieldError) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="text-red-700 dark:text-red-300">Error with field '${escape_html(form.fieldError[0])}': ${escape_html(form.fieldError[1])}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700"><div class="p-6 space-y-6"><div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-subject">Subject <span class="text-red-500 dark:text-red-400">*</span></label> <input type="text" id="task-subject" name="subject" class="w-full h-12 px-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", task.subject)} required placeholder="Enter task subject"></div> <div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-description">Description</label> <textarea id="task-description" name="description" class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 resize-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400" rows="4" placeholder="Add task description...">`;
  const $$body = escape_html(task.description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-status">Status</label> <select id="task-status" name="status" class="w-full h-12 px-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-white"><option>Not Started</option><option>In Progress</option><option>Completed</option><option>Waiting on someone else</option><option>Deferred</option></select></div> <div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-priority">Priority</label> <select id="task-priority" name="priority" class="w-full h-12 px-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-white"><option>High</option><option>Normal</option><option>Low</option></select></div></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-duedate"><div class="flex items-center space-x-2">`;
  Calendar($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span>Due Date</span></div></label> <input type="date" id="task-duedate" name="dueDate" class="w-full h-12 px-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-white"${attr("value", task.dueDate)}></div> <div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-owner"><div class="flex items-center space-x-2">`;
  User($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span>Owner <span class="text-red-500 dark:text-red-400">*</span></span></div></label> <select id="task-owner" name="ownerId" class="w-full h-12 px-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-white" required><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let user = each_array[$$index];
    $$payload.out += `<option${attr("value", user.id)}>${escape_html(user.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div> <div class="space-y-2"><label class="block text-sm font-semibold text-gray-900 dark:text-white" for="task-account"><div class="flex items-center space-x-2">`;
  Building($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span>Account</span> <span class="text-xs text-gray-500 dark:text-gray-400 font-normal">(Optional)</span></div></label> <select id="task-account" name="accountId" class="w-full h-12 px-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:bg-white dark:focus:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-white"><option${attr("value", null)}>No account selected</option><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let acc = each_array_1[$$index_1];
    $$payload.out += `<option${attr("value", acc.id)}>${escape_html(acc.name)}</option>`;
  }
  $$payload.out += `<!--]--></select></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700"><div class="px-6 py-4"><div class="flex justify-end space-x-3"><button type="button" class="h-11 px-6 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-medium transition-colors duration-200 flex items-center space-x-2">`;
  X($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span>Cancel</span></button> <button type="submit" class="h-11 px-6 rounded-xl bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium transition-colors duration-200 flex items-center space-x-2 shadow-sm">`;
  Save($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span>Save Changes</span></button></div></div></div></form></div></div>`;
  bind_props($$props, { data, form });
  pop();
}
export {
  _page as default
};
