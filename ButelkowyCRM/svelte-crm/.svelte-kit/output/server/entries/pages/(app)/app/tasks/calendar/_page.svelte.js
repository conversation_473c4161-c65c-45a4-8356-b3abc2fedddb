import { G as ensure_array_like, D as escape_html, I as attr_class, R as stringify, J as attr, M as bind_props, C as pop, A as push } from "../../../../../../chunks/index2.js";
import { C as Calendar } from "../../../../../../chunks/calendar.js";
import { C as Chevron_left } from "../../../../../../chunks/chevron-left.js";
import { C as Chevron_right } from "../../../../../../chunks/chevron-right.js";
import { C as Circle } from "../../../../../../chunks/circle.js";
import { C as Clock } from "../../../../../../chunks/clock.js";
import { C as Circle_alert } from "../../../../../../chunks/circle-alert.js";
import { C as Circle_check } from "../../../../../../chunks/circle-check.js";
function _page($$payload, $$props) {
  push();
  let year, month, monthStart, monthEnd, startDay, daysInMonth, calendar, monthNames, monthlyTasks, totalMonthlyTasks, selectedTasks;
  let data = $$props["data"];
  let today = /* @__PURE__ */ new Date();
  let currentDate = new Date(today);
  let selectedDate = today.toISOString().slice(0, 10);
  let tasksByDate = {};
  if (data) {
    for (const t of data.tasks) {
      if (!t.dueDate) continue;
      const date = (typeof t.dueDate === "string" ? t.dueDate : t.dueDate?.toISOString?.())?.slice(0, 10);
      if (!tasksByDate[date]) tasksByDate[date] = [];
      tasksByDate[date].push({
        id: t.id,
        title: t.subject,
        description: t.description,
        type: "CRM",
        status: t.status,
        priority: t.priority
      });
    }
  }
  function formatDate(date) {
    return date.toISOString().slice(0, 10);
  }
  function isToday(date) {
    return date && formatDate(date) === today.toISOString().slice(0, 10);
  }
  function hasTasks(date) {
    return date && tasksByDate[formatDate(date)]?.length > 0;
  }
  function getPriorityIcon(priority) {
    switch (priority?.toLowerCase()) {
      case "high":
        return Circle_alert;
      case "medium":
        return Clock;
      default:
        return Circle;
    }
  }
  function getStatusIcon(status) {
    if (status?.toLowerCase() === "completed") return Circle_check;
    return Circle;
  }
  year = currentDate.getFullYear();
  month = currentDate.getMonth();
  monthStart = new Date(year, month, 1);
  monthEnd = new Date(year, month + 1, 0);
  startDay = monthStart.getDay();
  daysInMonth = monthEnd.getDate();
  calendar = (() => {
    let cal = [];
    for (let i = 0; i < startDay; i++) cal.push(null);
    for (let d = 1; d <= daysInMonth; d++) cal.push(new Date(Date.UTC(year, month, d)));
    while (cal.length % 7 !== 0) cal.push(null);
    return cal;
  })();
  monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];
  monthlyTasks = Object.keys(tasksByDate).filter((dateStr) => {
    const taskDate = new Date(dateStr);
    return taskDate.getFullYear() === year && taskDate.getMonth() === month;
  });
  totalMonthlyTasks = monthlyTasks.reduce(
    (total, dateStr) => {
      return total + tasksByDate[dateStr].length;
    },
    0
  );
  selectedTasks = tasksByDate[selectedDate] || [];
  const each_array = ensure_array_like([
    "Sun",
    "Mon",
    "Tue",
    "Wed",
    "Thu",
    "Fri",
    "Sat"
  ]);
  const each_array_1 = ensure_array_like(calendar);
  $$payload.out += `<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 p-4 sm:p-6 lg:p-8"><div class="max-w-7xl mx-auto"><div class="mb-8 text-center"><div class="flex items-center justify-center gap-3 mb-4">`;
  Calendar($$payload, {
    class: "w-8 h-8 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----> <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Task Calendar</h1></div> <p class="text-gray-600 dark:text-gray-300">Manage and track your tasks with ease</p></div> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-2"><div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden"><div class="bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 px-6 py-4"><div class="flex items-center justify-between"><div class="flex items-center gap-4"><button class="p-2 rounded-lg bg-white/10 hover:bg-white/20 dark:bg-white/10 dark:hover:bg-white/20 transition-colors">`;
  Chevron_left($$payload, { class: "w-5 h-5 text-white" });
  $$payload.out += `<!----></button> <h2 class="text-xl font-semibold text-white">${escape_html(monthNames[month])} ${escape_html(year)}</h2> <button class="p-2 rounded-lg bg-white/10 hover:bg-white/20 dark:bg-white/10 dark:hover:bg-white/20 transition-colors">`;
  Chevron_right($$payload, { class: "w-5 h-5 text-white" });
  $$payload.out += `<!----></button></div> <button class="px-4 py-2 bg-white/10 hover:bg-white/20 dark:bg-white/10 dark:hover:bg-white/20 text-white rounded-lg font-medium transition-colors">Today</button></div></div> <div class="grid grid-cols-7 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let day = each_array[$$index];
    $$payload.out += `<div class="p-4 text-center text-sm font-medium text-gray-600 dark:text-gray-300 border-r border-gray-200 dark:border-gray-600 last:border-r-0">${escape_html(day)}</div>`;
  }
  $$payload.out += `<!--]--></div> <div class="grid grid-cols-7"><!--[-->`;
  for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
    let date = each_array_1[i];
    if (date) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button${attr_class(`relative h-16 sm:h-20 p-2 border-r border-b border-gray-200 dark:border-gray-600 last:border-r-0 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors group ${stringify(formatDate(date) === selectedDate ? "bg-blue-600 dark:bg-blue-700 text-white" : "")} ${stringify(isToday(date) && formatDate(date) !== selectedDate ? "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300" : "")} ${stringify(!isToday(date) && formatDate(date) !== selectedDate ? "text-gray-900 dark:text-gray-100" : "")}`)}><div class="text-sm font-medium">${escape_html(date.getDate())}</div> `;
      if (hasTasks(date)) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div${attr_class(`absolute bottom-1 right-1 w-2 h-2 rounded-full ${stringify(formatDate(date) === selectedDate ? "bg-white" : "bg-blue-500 dark:bg-blue-400")}`)}></div> <div${attr_class(`absolute bottom-1 left-1 text-xs font-medium ${stringify(formatDate(date) === selectedDate ? "text-white" : "text-blue-600 dark:text-blue-400")}`)}>${escape_html(tasksByDate[formatDate(date)].length)}</div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></button>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="h-16 sm:h-20 border-r border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800"></div>`;
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></div></div></div> <div class="lg:col-span-1"><div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 h-fit"><div class="p-6 border-b border-gray-200 dark:border-gray-600"><h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Tasks for ${escape_html((/* @__PURE__ */ new Date(selectedDate + "T00:00:00")).toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric"
  }))}</h3> <div class="text-sm text-gray-600 dark:text-gray-300">${escape_html(selectedTasks.length)} task${escape_html(selectedTasks.length !== 1 ? "s" : "")}</div></div> <div class="p-6">`;
  if (selectedTasks.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_2 = ensure_array_like(selectedTasks);
    $$payload.out += `<div class="space-y-4"><!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
      let task = each_array_2[$$index_2];
      $$payload.out += `<a${attr("href", `/app/tasks/${stringify(task.id)}`)} class="block p-4 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-xl transition-colors border border-gray-200 dark:border-gray-600"><div class="flex items-start justify-between mb-2"><h4 class="font-medium text-gray-900 dark:text-white overflow-hidden text-ellipsis" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${escape_html(task.title)}</h4> <!---->`;
      getStatusIcon(task.status)?.($$payload, {
        class: "w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0 ml-2"
      });
      $$payload.out += `<!----></div> `;
      if (task.description) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-sm text-gray-600 dark:text-gray-300 mb-3 overflow-hidden text-ellipsis" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${escape_html(task.description)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="flex items-center justify-between"><div class="flex items-center gap-2"><span class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-lg">${escape_html(task.type)}</span> <span${attr_class(`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-lg ${stringify(task.priority === "High" ? "bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300" : task.priority === "Medium" ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300" : "bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300")}`)}><!---->`;
      getPriorityIcon(task.priority)?.($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> ${escape_html(task.priority)}</span></div> <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">${escape_html(task.status)}</span></div></a>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="text-center py-12">`;
    Calendar($$payload, {
      class: "w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4"
    });
    $$payload.out += `<!----> <p class="text-gray-500 dark:text-gray-400 text-sm">No tasks scheduled for this date</p> <p class="text-gray-400 dark:text-gray-500 text-xs mt-1">Select a different date to view tasks</p></div>`;
  }
  $$payload.out += `<!--]--></div></div> <div class="mt-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 p-6"><h4 class="font-semibold text-gray-900 dark:text-white mb-4">This Month</h4> <div class="space-y-3"><div class="flex justify-between items-center"><span class="text-sm text-gray-600 dark:text-gray-300">Total Tasks</span> <span class="font-medium text-gray-900 dark:text-white">${escape_html(totalMonthlyTasks)}</span></div> <div class="flex justify-between items-center"><span class="text-sm text-gray-600 dark:text-gray-300">Days with Tasks</span> <span class="font-medium text-gray-900 dark:text-white">${escape_html(monthlyTasks.length)}</span></div></div></div></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
