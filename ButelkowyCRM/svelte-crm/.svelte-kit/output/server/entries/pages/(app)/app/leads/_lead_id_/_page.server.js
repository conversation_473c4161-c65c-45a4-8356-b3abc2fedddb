import { e as error, f as fail } from "../../../../../../chunks/index.js";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
const prisma = new PrismaClient();
const commentSchema = z.object({
  comment: z.string().min(1, "Comment cannot be empty").max(1e3, "Comment too long").trim()
});
async function load({ params, locals }) {
  const lead_id = params.lead_id;
  const org = locals.org;
  const lead = await prisma.lead.findUnique({
    where: { id: lead_id, organizationId: org.id },
    include: {
      owner: true,
      tasks: {
        orderBy: { createdAt: "desc" }
      },
      events: {
        orderBy: { startDate: "asc" }
      },
      comments: {
        include: {
          author: true
        },
        orderBy: { createdAt: "desc" }
      },
      contact: true
    }
  });
  if (!lead) {
    throw error(404, "Lead not found");
  }
  return {
    lead
  };
}
const actions = {
  convert: async ({ params, locals }) => {
    const lead_id = params.lead_id;
    locals.user;
    const org = locals.org;
    try {
      const lead = await prisma.lead.findUnique({
        where: { id: lead_id, organizationId: org.id },
        include: {
          organization: true,
          owner: true
        }
      });
      if (!lead) {
        return fail(404, { status: "error", message: "Lead not found" });
      }
      if (lead.status === "CONVERTED") {
        return { status: "success", message: "Lead already converted" };
      }
      const contact = await prisma.contact.create({
        data: {
          firstName: lead.firstName,
          lastName: lead.lastName,
          email: lead.email,
          phone: lead.phone,
          title: lead.title,
          description: lead.description,
          owner: { connect: { id: lead.ownerId } },
          organization: { connect: { id: lead.organizationId } }
        }
      });
      let accountId = null;
      let account = null;
      if (lead.company) {
        account = await prisma.account.create({
          data: {
            name: lead.company,
            industry: lead.industry,
            owner: { connect: { id: lead.ownerId } },
            organization: { connect: { id: lead.organizationId } }
          }
        });
        accountId = account.id;
        await prisma.accountContactRelationship.create({
          data: {
            account: { connect: { id: account.id } },
            contact: { connect: { id: contact.id } },
            isPrimary: true,
            role: "Primary Contact"
          }
        });
      }
      const opportunityData = {
        name: `${lead.company || lead.firstName + " " + lead.lastName} Opportunity`,
        stage: "PROSPECTING",
        amount: 0,
        closeDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3),
        contacts: { connect: { id: contact.id } },
        owner: { connect: { id: lead.ownerId } },
        organization: { connect: { id: lead.organizationId } }
      };
      if (!accountId) {
        const placeholderAccount = await prisma.account.create({
          data: {
            name: `${lead.firstName} ${lead.lastName} Account`,
            owner: { connect: { id: lead.ownerId } },
            organization: { connect: { id: lead.organizationId } }
          }
        });
        accountId = placeholderAccount.id;
        account = placeholderAccount;
        await prisma.accountContactRelationship.create({
          data: {
            account: { connect: { id: placeholderAccount.id } },
            contact: { connect: { id: contact.id } },
            isPrimary: true,
            role: "Primary Contact"
          }
        });
      }
      opportunityData.account = { connect: { id: accountId } };
      const opportunity = await prisma.opportunity.create({
        data: opportunityData
      });
      await prisma.lead.update({
        where: { id: lead_id },
        data: {
          status: "CONVERTED",
          isConverted: true,
          convertedAt: /* @__PURE__ */ new Date(),
          convertedContactId: contact.id,
          convertedAccountId: accountId,
          convertedOpportunityId: opportunity.id,
          contact: { connect: { id: contact.id } }
        }
      });
      return {
        status: "success",
        message: "Lead successfully converted",
        contact,
        account,
        opportunity
      };
    } catch (err) {
      console.error("Error converting lead:", err.message);
      return fail(500, { status: "error", message: "Failed to convert lead" });
    }
  },
  addComment: async ({ params, request, locals }) => {
    const lead_id = params.lead_id;
    const user = locals.user;
    const org = locals.org;
    const data = await request.formData();
    const comment = data.get("comment");
    try {
      const validatedComment = commentSchema.parse({ comment });
      const lead = await prisma.lead.findUnique({
        where: { id: lead_id, organizationId: org.id },
        select: { organizationId: true }
      });
      if (!lead) {
        return fail(404, { status: "error", message: "Lead not found" });
      }
      await prisma.comment.create({
        data: {
          body: validatedComment.comment,
          lead: { connect: { id: lead_id } },
          author: { connect: { id: user.id } },
          organization: { connect: { id: lead.organizationId } }
        }
      });
      const updatedLead = await prisma.lead.findUnique({
        where: { id: lead_id },
        include: {
          comments: { include: { author: true }, orderBy: { createdAt: "desc" } }
        }
      });
      return {
        status: "success",
        message: "Comment added successfully",
        comments: updatedLead?.comments || []
      };
    } catch (err) {
      console.error("Error adding comment:", err.message);
      if (err instanceof z.ZodError) {
        return fail(400, { status: "error", message: err.errors[0].message });
      }
      return fail(500, { status: "error", message: "Failed to add comment" });
    }
  }
};
export {
  actions,
  load
};
