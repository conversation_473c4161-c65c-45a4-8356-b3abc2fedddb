import { J as attr, D as escape_html, M as bind_props, C as pop, A as push } from "../../../../../../../chunks/index2.js";
import "../../../../../../../chunks/client.js";
import { A as Arrow_left } from "../../../../../../../chunks/arrow-left.js";
import { U as User } from "../../../../../../../chunks/user.js";
import { M as Mail } from "../../../../../../../chunks/mail.js";
import { P as Phone } from "../../../../../../../chunks/phone.js";
import { M as Map_pin } from "../../../../../../../chunks/map-pin.js";
import { B as Building } from "../../../../../../../chunks/building.js";
import { S as Star } from "../../../../../../../chunks/star.js";
import { F as File_text } from "../../../../../../../chunks/file-text.js";
import { S as Save } from "../../../../../../../chunks/save.js";
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let contact = data.contact;
  let account = data.account;
  let isPrimary = data.isPrimary;
  let role = data.role;
  let firstName = contact.firstName;
  let lastName = contact.lastName;
  let email = contact.email || "";
  let phone = contact.phone || "";
  let title = contact.title || "";
  let department = contact.department || "";
  let street = contact.street || "";
  let city = contact.city || "";
  let state = contact.state || "";
  let postalCode = contact.postalCode || "";
  let country = contact.country || "";
  let description = contact.description || "";
  let submitting = false;
  $$payload.out += `<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-6"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="mb-8"><div class="flex items-center gap-4 mb-4"><button class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">`;
  Arrow_left($$payload, { class: "w-5 h-5" });
  $$payload.out += `<!----></button> <div><h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Contact</h1> <p class="text-gray-600 dark:text-gray-400 mt-1">Update contact information and details</p></div></div></div> <form class="space-y-8"><div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center gap-3"><div class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">`;
  User($$payload, {
    class: "w-5 h-5 text-blue-600 dark:text-blue-400"
  });
  $$payload.out += `<!----></div> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="firstName">First Name *</label> <input id="firstName" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", firstName)} required placeholder="Enter first name"></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="lastName">Last Name *</label> <input id="lastName" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", lastName)} required placeholder="Enter last name"></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="title">Job Title</label> <input id="title" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", title)} placeholder="e.g. Marketing Director"></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="department">Department</label> <input id="department" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", department)} placeholder="e.g. Marketing"></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center gap-3"><div class="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">`;
  Mail($$payload, {
    class: "w-5 h-5 text-green-600 dark:text-green-400"
  });
  $$payload.out += `<!----></div> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Contact Information</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="email">Email Address</label> <div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">`;
  Mail($$payload, { class: "w-5 h-5 text-gray-400" });
  $$payload.out += `<!----></div> <input id="email" type="email" class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", email)} placeholder="<EMAIL>"></div></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="phone">Phone Number</label> <div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">`;
  Phone($$payload, { class: "w-5 h-5 text-gray-400" });
  $$payload.out += `<!----></div> <input id="phone" type="tel" class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", phone)} placeholder="+****************"></div></div></div></div></div> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center gap-3"><div class="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">`;
  Map_pin($$payload, {
    class: "w-5 h-5 text-purple-600 dark:text-purple-400"
  });
  $$payload.out += `<!----></div> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Address Information</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 gap-6"><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="street">Street Address</label> <input id="street" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", street)} placeholder="123 Main Street"></div> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="city">City</label> <input id="city" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", city)} placeholder="San Francisco"></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="state">State/Province</label> <input id="state" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", state)} placeholder="CA"></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="postalCode">Postal Code</label> <input id="postalCode" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", postalCode)} placeholder="94102"></div></div> <div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="country">Country</label> <input id="country" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"${attr("value", country)} placeholder="United States"></div></div></div></div> `;
  if (account) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center gap-3"><div class="p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">`;
    Building($$payload, {
      class: "w-5 h-5 text-orange-600 dark:text-orange-400"
    });
    $$payload.out += `<!----></div> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Account Relationship</h2></div></div> <div class="p-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Account</div> <div class="text-gray-900 dark:text-white font-medium">${escape_html(account.name)}</div></div> `;
    if (role) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div><div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role</div> <div class="text-gray-900 dark:text-white">${escape_html(role)}</div></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (isPrimary) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="md:col-span-2"><div class="flex items-center gap-2 text-sm">`;
      Star($$payload, { class: "w-4 h-4 text-yellow-500" });
      $$payload.out += `<!----> <span class="text-gray-700 dark:text-gray-300 font-medium">Primary Contact</span></div></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"><div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700"><div class="flex items-center gap-3"><div class="p-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">`;
  File_text($$payload, {
    class: "w-5 h-5 text-indigo-600 dark:text-indigo-400"
  });
  $$payload.out += `<!----></div> <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Additional Information</h2></div></div> <div class="p-6"><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="description">Notes &amp; Description</label> <textarea id="description" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400" rows="4" placeholder="Add any additional notes or important information about this contact...">`;
  const $$body = escape_html(description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700"><button type="button" class="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 transition-colors">Cancel</button> <button type="submit"${attr("disabled", submitting, true)} class="px-6 py-3 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 dark:bg-blue-600 dark:hover:bg-blue-700 dark:disabled:bg-blue-500 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors flex items-center gap-2">`;
  {
    $$payload.out += "<!--[!-->";
    Save($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Save Changes`;
  }
  $$payload.out += `<!--]--></button></div></form></div></div>`;
  bind_props($$props, { data });
  pop();
}
export {
  _page as default
};
