import { p as prisma } from "../../../../../../chunks/prisma.js";
import { f as fail, r as redirect } from "../../../../../../chunks/index.js";
async function load({ locals, url }) {
  const org = locals.org;
  const preSelectedAccountId = url.searchParams.get("accountId");
  const accounts = await prisma.account.findMany(
    {
      where: { organizationId: org.id },
      select: { id: true, name: true }
    }
  );
  const users = await prisma.userOrganization.findMany({
    where: { organizationId: org.id },
    select: {
      user: {
        select: {
          id: true,
          name: true
        }
      }
    }
  });
  return { accounts, users, preSelectedAccountId };
}
const actions = {
  create: async ({ request, locals }) => {
    const org = locals.org;
    const form = await request.formData();
    const subject = form.get("title")?.toString().trim();
    const description = form.get("description")?.toString().trim();
    const accountId = form.get("accountId")?.toString();
    const dueDate = form.get("dueDate") ? new Date(form.get("dueDate")) : null;
    const priority = form.get("priority")?.toString() || "Medium";
    const ownerId = form.get("assignedId")?.toString();
    if (!subject || !accountId || !ownerId) {
      return fail(400, { error: "Missing required fields." });
    }
    const isValidOwner = await prisma.userOrganization.findFirst({
      where: {
        userId: ownerId,
        organizationId: org.id
      }
    });
    if (!isValidOwner) {
      return fail(400, { error: "Invalid owner ID." });
    }
    const newCase = await prisma.case.create({
      data: {
        subject,
        description,
        accountId,
        dueDate,
        priority,
        ownerId,
        organizationId: locals.org.id
      }
    });
    throw redirect(303, `/app/cases/${newCase.id}`);
  }
};
export {
  actions,
  load
};
