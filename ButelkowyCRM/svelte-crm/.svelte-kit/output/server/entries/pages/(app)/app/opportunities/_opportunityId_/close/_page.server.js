import { e as error, f as fail, r as redirect } from "../../../../../../../chunks/index.js";
import { p as prisma } from "../../../../../../../chunks/prisma.js";
async function load({ params, locals }) {
  if (!locals.org?.id) {
    throw error(403, "Organization access required");
  }
  const opportunity = await prisma.opportunity.findFirst({
    where: {
      id: params.opportunityId,
      organizationId: locals.org.id
    },
    include: {
      account: {
        select: { id: true, name: true }
      }
    }
  });
  if (!opportunity) {
    throw error(404, "Opportunity not found");
  }
  return { opportunity };
}
const actions = {
  default: async ({ request, params, locals }) => {
    if (!locals.org?.id) {
      return fail(403, { error: "Organization access required" });
    }
    const formData = await request.formData();
    const status = formData.get("status");
    const closeDate = formData.get("closeDate");
    const closeReason = formData.get("closeReason");
    if (!status || !closeDate) {
      return fail(400, { error: "Status and close date are required" });
    }
    if (!["CLOSED_WON", "CLOSED_LOST"].includes(status)) {
      return fail(400, { error: "Invalid status selected" });
    }
    try {
      const opportunity = await prisma.opportunity.findFirst({
        where: {
          id: params.opportunityId,
          organizationId: locals.org.id
        },
        include: {
          account: { select: { id: true } }
        }
      });
      if (!opportunity) {
        return fail(404, { error: "Opportunity not found" });
      }
      const updatedOpportunity = await prisma.opportunity.update({
        where: { id: params.opportunityId },
        data: {
          stage: status,
          // CLOSED_WON or CLOSED_LOST
          status: status === "CLOSED_WON" ? "SUCCESS" : "FAILED",
          closeDate: new Date(closeDate),
          description: closeReason ? opportunity.description ? `${opportunity.description}

Close Reason: ${closeReason}` : `Close Reason: ${closeReason}` : opportunity.description,
          updatedAt: /* @__PURE__ */ new Date()
        }
      });
      await prisma.auditLog.create({
        data: {
          action: "UPDATE",
          entityType: "Opportunity",
          entityId: opportunity.id,
          description: `Opportunity closed with status: ${status}`,
          newValues: {
            stage: status,
            status: status === "CLOSED_WON" ? "SUCCESS" : "FAILED",
            closeDate,
            closeReason
          },
          userId: locals.user.id,
          organizationId: locals.org.id
        }
      });
      throw redirect(303, `/app/opportunities/${opportunity.id}`);
    } catch (err) {
      console.error("Error closing opportunity:", err);
      if (err.status === 303) {
        throw err;
      }
      return fail(500, { error: "Failed to close opportunity. Please try again." });
    }
  }
};
export {
  actions,
  load
};
