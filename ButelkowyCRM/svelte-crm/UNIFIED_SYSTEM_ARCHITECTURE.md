# Unified System Architecture
## Complete HVAC CRM Ecosystem Integration

### Executive Summary

Ten dokument przedstawia kompletną architekturę zunifikowanego systemu HVAC CRM, integrującego wszystkie komponenty: Svelte CRM, Python Mixer, Gobeklitepe, GoSpine i systemy AI. Architektura zapewnia seamless data flow, real-time synchronization i scalable performance dla complete business ecosystem.

### System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        SC[Svelte CRM<br/>Port 3000]
        GI[Gradio Interface<br/>Port 7860]
        CP[Customer Portal<br/>Port 3001]
    end
    
    subgraph "API Gateway Layer"
        GS[GoSpine Backend<br/>Port 8080]
        PM[Python Mixer<br/>Port 8000]
        GB[Gobeklitepe<br/>Port 8082]
    end
    
    subgraph "AI Services Layer"
        BV3[Bielik V3<br/>Port 8877]
        G4B[Gemma3-4b<br/>Port 8878]
        NMO[NVIDIA NeMo<br/>STT Service]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL<br/>Port 5432)]
        WV[(Weaviate V4<br/>Port 8080)]
        RD[(Redis<br/>Port 6379)]
        MN[(MinIO<br/>Port 9000)]
    end
    
    subgraph "External Services"
        EM[Email Servers<br/>IMAP/SMTP]
        OL[Outlook Calendar<br/>Graph API]
        ST[Stripe Payments<br/>API]
    end
    
    SC --> GS
    GI --> PM
    CP --> GS
    
    GS --> PG
    GS --> RD
    PM --> WV
    PM --> BV3
    PM --> G4B
    PM --> NMO
    GB --> WV
    GB --> PG
    
    PM --> EM
    GS --> OL
    GS --> ST
    
    SC -.->|Real-time Sync| PM
    PM -.->|Intelligence Updates| GB
    GB -.->|Customer Insights| SC
```

### Component Integration Matrix

| Component | Svelte CRM | Python Mixer | Gobeklitepe | GoSpine | AI Services |
|-----------|------------|--------------|-------------|---------|-------------|
| **Svelte CRM** | ✅ Core | 🔄 Real-time | 📊 Insights | 🔗 API | 🤖 Features |
| **Python Mixer** | 📤 Updates | ✅ Core | 🧠 Intelligence | 📡 Bridge | 🎯 Processing |
| **Gobeklitepe** | 💡 Analytics | 📥 Data | ✅ Core | 🔍 Search | 🧮 Models |
| **GoSpine** | 🌐 Backend | 🔌 Integration | 📋 Queries | ✅ Core | 🚀 Orchestration |
| **AI Services** | 🎨 UI/UX | 🔬 Analysis | 🎓 Learning | ⚡ Performance | ✅ Core |

### Data Flow Architecture

#### 1. Email Processing Pipeline

```python
# unified_architecture/email_pipeline.py
class UnifiedEmailPipeline:
    def __init__(self):
        self.python_mixer = PythonMixerClient("http://python-mixer:8000")
        self.gobeklitepe = GobeklitepeClient("http://gobeklitepe:8082")
        self.svelte_crm = SvelteCRMClient("http://svelte-crm:3000")
        self.gospine = GoSpineClient("http://gospine:8080")
        
    async def process_email_flow(self, email_data: Dict):
        """Complete email processing flow"""
        
        # Step 1: Python Mixer - Email Intelligence
        email_analysis = await self.python_mixer.analyze_email(email_data)
        
        # Step 2: Gobeklitepe - Semantic Enhancement
        semantic_insights = await self.gobeklitepe.enhance_analysis(email_analysis)
        
        # Step 3: GoSpine - Business Logic Processing
        business_actions = await self.gospine.process_business_logic(semantic_insights)
        
        # Step 4: Svelte CRM - UI Updates
        ui_updates = await self.svelte_crm.update_interface(business_actions)
        
        # Step 5: Real-time Synchronization
        await self._sync_all_systems(email_analysis, semantic_insights, business_actions)
        
        return {
            'email_processed': True,
            'customer_updated': ui_updates['customer_updated'],
            'actions_created': business_actions['actions'],
            'insights_generated': semantic_insights['insights']
        }
```

#### 2. Customer Profile Unification

```python
# unified_architecture/customer_unification.py
class CustomerProfileUnifier:
    def __init__(self):
        self.data_sources = {
            'crm': SvelteCRMClient(),
            'intelligence': PythonMixerClient(),
            'semantic': GobeklitepeClient(),
            'backend': GoSpineClient()
        }
        
    async def create_unified_profile(self, customer_id: str) -> Dict:
        """Tworzenie zunifikowanego profilu klienta"""
        
        # Parallel data collection from all sources
        tasks = [
            self.data_sources['crm'].get_customer_data(customer_id),
            self.data_sources['intelligence'].get_email_analysis(customer_id),
            self.data_sources['semantic'].get_customer_intelligence(customer_id),
            self.data_sources['backend'].get_service_history(customer_id)
        ]
        
        crm_data, email_data, semantic_data, service_data = await asyncio.gather(*tasks)
        
        # Merge and enrich data
        unified_profile = {
            'basic_info': crm_data['customer'],
            'communication_intelligence': email_data['analysis'],
            'semantic_insights': semantic_data['intelligence'],
            'service_history': service_data['history'],
            'ai_predictions': semantic_data['predictions'],
            'health_score': semantic_data['health_score'],
            'lifetime_value': semantic_data['ltv'],
            'churn_probability': semantic_data['churn_risk']
        }
        
        # Store unified profile in all systems
        await self._distribute_unified_profile(customer_id, unified_profile)
        
        return unified_profile
    
    async def _distribute_unified_profile(self, customer_id: str, profile: Dict):
        """Dystrybucja zunifikowanego profilu do wszystkich systemów"""
        
        # Update Svelte CRM
        await self.data_sources['crm'].update_customer_profile(customer_id, {
            'ai_insights': profile['semantic_insights'],
            'health_score': profile['health_score'],
            'predictions': profile['ai_predictions']
        })
        
        # Update Gobeklitepe knowledge base
        await self.data_sources['semantic'].store_customer_intelligence(customer_id, profile)
        
        # Update GoSpine backend
        await self.data_sources['backend'].sync_customer_data(customer_id, profile)
```

### Real-time Synchronization

#### WebSocket Event System

```python
# unified_architecture/realtime_sync.py
import asyncio
import websockets
import json
from typing import Dict, List

class RealtimeSynchronizer:
    def __init__(self):
        self.connections = {}
        self.event_queue = asyncio.Queue()
        
    async def start_sync_server(self):
        """Start WebSocket server for real-time synchronization"""
        
        async def handle_connection(websocket, path):
            service_name = await self._authenticate_service(websocket)
            self.connections[service_name] = websocket
            
            try:
                await self._handle_service_messages(service_name, websocket)
            finally:
                del self.connections[service_name]
        
        start_server = websockets.serve(handle_connection, "0.0.0.0", 9001)
        await start_server
        
        # Start event processor
        asyncio.create_task(self._process_events())
    
    async def broadcast_event(self, event_type: str, data: Dict):
        """Broadcast event to all connected services"""
        
        event = {
            'type': event_type,
            'data': data,
            'timestamp': asyncio.get_event_loop().time()
        }
        
        await self.event_queue.put(event)
    
    async def _process_events(self):
        """Process events from queue and broadcast to services"""
        
        while True:
            event = await self.event_queue.get()
            
            # Determine target services based on event type
            targets = self._get_event_targets(event['type'])
            
            # Broadcast to target services
            for service_name in targets:
                if service_name in self.connections:
                    try:
                        await self.connections[service_name].send(json.dumps(event))
                    except websockets.exceptions.ConnectionClosed:
                        del self.connections[service_name]
    
    def _get_event_targets(self, event_type: str) -> List[str]:
        """Determine which services should receive the event"""
        
        event_routing = {
            'customer_updated': ['svelte-crm', 'gobeklitepe', 'gospine'],
            'email_processed': ['svelte-crm', 'gobeklitepe'],
            'service_order_created': ['svelte-crm', 'python-mixer', 'gospine'],
            'equipment_alert': ['svelte-crm', 'gobeklitepe', 'python-mixer'],
            'ai_insight_generated': ['svelte-crm', 'gospine']
        }
        
        return event_routing.get(event_type, [])
```

### Performance Optimization

#### Caching Strategy

```python
# unified_architecture/caching_strategy.py
import redis
import json
from typing import Optional, Dict, Any

class UnifiedCachingStrategy:
    def __init__(self):
        self.redis_client = redis.Redis(host='redis', port=6379, decode_responses=True)
        self.cache_ttl = {
            'customer_profile': 3600,  # 1 hour
            'email_analysis': 1800,    # 30 minutes
            'semantic_insights': 7200, # 2 hours
            'equipment_data': 1800,    # 30 minutes
            'ai_predictions': 14400    # 4 hours
        }
    
    async def get_cached_data(self, cache_type: str, key: str) -> Optional[Dict]:
        """Get data from cache"""
        
        cache_key = f"{cache_type}:{key}"
        cached_data = self.redis_client.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        
        return None
    
    async def set_cached_data(self, cache_type: str, key: str, data: Dict):
        """Set data in cache with appropriate TTL"""
        
        cache_key = f"{cache_type}:{key}"
        ttl = self.cache_ttl.get(cache_type, 3600)
        
        self.redis_client.setex(
            cache_key,
            ttl,
            json.dumps(data, default=str)
        )
    
    async def invalidate_cache(self, cache_type: str, key: str = None):
        """Invalidate cache entries"""
        
        if key:
            cache_key = f"{cache_type}:{key}"
            self.redis_client.delete(cache_key)
        else:
            # Invalidate all entries of this type
            pattern = f"{cache_type}:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
```

### Monitoring & Observability

#### Health Check System

```python
# unified_architecture/health_monitoring.py
class SystemHealthMonitor:
    def __init__(self):
        self.services = {
            'svelte-crm': 'http://svelte-crm:3000/health',
            'python-mixer': 'http://python-mixer:8000/health',
            'gobeklitepe': 'http://gobeklitepe:8082/health',
            'gospine': 'http://gospine:8080/health',
            'weaviate': 'http://weaviate:8080/v1/.well-known/ready',
            'redis': 'redis://redis:6379',
            'postgres': '************************************/hvac_crm'
        }
        
    async def check_system_health(self) -> Dict:
        """Comprehensive system health check"""
        
        health_status = {}
        
        for service_name, endpoint in self.services.items():
            try:
                if service_name == 'redis':
                    status = await self._check_redis_health()
                elif service_name == 'postgres':
                    status = await self._check_postgres_health()
                else:
                    status = await self._check_http_health(endpoint)
                
                health_status[service_name] = {
                    'status': 'healthy' if status else 'unhealthy',
                    'response_time': status.get('response_time', 0),
                    'last_check': asyncio.get_event_loop().time()
                }
                
            except Exception as e:
                health_status[service_name] = {
                    'status': 'error',
                    'error': str(e),
                    'last_check': asyncio.get_event_loop().time()
                }
        
        # Calculate overall system health
        healthy_services = sum(1 for s in health_status.values() if s['status'] == 'healthy')
        total_services = len(health_status)
        overall_health = (healthy_services / total_services) * 100
        
        return {
            'overall_health': overall_health,
            'services': health_status,
            'critical_issues': self._identify_critical_issues(health_status)
        }
```

### Deployment Configuration

#### Complete Docker Compose

```yaml
# docker-compose.unified.yml
version: '3.8'

services:
  # Frontend Services
  svelte-crm:
    build: ./ButelkowyCRM/svelte-crm
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=************************************/svelte_crm
      - GOSPINE_API_URL=http://gospine:8080
      - PYTHON_MIXER_URL=http://python-mixer:8000
      - GOBEKLITEPE_URL=http://gobeklitepe:8082
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
      - gospine
    networks:
      - hvac-network

  # AI Processing Services
  python-mixer:
    build: ./python-mixer
    ports:
      - "7860:7860"  # Gradio
      - "8000:8000"  # FastAPI
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - BIELIK_API_URL=http://bielik:8877
      - GEMMA_API_URL=http://gemma:8878
      - WEAVIATE_URL=http://weaviate:8080
      - SVELTE_CRM_URL=http://svelte-crm:3000
      - GOSPINE_URL=http://gospine:8080
    volumes:
      - ./data/python-mixer:/app/data
      - ./models:/app/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - hvac-network

  gobeklitepe:
    build: ./gobeklitepe
    ports:
      - "8082:8082"
    environment:
      - WEAVIATE_URL=http://weaviate:8080
      - PYTHON_MIXER_URL=http://python-mixer:8000
      - SVELTE_CRM_URL=http://svelte-crm:3000
      - GOSPINE_URL=http://gospine:8080
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./data/gobeklitepe:/app/data
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 40G
    networks:
      - hvac-network

  # Backend Services
  gospine:
    build: ./GoBackend-Kratos-fresh
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=************************************/gospine
      - REDIS_URL=redis://redis:6379
      - PYTHON_MIXER_URL=http://python-mixer:8000
      - GOBEKLITEPE_URL=http://gobeklitepe:8082
    depends_on:
      - postgres
      - redis
    networks:
      - hvac-network

  # AI Model Services
  bielik:
    image: bielik-v3:latest
    ports:
      - "8877:8877"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - hvac-network

  gemma:
    image: gemma3-4b:latest
    ports:
      - "8878:8878"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - hvac-network

  # Data Services
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: hvac_unified
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - hvac-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hvac-network

  weaviate:
    image: semitechnologies/weaviate:1.22.4
    ports:
      - "8080:8080"
      - "50051:50051"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'text2vec-transformers'
      ENABLE_MODULES: 'text2vec-transformers'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - hvac-network

  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - hvac-network

  # Monitoring
  realtime-sync:
    build: ./unified-architecture/realtime-sync
    ports:
      - "9001:9001"  # WebSocket
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - hvac-network

volumes:
  postgres_data:
  redis_data:
  weaviate_data:
  minio_data:

networks:
  hvac-network:
    driver: bridge
```

### Performance Targets

- **System Response Time**: < 200ms for API calls
- **Real-time Sync**: < 100ms event propagation
- **Email Processing**: < 30s per email with attachments
- **Customer Profile Generation**: < 5s for complete profile
- **AI Predictions**: < 2s for churn/maintenance predictions
- **System Uptime**: 99.9% availability
- **Concurrent Users**: 1000+ simultaneous users
- **Data Throughput**: 10,000+ operations per second

### Success Metrics

- **Integration Completeness**: 100% data flow between all components
- **Performance Efficiency**: 50% improvement in processing speed
- **Data Accuracy**: 95%+ accuracy in AI predictions and analysis
- **System Reliability**: < 0.1% error rate across all services
- **User Experience**: < 2s page load times in Svelte CRM
- **Business Impact**: 40% improvement in customer service efficiency

### Next Steps

1. **Infrastructure Setup**: Deploy complete Docker environment
2. **Service Integration**: Implement real-time synchronization
3. **Performance Testing**: Load testing with production data volumes
4. **Security Hardening**: Implement authentication and authorization
5. **Monitoring Setup**: Deploy comprehensive observability stack
6. **Production Deployment**: Gradual rollout with blue-green strategy
